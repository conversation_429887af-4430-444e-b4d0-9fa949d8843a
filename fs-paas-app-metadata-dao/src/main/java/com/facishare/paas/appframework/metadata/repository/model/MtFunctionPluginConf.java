package com.facishare.paas.appframework.metadata.repository.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.UdobjSectionConfig;
import com.facishare.paas.appframework.metadata.layout.I18nInfo;
import com.facishare.paas.appframework.metadata.repository.annotation.*;
import com.facishare.paas.appframework.metadata.repository.fieldhandler.ObjectFieldType;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.constraints.Size;
import java.util.*;

import static com.facishare.paas.metadata.api.IObjectData.SYS_MODIFIED_TIME;

/**
 * APL插件配置对应内部对象
 * 界面上对应 对象管理 - 页面扩展
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Entity(apiName = MtFunctionPluginConf.FUNCTION_PLUGIN_CONF_API_NAME)
// https://wiki.firstshare.cn/pages/viewpage.action?pageId=599066190
public class MtFunctionPluginConf extends BaseEntity {

    public static final String TABLE_NAME = "mt_function_plugin_conf";
    public static final String TAB = "extension";

    public static final String FUNCTION_PLUGIN_CONF_API_NAME = "MtFunctionPluginObj";

    public static final String REF_OBJECT_API_NAME = "ref_object_api_name";
    public static final String PLUGIN_PROVIDER = "plugin_provider";
    public static final String METHODS = "methods";
    public static final String MODULE_NAME = "module_name";
    public static final String MODULE_TYPE = "module_type";
    public static final String FUNCTION_API_NAME = "function_api_name";
    public static final String NAME = "name";
    public static final String DESCRIPTION = "description";
    public static final String API_NAME = "api_name";
    public static final String I18N_INFO = "i18n_info";
    public static final String IS_ACTIVE = "is_active";
    public static final String AGENT_TYPE = "agent_type";
    private static final Logger log = LoggerFactory.getLogger(MtFunctionPluginConf.class);

    // 不具有参考意义
    @JsonProperty(SYS_MODIFIED_TIME)
    @JSONField(name = SYS_MODIFIED_TIME)
    @DateTimeField(field = @ObjectField(apiName = IObjectData.SYS_MODIFIED_TIME))
    protected Long sysModifiedTime;

    @TextField(field = @ObjectField(apiName = REF_OBJECT_API_NAME), maxLength = 100)
    @JsonProperty(REF_OBJECT_API_NAME)
    @JSONField(name = REF_OBJECT_API_NAME)
    @NonNull
    private String refObjectApiName;

    @TextField(field = @ObjectField(apiName = PLUGIN_PROVIDER), maxLength = 32)
    @JsonProperty(PLUGIN_PROVIDER)
    @JSONField(name = PLUGIN_PROVIDER)
    @NonNull
    private String pluginProvider;

    @LongTextField(field = @ObjectField(apiName = METHODS), fieldType = ObjectFieldType.JSON, maxLength = 500)
    @JsonProperty(METHODS)
    @JSONField(name = METHODS)
    @NonNull
    @Size(min = 1)  // 执行时机至少有一个
    private List<String> methods;

    @TextField(field = @ObjectField(apiName = MODULE_NAME), maxLength = 64)
    @JsonProperty(MODULE_NAME)
    @JSONField(name = MODULE_NAME)
    @NonNull
    private String moduleName;

    @LongTextField(field = @ObjectField(apiName = MODULE_TYPE), fieldType = ObjectFieldType.JSON, maxLength = 500)
    @JsonProperty(MODULE_TYPE)
    @JSONField(name = MODULE_TYPE)
    private List<String> moduleType;

    @TextField(field = @ObjectField(apiName = FUNCTION_API_NAME), maxLength = 64)
    @JsonProperty(FUNCTION_API_NAME)
    @JSONField(name = FUNCTION_API_NAME)
    @NonNull
    private String functionApiName;

    @TextField(field = @ObjectField(apiName = NAME), maxLength = 100)
    @JsonProperty(NAME)
    @JSONField(name = NAME)
    @NonNull
    private String name;

    @TextField(field = @ObjectField(apiName = DESCRIPTION), maxLength = 2000)
    @JsonProperty(DESCRIPTION)
    @JSONField(name = DESCRIPTION)
    private String description;

    @TextField(field = @ObjectField(apiName = API_NAME), maxLength = 64)
    @JsonProperty(API_NAME)
    @JSONField(name = API_NAME)
    @NonNull
    private String apiName;

    @LongTextField(field = @ObjectField(apiName = I18N_INFO), fieldType = ObjectFieldType.JSON, maxLength = 150000)
    @JsonProperty(I18N_INFO)
    @JSONField(name = I18N_INFO)
    private List<I18nInfo> i18nInfo;

    @TrueOrFalseField(field = @ObjectField(apiName = IS_ACTIVE))
    @JsonProperty(IS_ACTIVE)
    @JSONField(name = IS_ACTIVE)
    private Boolean isActive;

    @LongTextField(field = @ObjectField(apiName = AGENT_TYPE), fieldType = ObjectFieldType.JSON, maxLength = 500)
    @JsonProperty(AGENT_TYPE)
    @JSONField(name = AGENT_TYPE)
    @NonNull    // size = 1 与 全集 等价
    private List<String> agentTypes;

    public boolean show() {
        return CollectionUtils.emptyIfNull(UdobjSectionConfig.getFunctionPluginConfig().getShowModuleName())
                .contains(this.getModuleName());
    }

    public boolean enabled() {
        return BooleanUtils.isNotFalse(this.getIsActive());
    }

    public String UniqueId() {
        return String.join("_", tenantId, refObjectApiName, moduleName);
    }

    public boolean agentTypeMatch(String agentType) {
        if (StringUtils.isBlank(agentType)) {   // 只有 ListHeader 会传递这个参数, 所以空值默认 true
            return true;
        }
        return CollectionUtils.isEmpty(this.agentTypes) || CollectionUtils.emptyIfNull(this.agentTypes).contains(agentType);
    }

    @Getter
    @Data
    @AllArgsConstructor
    public static class RuleConfig {
        private final String pluginProvider;
        private final Map<String, ModuleConfig> moduleNameConfig; // Key: moduleName, Value: ModuleConfig

        @Getter
        @Data
        @AllArgsConstructor
        public static class ModuleConfig {
            private final Set<String> methods;
            private final Set<String> moduleType;
        }

        public static Map<String, RuleConfig> createDefaultRuleConfigs() {
            Map<String, RuleConfig> configs = new HashMap<>();

            // APLControllerPlugin
            Map<String, ModuleConfig> aplControllerModuleNameConfig = new HashMap<>();
            aplControllerModuleNameConfig.put("RelatedList", new ModuleConfig(
                    Collections.unmodifiableSet(new HashSet<>(Arrays.asList("before", "after"))),
                    Collections.unmodifiableSet(new HashSet<>(Arrays.asList("select", "list", "related")))
            ));
            aplControllerModuleNameConfig.put("WebDetail", new ModuleConfig(
                    Collections.unmodifiableSet(new HashSet<>(Arrays.asList("before", "after"))),
                    Collections.emptySet()
            ));
            aplControllerModuleNameConfig.put("List", new ModuleConfig(
                    Collections.unmodifiableSet(new HashSet<>(Arrays.asList("before", "after"))),
                    Collections.emptySet()
            ));
            aplControllerModuleNameConfig.put("ListHeader", new ModuleConfig(
                    Collections.unmodifiableSet(new HashSet<>(Arrays.asList("before", "after"))),
                    Collections.emptySet()
            ));
            aplControllerModuleNameConfig.put("SearchList", new ModuleConfig(
                    Collections.unmodifiableSet(new HashSet<>(Arrays.asList("before", "after"))),
                    Collections.emptySet()
            ));
            aplControllerModuleNameConfig.put("TodoList", new ModuleConfig(
                    Collections.unmodifiableSet(new HashSet<>(Arrays.asList("before", "after"))),
                    Collections.emptySet()
            ));
            aplControllerModuleNameConfig.put("WhatList", new ModuleConfig(
                    Collections.unmodifiableSet(new HashSet<>(Arrays.asList("before", "after"))),
                    Collections.emptySet()
            ));
            aplControllerModuleNameConfig.put("DuplicateSearch", new ModuleConfig(
                    Collections.unmodifiableSet(new HashSet<>(Arrays.asList("before", "after"))),
                    Collections.emptySet()
            ));
            configs.put("APLControllerPlugin", new RuleConfig("APLControllerPlugin", Collections.unmodifiableMap(aplControllerModuleNameConfig)));

            // APLExportPlugin
            Map<String, ModuleConfig> aplExportModuleNameConfig = new HashMap<>();
            aplExportModuleNameConfig.put("Export", new ModuleConfig(
                    Collections.unmodifiableSet(new HashSet<>(Arrays.asList("doExport", "exportAfter"))),
                    Collections.emptySet()
            ));
            aplExportModuleNameConfig.put("ExportExcelTemplate", new ModuleConfig(
                    Collections.unmodifiableSet(new HashSet<>(Arrays.asList("doExport"))),
                    Collections.emptySet()
            ));
            configs.put("APLExportPlugin", new RuleConfig("APLExportPlugin", Collections.unmodifiableMap(aplExportModuleNameConfig)));

            return Collections.unmodifiableMap(configs);
        }
    }
} 