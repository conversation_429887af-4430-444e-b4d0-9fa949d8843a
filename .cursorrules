- 单元测试
    - 默认放到groovy包下
    - 命名规范：类名+Test，如果存在则直接在此类中修改
    - 使用 spock 进行单元测试。
    - 覆盖主要的业务逻辑和边界条件。
    - 使用 Mock 框架（如 spock）来模拟依赖。
    - 确保每一次方法调用都能被测试用例覆盖
    - 每个用例设计时请注意入参的边界值均被覆盖，对于数字，测试负数、0、正数、最小值、最大值、NaN（非数字）、无穷大等；对于字符串，测试空字符串、单字符、非ASCII字符串、多字节字符串等；对于集合类型，测试空、1、第一个、最后一个等；对于日期，测试1月1号、2月29号、12月31号等.
    - 确保代码中每个分支都有用例覆盖
    - 保证用例之间不要相互依赖
    - 不使用springboot
    - 如果当前类中使用了I18nClient,I18nServiceImpl等相关类，则为每个未使用PowerMock的单测类添加
        - 参考代码：
            def setupSpec() {
                def i18nClient = Mock(I18nClient)
                def i18nServiceImpl = Mock(I18nServiceImpl)
                Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
                Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
                i18nClient.getAllLanguage() >> []
            }
        - 添加了setupSpec方法后需要导入相关依赖
            import com.fxiaoke.i18n.client.I18nClient
            import com.fxiaoke.i18n.client.impl.I18nServiceImpl
            import org.powermock.reflect.Whitebox

    - 当需要验证WebPageGraySwitch.isAllow方法时，需要参考下面代码编写单测。注意isAllow使用通配符即可 *_ 。
        - 导入相关依赖类：
        import com.fxiaoke.release.FsGrayReleaseBiz
        import com.facishare.paas.appframework.core.util.UdobjGrayConfig

        - 参考代码：
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        Whitebox.setInternalState(WebPageGraySwitch.biz, "biz", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(*_) >> tureOrFalse
       
        
        
