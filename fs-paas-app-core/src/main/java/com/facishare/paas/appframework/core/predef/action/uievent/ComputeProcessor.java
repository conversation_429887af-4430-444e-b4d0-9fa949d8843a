package com.facishare.paas.appframework.core.predef.action.uievent;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.CalculateObjectData;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.relation.CalculateFields;
import com.facishare.paas.appframework.metadata.relation.CalculateRelation;
import com.facishare.paas.appframework.metadata.relation.EditCalculateParam;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-08-05 19:53
 */
@Slf4j
public class ComputeProcessor extends AbstractProcessor {
    public ComputeProcessor(ActionContainer container) {
        super(container);
    }

    @Override
    public void invoke(UIEventProcess.ProcessRequest request, ProcessorContext context) {
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName());
        if (request.isDoCalculate()) {
            //补充temporaryId
            fillTemporaryId(request);
            // 构建计算参数
            EditCalculateParam param = buildEditCalculateParam(request);
            // 调用计算接口
            serviceFacade.calculateForEditData(requestContext.getUser(), param);
            stopWatch.lap("calculateForEditData");
        } else {
            calculateForUIEventWithGray(request);
            stopWatch.lap("calculateForUIEventWithGray");
        }
        removeTemporaryId(request);
        stopWatch.logSlow(500);
    }

    private void calculateForUIEvent(UIEventProcess.ProcessRequest request) {
        // 主对象变化的字段
        List<String> masterChangedFields = findChangedFields(request.getMasterWithOnlyChangedFields());
        // 从对象变化的字段
        Map<String, List<String>> detailChangedFields = findDetailChangedFields(request.getDetailWithOnlyChangedFields());
        // 查询有删除数据的从对象列表
        List<String> describeApiNameListDeleteOrAdd = findApiNameListDeleteOrAddData(container.getDetailData(), request.getDetailDataMap());
        // 调用计算接口
        serviceFacade.calculateForUIEvent(requestContext.getUser(), request.getMasterData(),
                request.getDetailDataMap(), masterChangedFields, detailChangedFields, describeApiNameListDeleteOrAdd);
    }

    private void calculateForUIEventWithGray(UIEventProcess.ProcessRequest request) {
        StopWatch stopWatch = StopWatch.create("ComputeProcessor.calculateForUIEventWithGray");
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CALCULATE_DEFAULT_VALUE_AND_REPORT_LOG_FOR_UI_EVENT, requestContext.getTenantId())) {
            calculateForUIEvent(request);
            stopWatch.lap("calculateForUIEvent");
            stopWatch.logSlow(500);
            return;
        }
        EditCalculateParam calculateParam = null;
        CalculateFields calculateFields = null;
        try {
            //补充temporaryId
            fillTemporaryId(request, true);
            // 构建计算参数
            calculateParam = buildEditCalculateParam(request);
            //计算字段依赖关系
            calculateFields = infraServiceFacade.computeCalculateFieldsForEditData(calculateParam);
            stopWatch.lap("computeCalculateFieldsForEditData");
        } catch (Exception e) {
            log.warn("computeCalculateFieldsDependency fail! ei:{}, objectApiName:{}", requestContext.getTenantId(), objectDescribe.getApiName(), e);
        }
        // 调用计算接口
        calculateForUIEvent(request);
        stopWatch.lap("calculateForUIEvent");

        try {
            Map<String, List<IObjectData>> detailDataMap = Optional.ofNullable(calculateParam)
                    .map(EditCalculateParam::getDetailDataMap)
                    .orElse(Collections.emptyMap());
            Map<String, List<IObjectData>> detailWithOnlyChangedFields = request.getDetailWithOnlyChangedFields();
            if (Objects.isNull(calculateFields) || CollectionUtils.empty(detailDataMap) || CollectionUtils.empty(detailWithOnlyChangedFields)) {
                stopWatch.logSlow(500);
                return;
            }
            Map<String, List<CalculateObjectData>> calculateDataMap = calculateFields.getCalculateDataMap();
            detailWithOnlyChangedFields.forEach((describeApiName, dataList) -> {
                modifyDetailWithOnlyChangedFields(detailObjectDescribeMap.get(describeApiName), dataList, calculateDataMap.get(describeApiName), detailDataMap.get(describeApiName));
            });
            stopWatch.lap("modifyDetailWithOnlyChangedFields");
        } catch (Exception e) {
            log.warn("modifyDetailWithOnlyChangedFields fail! ei:{}, objectApiName:{}", requestContext.getTenantId(), objectDescribe.getApiName(), e);
        }
        stopWatch.logSlow(500);
    }

    private void modifyDetailWithOnlyChangedFields(IObjectDescribe detailDescribe, List<IObjectData> modifyChangeDataList, List<CalculateObjectData> calculateObjectDataList, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(modifyChangeDataList) || CollectionUtils.empty(calculateObjectDataList) || Objects.isNull(detailDescribe)) {
            return;
        }
        List<String> defaultValueFields = ObjectDescribeExt.of(detailDescribe).getDefaultValueFields().stream()
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toList());
        if (CollectionUtils.empty(defaultValueFields)) {
            return;
        }
        Map<String, CalculateObjectData> calculateObjectDataMap = calculateObjectDataList.stream()
                .collect(Collectors.toMap(CalculateObjectData::getIndex, Function.identity()));
        Map<String, IObjectData> objectDataMap = CollectionUtils.nullToEmpty(objectDataList).stream()
                .map(ObjectDataExt::of)
                .filter(objectDataExt -> Objects.nonNull(objectDataExt.getDataIndex()))
                .collect(Collectors.toMap(ObjectDataExt::getDataIndex, Function.identity()));
        for (IObjectData objectData : modifyChangeDataList) {
            ObjectDataExt dataExt = ObjectDataExt.of(objectData);
            String dataIndex = dataExt.getDataIndex();
            CalculateObjectData calculateObjectData = calculateObjectDataMap.get(dataIndex);
            if (Objects.isNull(calculateObjectData)) {
                continue;
            }
            List<String> fieldNames = calculateObjectData.getCalculateFields().stream()
                    .map(CalculateRelation.RelateField::getFieldName)
                    .filter(defaultValueFields::contains)
                    .collect(Collectors.toList());
            if (CollectionUtils.empty(fieldNames)) {
                continue;
            }
            if (UIEventUtils.isNewDetail(dataExt)) {
                fieldNames.forEach(fieldName -> dataExt.setIfAbsent(fieldName, null));
            } else {
                IObjectData data = objectDataMap.get(dataIndex);
                if (Objects.nonNull(data)) {
                    ObjectDataExt.of(data).toMap().forEach((key, value) -> {
                        if (fieldNames.contains(key)) {
                            dataExt.setIfAbsent(key, value);
                        }
                    });
                }
            }
        }
    }

    private EditCalculateParam buildEditCalculateParam(UIEventProcess.ProcessRequest request) {
        Map<String, List<IObjectData>> detailAddDataMap = Maps.newHashMap();
        Map<String, List<IObjectData>> detailDeleteDataMap = Maps.newHashMap();
        Map<String, List<IObjectData>> detailModifyDataMap = Maps.newHashMap();
        request.getDetailDataMap().forEach((apiName, dataList) -> {
            Map<String, IObjectData> dataMap = dataList.stream().filter(x -> !UIEventUtils.isNewDetail(x))
                    .collect(Collectors.toMap(x -> ObjectDataExt.of(x).getMark(), x -> x));
            List<IObjectData> addDataList = dataList.stream().filter(UIEventUtils::isNewDetail).collect(Collectors.toList());
            List<IObjectData> modifyDataList = CollectionUtils.nullToEmpty(request.getDetailWithOnlyChangedFields())
                    .getOrDefault(apiName, Collections.emptyList()).stream()
                    .filter(x -> !UIEventUtils.isNewDetail(x))
                    .filter(x -> ObjectDataExt.of(x).toMap().keySet().stream().anyMatch(k -> !ObjectDescribeExt.isSystemField(k)
                            && detailObjectDescribeMap.containsKey(apiName)
                            && detailObjectDescribeMap.get(apiName).containsField(k)))
                    .collect(Collectors.toList());
            List<IObjectData> deleteDataList = CollectionUtils.nullToEmpty(container.getDetailData())
                    .getOrDefault(apiName, Collections.emptyList()).stream()
                    .filter(x -> !dataMap.containsKey(ObjectDataExt.of(x).getMark()))
                    .collect(Collectors.toList());

            if (CollectionUtils.notEmpty(addDataList)) {
                detailAddDataMap.put(apiName, addDataList);
            }
            if (CollectionUtils.notEmpty(deleteDataList)) {
                detailDeleteDataMap.put(apiName, deleteDataList);
            }
            if (CollectionUtils.notEmpty(modifyDataList)) {
                detailModifyDataMap.put(apiName, modifyDataList);
            }
        });

        return EditCalculateParam.builder()
                .masterData(request.getMasterData())
                .oldMasterData(container.getMasterData())
                .detailDataMap(request.getDetailDataMap())
                .oldDetailDataMap(container.getDetailData())
                .masterModifyData(ObjectDataExt.of(request.getMasterWithOnlyChangedFields()).toMap())
                .detailAddDataMap(detailAddDataMap)
                .detailDeleteDataMap(detailDeleteDataMap)
                .detailModifyDataMap(detailModifyDataMap)
                .masterDescribe(objectDescribe)
                .detailDescribeMap(detailObjectDescribeMap)
                .excludeDefaultValue(false)
                .includeQuoteField(true)
                .excludeLookupRelateField(false)
                .build()
                .initWithUIEvent();
    }

    private void removeTemporaryId(UIEventProcess.ProcessRequest request) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.REMOVE_TEMPORARY_ID_BY_UI_EVENT_COMPUTE_GRAY, requestContext.getTenantId())) {
            return;
        }
        ObjectDataExt.removeTemporaryId(container.getDetailData());
        ObjectDataExt.removeTemporaryId(request.getDetailDataMap());
        ObjectDataExt.removeTemporaryId(request.getDetailWithOnlyChangedFields());
    }

    private void fillTemporaryId(UIEventProcess.ProcessRequest request) {
        fillTemporaryId(request, false);
    }

    private void fillTemporaryId(UIEventProcess.ProcessRequest request, boolean needDataIndex) {
        fillTemporaryId(container.getDetailData(), needDataIndex);
        fillTemporaryId(request.getDetailDataMap(), needDataIndex);
        fillTemporaryId(request.getDetailWithOnlyChangedFields(), needDataIndex);
    }

    /**
     * 补充TemporaryId
     *
     * @param detailDataMap
     */
    private void fillTemporaryId(Map<String, List<IObjectData>> detailDataMap, boolean needDataIndex) {
        if (CollectionUtils.empty(detailDataMap)) {
            return;
        }
        detailDataMap.forEach((apiName, dataList) -> {
            dataList.forEach(data -> {
                ObjectDataExt dataExt = ObjectDataExt.of(data);
                if (!UIEventUtils.isNewDetail(data)) {
                    String mark = dataExt.getMark();
                    dataExt.setTemporaryId(mark);
                    if (needDataIndex) {
                        dataExt.setDataIndex(mark);
                    }
                } else {
                    String temporaryId = ObjectDataExt.generateId();
                    dataExt.setTemporaryId(temporaryId);
                    if (needDataIndex) {
                        dataExt.setDataIndex(temporaryId);
                    }
                }
            });
        });
    }

    /**
     * 查找有删除或新增数据的从对象api
     * newData中，每个对象下的数据列表，排除新增数据(无mark的数据)，剩下数据的数量
     * 如果小于oldData，表示有删除数据
     */
    private List<String> findApiNameListDeleteOrAddData(Map<String, List<IObjectData>> oldData,
                                                        Map<String, List<IObjectData>> newData) {
        List<String> result = Lists.newArrayList();
        oldData.forEach((apiName, oldDataList) -> {
            List<IObjectData> newDataList = CollectionUtils.nullToEmpty(newData.get(apiName));
            int size = (int) newDataList.stream()
                    .filter(d -> Objects.nonNull(d.get(ObjectDataExt.MARK_API_NAME))).count();
            // 新增删除只需要add一次即可
            if (size < oldDataList.size()) {
                // 数量变少肯定有删除数据
                result.add(apiName);
            } else if (size < newDataList.size()) {
                // 过滤后的size变少肯定有新增数据
                result.add(apiName);
            }
        });
        return result;
    }

    /**
     * 从对象变化的字段
     */
    private Map<String, List<String>> findDetailChangedFields(
            Map<String, List<IObjectData>> detailWithOnlyChangedFields) {
        Map<String, List<String>> result = Maps.newHashMap();
        if (CollectionUtils.notEmpty(detailWithOnlyChangedFields)) {
            detailWithOnlyChangedFields.forEach((apiName, dataList) -> {
                result.put(apiName, batchFindChangedFields(dataList));
            });
        }
        return result;
    }

    private List<String> batchFindChangedFields(List<IObjectData> dataList) {
        List<String> result = Lists.newArrayList();
        if (CollectionUtils.notEmpty(dataList)) {
            Set<String> set = dataList.stream().flatMap(data -> ObjectDataExt.of(data).toMap().keySet().stream())
                    .collect(Collectors.toSet());
            result.addAll(set);
        }
        return result;
    }

    /**
     * 变化的字段
     *
     * @param dataWithOnlyChangedFields
     */
    private List<String> findChangedFields(IObjectData dataWithOnlyChangedFields) {
        List<String> result = Lists.newArrayList();
        Map<String, Object> map = ObjectDataExt.of(dataWithOnlyChangedFields).toMap();
        if (CollectionUtils.notEmpty(map)) {
            result.addAll(map.keySet());
        }
        return result;
    }

}
