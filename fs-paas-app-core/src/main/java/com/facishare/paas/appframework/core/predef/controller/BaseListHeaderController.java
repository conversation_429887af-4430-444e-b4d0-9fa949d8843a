package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.crm.privilege.util.PrivilegeFieldsUtils;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.AcceptableValidateException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.handler.Handler;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerFunctions;
import com.facishare.paas.appframework.core.predef.handler.listheader.ListHeaderHandler;
import com.facishare.paas.appframework.core.predef.service.ObjectQuerySceneService;
import com.facishare.paas.appframework.core.util.RecordTypeUtil;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.domain.DomainPluginLogicService;
import com.facishare.paas.appframework.metadata.dto.CommonFilterField;
import com.facishare.paas.appframework.metadata.dto.CommonFilterField.FilterField;
import com.facishare.paas.appframework.metadata.fieldalign.GlobalFieldAlign;
import com.facishare.paas.appframework.metadata.handler.SimpleHandlerDescribe;
import com.facishare.paas.appframework.metadata.layout.*;
import com.facishare.paas.appframework.metadata.layout.component.*;
import com.facishare.paas.appframework.metadata.layout.factory.ListComponentFactory;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.MasterDetail;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.userdefobj.DefObjConstants.*;
import static com.facishare.paas.appframework.common.util.CollectionUtils.sortByGivenOrder;
import static com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController.Arg;
import static com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController.Result;
import static com.facishare.paas.appframework.core.util.RequestUtil.VERSION_715;
import static com.facishare.paas.appframework.metadata.SearchTemplateExt.SceneType.isSceneType;

/**
 * Created by liyiguang on 2018/4/19.
 */
public abstract class BaseListHeaderController<A extends Arg> extends PreDefineController<A, StandardListHeaderController.Result> {
    private List<String> showFields;
    protected ObjectDescribeExt objectDescribeExt;
    protected LayoutExt layoutExt;
    private List<ISearchTemplate> templates;
    private List<ISearchTemplate> baseScenes;
    private List<IButton> bulkButtonList;
    private List<LayoutRuleExt.FieldConfig> fieldConfigs;
    private ILayout defaultLayout;
    private Boolean hasEditPermission;
    protected LayoutExt listLayout;
    private ListComponentExt listComponentExt;
    private List<ILayout> abstractLayoutList;
    private Map<String, String> recordLayoutMapping;
    private String fieldAlign;
    private List<CommonFilterField.FilterField> filterFields;
    protected List<String> filterFieldsConfig;
    private Boolean componentFlag;

    /**
     * 侧栏可筛选元素显示到顶部，默认为开
     */
    private Boolean convertTopListFilter = true;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        //无需校验功能权限
        return Collections.emptyList();
    }

    @Override
    protected void doInit() {
        ContextCacheUtil.openContextCache();
        if (!serializeEmpty()) {
            RequestUtil.setSerializeEmptyFalse();
        }
        IObjectDescribe describe = doGetObjectDescribe();
        //非灰度企业才直接修改describe，否则通过describeExt下发
        if (!AppFrameworkConfig.notCopyDescribeInController(controllerContext.getTenantId(), describe.getApiName())) {
            RecordTypeUtil.extendRecordTypeFieldWithValue(objectDescribeExt);
            objectDescribeExt.changeIsIndexWithOuter(controllerContext.getUser());
        }
        stopWatch.lap("findObject");
        initLayoutContext();
        stopWatch.lap("initLayoutContext");
    }

    protected boolean serializeEmpty() {
        return arg.serializeEmpty();
    }

    private void initLayoutContext() {
        if (!isGrayListLayout()) {
            return;
        }
        LayoutContext layoutContext = LayoutContext.get();
        layoutContext.setLayoutAgentType(LayoutAgentType.of(arg.getLayoutAgentType()));
    }

    protected boolean isBackground() {
        return false;
    }

    @Override
    protected Result doService(A arg) {
        doFindListLayout();
        doFindTemplates();
        doFindOtherInfo();
        return getResult();
    }

    private void doFindListLayout() {
        listComponentExt = initListComponentExt();
        if (!arg.onlyDefaultTemplate()) {
            //defaultLayout为详情页布局
            defaultLayout = findDefaultLayout();//默认详情页布局，别往下看了真的是详情页布局指定查列表页布局也查不了，只能查详情页布局和新建编辑页布局
            stopWatch.lap("findDefaultLayout");
        }
    }

    private void doFindTemplates() {
        if (isBackground()) {
            return;
        }
        templates = findTemplates();
        stopWatch.lap("findTemplates");

        removeUnAuthorizedFields(templates, getShowFields());
        stopWatch.lap("removeUnAuthorizedFields");

        if (arg.onlyDefaultTemplate()) {
            throw new AcceptableValidateException(buildDefaultTemplateResult());
        }

        //only_template=true的时候，直接返回只包含场景的result，因为怕后边代码导致子类出现空指针等异常，所以通过异常跳出代码。
        if (arg.isOnlyTemplate()) {
            throw new AcceptableValidateException(buildTemplateResult());
        }
    }

    private void doFindOtherInfo() {
        if (isBackground()) {
            layoutExt = LayoutExt.of((ILayout) null);
            return;
        }
        ILayout layout = findLayout(arg);
        layoutExt = LayoutExt.of(layout);
        stopWatch.lap("findLayout");
        OptionalFeaturesSwitchDTO optionalFeaturesSwitch = infraServiceFacade.findOptionalFeaturesSwitch(controllerContext.getTenantId(), objectDescribeExt);
        stopWatch.lap("findOptionalFeaturesSwitch");
        if (!objectDescribeExt.isSlaveObject() && optionalFeaturesSwitch.getIsRelatedTeamEnabled()) {
            layoutExt.fillRelevantTeamField();
        }

        // 设置表头buttons(通用按钮)
        if (needNormalButton()) {
            List<IButton> buttons = getButtons();
            layout.setButtons(buttons);
            stopWatch.lap("getButtons");
        }

        // 查询基础数据范围
        baseScenes = findBaseScenes();
        stopWatch.lap("findBaseScenes");
        bulkButtonList = Lists.newArrayList();
        if (needListBatchButton()) {
            bulkButtonList = queryBulkButton();
            stopWatch.lap("queryBulkButton");
        }

        if (arg.checkEditPermission()) {
            hasEditPermission = checkEditPermission();
            stopWatch.lap("checkEditPermission");
        }

        fieldAlign = getFieldAlign();
        stopWatch.lap("getFieldAlign");

        if (isMobileLayout()) {
            filterFields = getFilterFields(layout);
            stopWatch.lap("getFilterFields");
        }
    }

    private Result getResult() {
        Result ret = buildResult(layoutExt.getLayout(), baseScenes, bulkButtonList);
        stopWatch.lap("buildResult");
        processResult(ret);
        stopWatch.lap("processResult");
        return ret;
    }

    protected boolean needListBatchButton() {
        return !AppFrameworkConfig.unSupportButtonInternalObject(controllerContext.getObjectApiName());
    }

    protected boolean needNormalButton() {
        return !AppFrameworkConfig.unSupportButtonInternalObject(controllerContext.getObjectApiName());
    }

    private Map<String, Object> getRelatedListComponent() {
        return arg.getRelatedListComponent();
    }

    private Map<String, Object> getListComponent() {
        return arg.getListComponent();
    }

    private ListComponentExt initListComponentExt() {
        if (CollectionUtils.notEmpty(getRelatedListComponent()) && getRelatedListComponent().containsKey(ListComponentExt.SCENE_INFO)) {
            //相关列表组件
            ListComponentExt relatedListCompExt = ListComponentExt.of(getRelatedListComponent());
            ListComponentExt listComponentExt = buildListComponentExtByListLayout();
            if (Objects.nonNull(listComponentExt) && CollectionUtils.notEmpty(listComponentExt.getViewInfos())) {
                relatedListCompExt.resetViewInfos(listComponentExt.getViewInfos());
            }
            return relatedListCompExt
                    .fillSceneInfoPageType(IComponentInfo.PAGE_TYPE_RELATED)
                    .fillButtonInfoPageType(IComponentInfo.PAGE_TYPE_RELATED);
        }
        return buildListComponentExtByListLayout();
    }

    private ListComponentExt buildListComponentExtByListLayout() {
        if (!isGrayListLayout()) {
            return null;
        }
        if (CollectionUtils.notEmpty(getListComponent())) {
            return ListComponentExt.of(getListComponent());
        }
        LayoutExt listLayoutExt = findListLayout(arg.onlyDefaultTemplate());
        stopWatch.lap("findListLayout");
        return Optional.ofNullable(listLayoutExt)
                .map(ListLayoutExt::of)
                .flatMap(ListLayoutExt::getFirstListComponent)
                .map(it -> {
                    it.fillAttributeFromLayout(listLayoutExt);
                    return it;
                })
                .orElse(null);
    }

    private LayoutExt findListLayout(boolean simpleLayout) {
        if (!isGrayListLayout()) {
            return null;
        }
        if (Objects.nonNull(listLayout)) {
            return listLayout;
        }
        if (simpleLayout) {
            listLayout = findSimpleLayout();
            return listLayout;
        }
        ILayout layout = getListLayout();
        listLayout = LayoutExt.of(layout);
        return listLayout;
    }

    private ILayout getListLayout() {
        String listLayoutApiName = getListLayoutApiName();
        if (!Strings.isNullOrEmpty(listLayoutApiName)) {
            return serviceFacade.getLayoutLogicService().getListLayoutByApiNameWitchComponents(controllerContext.getUser(), listLayoutApiName,
                    objectDescribeExt, PageType.ListHeader, arg.getListType());
        }
        return serviceFacade.getLayoutLogicService().getListLayoutWitchComponents(buildLayoutContext(),
                objectDescribeExt, PageType.ListHeader, arg.getListType(), arg.getThirdRecordType());
    }

    protected String getListLayoutApiName() {
        return arg.getListLayoutApiName();
    }

    /**
     * 如果当前是移动端的请求，需要返回移动端的 simpleLayout
     *
     * @return
     */
    private LayoutExt findSimpleLayout() {
        String listLayoutApiName = getListLayoutApiName();
        ILayout layout;
        if (Strings.isNullOrEmpty(listLayoutApiName)) {
            layout = serviceFacade.getLayoutLogicService().findListLayoutByRecordType(buildLayoutContext(), objectDescribeExt, arg.getThirdRecordType());
        } else {
            layout = serviceFacade.getLayoutLogicService().findLayoutByApiName(controllerContext.getUser(), listLayoutApiName, objectDescribeExt.getApiName());
        }
        if (Objects.isNull(layout)) {
            return null;
        }
        if (!isMobileLayout()) {
            return LayoutExt.of(layout);
        }
        ListComponentFactory listComponentFactory = serviceFacade.getBean(ListComponentFactory.class);
        LayoutExt mobileLayout = MobileListLayoutBuilder.builder()
                .listLayout(LayoutExt.of(layout))
                .pageType(PageType.ListHeader)
                .listComponentFactory(listComponentFactory)
                .build()
                .getMobileListLayout();
        ListLayoutExt listLayoutExt = ListLayoutExt.of(layout);
        listLayoutExt.syncComponentAndLayoutStructure(mobileLayout);
        return LayoutExt.of(listLayoutExt.getLayout());
    }

    private ILayout findDefaultLayout() {
        ILayout defaultLayout = serviceFacade.getLayoutLogicService().findObjectLayoutWithType(buildLayoutContext(), "", objectDescribeExt.getObjectDescribe(), ILayout.LIST_LAYOUT_TYPE, null);
        if (!objectDescribeExt.isSlaveObject()) {
            LayoutExt.of(defaultLayout).fillRelevantTeamField();
        }
        return defaultLayout;
    }

    private String getFieldAlign() {
        // 处理 fieldAlign
        if (infraServiceFacade.isGrayFieldAlign(controllerContext.getTenantId(), controllerContext.getObjectApiName())) {
            GlobalFieldAlign.FieldAlign fieldAlign = Optional.ofNullable(listComponentExt)
                    .map(it -> (String) it.getAttribute(LayoutStructure.FIELD_ALIGN))
                    .map(GlobalFieldAlign.FieldAlign::of)
                    .orElseGet(() -> infraServiceFacade.getGlobalFieldAlign(controllerContext.getTenantId()).getFieldAlignByLang(controllerContext.getLang()));
            return fieldAlign.getType();
        }
        return null;
    }

    private Result buildTemplateResult() {
        return Result.builder()
                .templates(QueryTemplateDocument.ofList(templates))
                .build();
    }

    private Result buildDefaultTemplateResult() {
        List<ISearchTemplate> defaultTemplate = SearchTemplateExt.getFirstDefaultTemplate(templates, arg.getThirdRecordType())
                .map(Lists::newArrayList)
                .orElse(null);
        return Result.builder()
                .templates(QueryTemplateDocument.ofList(defaultTemplate))
                .build();
    }

    /**
     * 从场景的移动端摘要字段中删除没有权限的字段
     *
     * @param templates     场景的集合
     * @param fieldNameList 有权限的字段名称的集合
     */
    private void removeUnAuthorizedFields(List<ISearchTemplate> templates, List<String> fieldNameList) {
        templates.forEach(template -> SearchTemplateExt.of(template).showFieldsFromEnable(Sets.newHashSet(fieldNameList)));
    }

    protected Result buildResult(ILayout layout, List<ISearchTemplate> baseScenes, List<IButton> bulkButtonList) {
        List<String> authorizedFields = getShowFields();
        Result result = Result.builder()
                .objectDescribe(ObjectDescribeDocument.of(objectDescribeExt))
                .layout(LayoutDocument.of(layout))
                .isChildObj(objectDescribeExt.isSlaveObject()) //租户场景完成后可以去掉
                .buttons(ButtonDocument.fromButtons(bulkButtonList))
                .isInApprovalWhiteList(AppFrameworkConfig.isInMasterDetailApprovalWhiteList(controllerContext.getTenantId()))
                .hasEditPermission(hasEditPermission)
                .generalInfoHideType(getGeneralInfoHideType())
                .componentHideType(getComponentHideType())
                .build();

        Optional.ofNullable(listComponentExt)
                .ifPresent(it -> result.setSceneRenderType(getSceneRenderTypeFromComponent()));
        if (isMobileLayout()) {
            result.setFilterFields(filterFields);
            result.setConvertTopListFilter(convertTopListFilter);
            result.setSupportGeoQuery(isSupportGeoQuery());
            templates.forEach(it -> it.setFieldList(Collections.emptyList()));
            result.setTemplates(QueryTemplateDocument.ofList(templates));
            if (AppFrameworkConfig.manyAbstractLayout(controllerContext.getObjectApiName(), controllerContext.getTenantId())) {
                result.setAbstractLayoutList(LayoutDocument.ofList(abstractLayoutList));
                result.setRecordLayoutMapping(recordLayoutMapping);
            }
        } else {
            result.setTemplates(QueryTemplateDocument.ofList(templates));
            result.setBaseScenes(QueryTemplateDocument.ofList(baseScenes));
            result.setVisibleFields(authorizedFields);
            result.setVisibleFieldsWidth(getFieldWidth());
            result.setQuickFilterField(getQuickFilterField());
        }
        if (isGrayListLayout()) {
            result.setViewInfo(getViewInfo());
            setSummaryInfo(authorizedFields, result);
            result.setListSingleExposed(getListSingleExposedFromComponent());
            result.setFieldAlign(fieldAlign);
        }
        result.setRenderTypeInfo(getRenderTypeInfo());
        return result;
    }

    protected void processResult(Result result) {

    }

    private String getSceneRenderTypeFromComponent() {
        Optional<IScenesComponentInfo> sceneInfo = listComponentExt.getSceneInfoByPageType(arg.getListType());
        if (!sceneInfo.isPresent()) {
            sceneInfo = listComponentExt.getSceneInfoByPageType(IComponentInfo.PAGE_TYPE_LIST);
        }
        String renderType = sceneInfo.map(x -> x.getRenderType()).orElse(null);
        return Strings.isNullOrEmpty(renderType) ? IListComponentInfo.RENDER_TYPE_DROP_DOWN : renderType;
    }

    private int getListSingleExposedFromComponent() {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.RELATED_LIST_SINGLE_EXPOSED_GRAY, controllerContext.getTenantId())) {
            return listComponentExt.getButtonInfoByUsePageAndRenderPageType(ButtonUsePageType.DataList, arg.getListType())
                    .map(it -> ListComponentInfo.ButtonRenderType.LIST_SINGLE.getExposedButtonDefaultSize(it))
                    .orElse(0);
        }
        return listComponentExt.getButtonInfo().stream()
                .filter(it -> ListComponentInfo.ButtonRenderType.LIST_SINGLE.getType().equals(it.getRenderType()))
                .findFirst()
                .map(x -> ListComponentInfo.ButtonRenderType.LIST_SINGLE.getExposedButtonDefaultSize(x))
                .orElse(0);
    }

    /**
     * 将组件中的字段汇总设置到返回结果
     * <p>
     * 大列表支持 「当前页数据汇总」、 「所有数据汇总」和「选中数据汇总」
     * 相关列表支持「当前页数据汇总」和 「所有数据汇总」
     * 选数据列表支持「选中数据汇总」
     * <p>
     * 移动端支持 「所有数据汇总」和「选中数据汇总」
     *
     * @param authorizedFields
     * @param result
     */
    private void setSummaryInfo(List<String> authorizedFields, Result result) {
        if (!(isList() || isRelatedList() || isSelectedList())) {
            // 1. !(list || related)
            return;
        }

        if (!isRelatedList()) {
            //选中数据汇总
            List<ISummaryComponentInfo> summaryComponentInfos = Optional.ofNullable(listComponentExt)
                    .map(it -> it.getSelectedDataSummaryComponentInfoByPageType(arg.getListType()))
                    .orElseGet(Lists::newArrayList);
            result.setSelectedDataSummaryInfo(buildTotalsInfo(summaryComponentInfos, authorizedFields));
        }
        // 选数据列表只支持选中数据的汇总
        if (isSelectedList()) {
            return;
        }

        if (!isMobileLayout()) {
            //移动端列表页没有翻页
            // 当前页的汇总字段 暂只适用于网页端
            List<ISummaryComponentInfo> singlePageDataTotalsInfo = Optional.ofNullable(listComponentExt)
                    .map(it -> it.getSummaryComponentInfoByPageType(arg.getListType()))
                    .orElseGet(Lists::newArrayList);
            result.setSummaryInfo(buildTotalsInfo(singlePageDataTotalsInfo, authorizedFields));
        }


        //所有页的汇总字段
        List<ISummaryComponentInfo> allDataTotalsInfo = Optional.ofNullable(listComponentExt)
                .map(it -> it.getAllPageSummaryComponentInfoByPageType(arg.getListType()))
                .orElseGet(Lists::newArrayList);

        result.setAllPageSummaryInfo(buildTotalsInfo(allDataTotalsInfo, authorizedFields));
    }

    /**
     * 换用totals区分summary，因为详情页用summary来摘要字段组件
     * 列表页 汇总字段配置信息
     *
     * @param totalsInfo
     * @param authorizedFields
     * @return
     */
    private List<DocumentBaseEntity> buildTotalsInfo(List<ISummaryComponentInfo> totalsInfo, List<String> authorizedFields) {
        return ListComponentExt.filterSummaryInfos(objectDescribeExt, totalsInfo, authorizedFields).stream()
                .map(ISummaryComponentInfo::toMap)
                .map(DocumentBaseEntity::new)
                .collect(Collectors.toList());
    }

    protected boolean isRelatedList() {
        return IComponentInfo.PAGE_TYPE_RELATED.equals(arg.getListType());
    }

    protected boolean isSelectedList() {
        return IComponentInfo.PAGE_TYPE_SELECTED.equals(arg.getListType());
    }

    protected boolean isList() {
        return IComponentInfo.PAGE_TYPE_LIST.equals(arg.getListType());
    }

    protected boolean isRelatedIndependent() {
        return CollectionUtils.notEmpty(getRelatedListComponent()) && getRelatedListComponent().containsKey(ListComponentExt.SCENE_INFO);
    }

    protected LayoutLogicService.LayoutContext buildLayoutContext() {
        return LayoutLogicService.LayoutContext.of(controllerContext.getUser(), controllerContext.getAppId());
    }

    protected List<ILayout> findMobileLayouts(ObjectDescribeExt describeExt) {
        List<ILayout> listLayouts = serviceFacade.getLayoutLogicService().findMobileListLayout(buildLayoutContext(), describeExt,
                ILayout.LIST_LAYOUT_TYPE.equals(arg.getListType()));
        listLayouts.forEach(this::doRender);

        return listLayouts;
    }

    protected void doRender(ILayout layout) {
        getComponentRender(layout).render(layout);
    }

    protected TableComponentRender getComponentRender(ILayout x) {
        return TableComponentRender.builder()
                .functionPrivilegeService(serviceFacade)
                .user(controllerContext.getUser())
                .describeExt(objectDescribeExt)
                .tableComponentExt(TableComponentExt.of(LayoutExt.of(x).getTableComponent()
                        .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR)))))
                .build();
    }

    private List<DocumentBaseEntity> getViewInfo() {
        if (isBackground()) {
            return null;
        }
        if (!isGrayListLayout()) {
            return null;
        }
        List<IViewComponentInfo> viewComponentInfos = Optional.ofNullable(listComponentExt)
                .map(ListComponentExt::getViewInfos)
                .orElseGet(Lists::newArrayList);

        List<DocumentBaseEntity> result = Lists.newArrayList();

        boolean fromWebSite = arg.fromWebSite();
        for (IViewComponentInfo viewInfo : viewComponentInfos) {
            resetCardLayoutComponent(viewInfo);
            if (fromWebSite) {  // 独立站点的列表页视图, 除非不能展示(字段被删除/禁用), 都可以展示
                viewInfo.setIsShow(true);
            }
            if (!viewFieldIsPresent(viewInfo)) {
                viewInfo.setIsShow(false);
            }
            Map document = ((ViewComponentInfo) viewInfo).getContainerDocument();
            result.add(new DocumentBaseEntity(document));
        }
        return result;
    }

    private void resetCardLayoutComponent(IViewComponentInfo viewInfo) {
        if (!IViewComponentInfo.CARD_VIEW.equals(viewInfo.getName())) {
            return;
        }
        Map cardLayout = viewInfo.get("card_layout", Map.class);
        if (CollectionUtils.empty(cardLayout)) {
            return;
        }
        Set<String> unauthorizedFields = serviceFacade.getUnauthorizedFields(controllerContext.getUser(), objectDescribeExt.getApiName());
        NewTableComponentExt newTableComponentExt = new NewTableComponentExt(cardLayout);
        newTableComponentExt.formatNewLayoutByDescribe(objectDescribeExt, unauthorizedFields, controllerContext.getUser(), true);
    }

    private List<DocumentBaseEntity> getRenderTypeInfo() {
        if (!isGrayListLayout()) {
            return null;
        }
        if (!isMobileLayout()) {
            return null;
        }

        List<DocumentBaseEntity> result = Lists.newArrayList();
        Optional.ofNullable(listComponentExt)
                .flatMap(it -> it.getRenderTypeInfoByPageType(arg.getListType()))
                .ifPresent(renderTypeInfo -> {
                    Map document = ((RenderTypeComponentInfo) renderTypeInfo).getContainerDocument();
                    result.add(new DocumentBaseEntity(document));
                });
        return result;
    }

    private boolean viewFieldIsPresent(IViewComponentInfo viewInfo) {
        if (IViewComponentInfo.MAP_VIEW.equals(viewInfo.getName())) {
            return objectDescribeExt.getActiveFieldDescribeSilently(viewInfo.getLocationField()).isPresent();
        }
        if (IViewComponentInfo.CALENDAR_VIEW.equals(viewInfo.getName())) {
            return viewInfo.getTimeDimension()
                    .stream()
                    .allMatch(fieldName -> objectDescribeExt.getActiveFieldDescribeSilently(fieldName).isPresent());
        }
        return true;
    }

    private List<String> getQuickFilterField() {
        if (!isGrayListLayout()) {
            return null;
        }
        List<String> authorizedFields = getShowFields();
        return Optional.ofNullable(listComponentExt).map(it -> CollectionUtils.nullToEmpty(it.getFiltersInfos(arg.getListType())).stream()
                        // 过滤禁用、删除和 index 为 false 的字段
                        .filter(fieldName -> objectDescribeExt.getActiveFieldDescribeSilently(StringUtils.substringBefore(fieldName, "."))
                                .filter(IFieldDescribe::isIndex)
                                .isPresent())
                        // 过滤无权限的字段
                        .filter(fieldName -> authorizedFields.contains(StringUtils.substringBefore(fieldName, ".")))
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

    private List<String> getGeneralInfoHideType() {
        if (listComponentExt != null && arg != null) {
            Optional<GeneralComponentInfo> generalComponentInfo = listComponentExt.getGeneralInfoByPageType(arg.getListType());
            if (generalComponentInfo.isPresent()) {
                return generalComponentInfo.map(GeneralComponentInfo::getHideType)
                        .orElseGet(Lists::newArrayList);
            }
        }
        return Lists.newArrayList();
    }

    //todo 去掉
    private List<String> getComponentHideType() {
        if (listComponentExt != null && arg != null
                && Objects.nonNull(listComponentExt.get(ListComponentExt.FEATURE_HIDE_SETTING))) {
            List<Map> componentHideType = (List<Map>) listComponentExt.get(ListComponentExt.FEATURE_HIDE_SETTING);
            if (CollectionUtils.notEmpty(componentHideType)) {
                return (List<String>) componentHideType.get(0).get(ListComponentExt.HIDE_TYPE);
            }
            return Lists.newArrayList();
        }
        return Lists.newArrayList();
    }

    /**
     * 2023-02-20 移动端可筛选元素增加租户级配置，租户级配置中，可筛选元素可配置为跟随移动端摘布局（目前是默认布局）展示字段和自定义字段
     * 2023-02-23 如果用户没有移动端列表页布局时，则使用默认配置
     *
     * @param layout 移动端摘要布局
     * @return
     * @See {@link ObjectQuerySceneService#getTemplate}
     */
    protected List<CommonFilterField.FilterField> getFilterFields(ILayout layout) {
        List<FilterField> filterFieldsList = infraServiceFacade.findFilterFields(controllerContext.getUser(), objectDescribeExt.getObjectDescribe(), arg.getExtendAttribute(), null, null);
        boolean hasShowField = filterFieldsList.stream().anyMatch(x -> BooleanUtils.isTrue(x.getIsShow()));
        filterFieldsConfig = filterFieldsList.stream().filter(x -> BooleanUtils.isTrue(x.getIsShow()))
                .map(FilterField::getFieldName).collect(Collectors.toList());
        if (CollectionUtils.empty(filterFieldsConfig) && Objects.nonNull(listComponentExt)) {
            filterFieldsConfig = listComponentExt.getSideListFilterItems();
        }
        convertTopListFilter = infraServiceFacade.convertTopListFilter(controllerContext.getUser(), objectDescribeExt.getApiName(), arg.getExtendAttribute());
        if (Objects.isNull(convertTopListFilter) && Objects.nonNull(listComponentExt)) {
            convertTopListFilter = listComponentExt.getTopListFilterSwitch();
        }
        Optional<MasterDetail> masterDetail = objectDescribeExt.getMasterDetailField();
        Set<String> toRemoveFields = invisibleFieldNameListForListLayout.getOrDefault(objectDescribeExt.getApiName(), Sets.newHashSet());

        Set<String> authorizedFields = Sets.newHashSet(getShowFields());
        List<String> fieldList = objectDescribeExt.getFilterableFields()
                .stream()
                .filter(field -> authorizedFields.contains(field.getApiName()))
                .filter(field -> !FieldDescribeExt.CANNOT_FILTER_FIELDS.contains(field.getApiName()))
                .filter(field -> (!masterDetail.isPresent() || !ObjectDataExt.RELEVANT_TEAM.equals(field.getApiName())))
                .filter(field -> !toRemoveFields.contains(field.getApiName()))
                .filter(field -> !controllerContext.getUser().isOutUser() || !FieldDescribeExt.of(field).isNotIndexFieldForOutUser())
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toList());
        List<FilterField> filterFieldList = filterFieldsList.stream()
                .filter(field -> fieldList.contains(field.getFieldName()))
                .map(x -> FilterField.builder()
                        .fieldName(x.getFieldName())
                        .isShow(hasShowField ? x.getIsShow() : filterFieldsConfig.contains(x.getFieldName()))
                        .enableQuickFilter(x.getEnableQuickFilter())
                        .build())
                .collect(Collectors.toList());

        fillDefaultShowField(layout, filterFieldList);
        return sortByGivenOrder(filterFieldList, filterFieldsConfig, CommonFilterField.FilterField::getFieldName);
    }

    protected List<String> findFilterFields() {
        return infraServiceFacade.findFilterFields(controllerContext.getUser(), objectDescribeExt.getApiName(), arg.getExtendAttribute());
    }

    private void fillDefaultShowField(ILayout layout, List<CommonFilterField.FilterField> fieldList) {
        if (fieldList.stream().noneMatch(a -> BooleanUtils.isTrue(a.getIsShow())) && Objects.nonNull(layout)) {
            Optional<TableComponent> tableComponent = LayoutExt.of(layout).getTableComponent();
            if (!tableComponent.isPresent()) {
                return;
            }
            //按照列表布局配置的字段，补充默认的筛选字段
            List<CommonFilterField.FilterField> showFields = Lists.newArrayList();
            tableComponent.get().getIncludeFields().forEach(f -> {
                Optional<CommonFilterField.FilterField> field = fieldList.stream()
                        .filter(a -> Objects.equals(a.getFieldName(), f.getName()))
                        .findFirst();
                if (field.isPresent()) {
                    field.get().setIsShow(true);
                    showFields.add(field.get());
                }
            });
            fieldList.removeAll(showFields);
            fieldList.addAll(0, showFields);
        }
    }

    private boolean isMobileLayout() {
        return LayoutAgentType.MOBILE.getCode().equals(arg.getLayoutAgentType());
    }

    protected boolean isSupportGeoQuery() {
        return CollectionUtils.notEmpty(objectDescribeExt.findGeoSearchField());
    }

    protected IObjectDescribe findObject() {
        //灰度企业不拷贝对象描述
        if (AppFrameworkConfig.notCopyDescribeInController(controllerContext.getTenantId(), controllerContext.getObjectApiName())) {
            return serviceFacade.findObjectWithoutCopy(controllerContext.getTenantId(), controllerContext.getObjectApiName());
        }
        return serviceFacade.findObject(controllerContext.getTenantId(), controllerContext.getObjectApiName());
    }

    protected boolean checkEditPermission() {
        // 变更单对象不支持快速编辑
        if (objectDescribeExt.isChangeOrderObject() || objectDescribeExt.enabledChangeOrder()) {
            return false;
        }
        if (objectDescribeExt.isSlaveObjectCreateWithMasterAndHiddenDetailButton()) {
            return false;
        }
        return serviceFacade.funPrivilegeCheck(controllerContext.getUser(), controllerContext.getObjectApiName(), ObjectAction.UPDATE.getActionCode());
    }

    protected List<IButton> queryBulkButton() {
        boolean enableMobileLayout = Optional.ofNullable(listComponentExt)
                .map(it -> it.<Boolean>getAttribute(ILayout.ENABLE_MOBILE_LAYOUT))
                .orElse(false);
        List<IButton> buttonList = serviceFacade.findListBatchButton(objectDescribeExt, enableMobileLayout,
                controllerContext.getUser());
        return handleButtonsByListLayout(buttonList, ButtonUsePageType.ListBatch);  // 按照管理后台布局配置的按钮显示做排序并设置外露, 不会过滤掉未配置按钮
    }

    protected List<String> getAuthorizedFields() {
        // 字段顺序，默认为默认布局顺序，老对象为配置文件中的顺序
        List<String> fieldList = getFiledList().stream().distinct().collect(Collectors.toList());
        objectDescribeExt.addDateTimeRangeEndTime(fieldList);

        OptionalFeaturesSwitchDTO optionalFeaturesSwitch = infraServiceFacade.findOptionalFeaturesSwitch(controllerContext.getTenantId(), objectDescribeExt);
        fieldList.removeIf(field -> !optionalFeaturesSwitch.getIsRelatedTeamEnabled() && ObjectDataExt.RELEVANT_TEAM.equals(field));

        //根据业务插件过滤字段
        filterFieldsByDomainPlugin(fieldList);

        List<String> orders = PrivilegeFieldsUtils.objectApiName2FiledOrderInfo.get(objectDescribeExt.getApiName());
        if (CollectionUtils.empty(orders)) {
            return fieldList;
        }
        return CollectionUtils.sortByGivenOrder(fieldList, orders, x -> x);
    }

    private void filterFieldsByDomainPlugin(List<String> fieldList) {
        if (CollectionUtils.empty(fieldList)) {
            return;
        }
        DomainPluginLogicService.FieldConfig fieldConfig = infraServiceFacade.findFieldConfigByDomainPlugin(controllerContext.getTenantId(),
                objectDescribeExt.getApiName(), null, getRecordTypes(), PageType.List.name());
        Set<String> hiddenFields = fieldConfig.getHiddenFields(objectDescribeExt.getApiName());
        if (CollectionUtils.notEmpty(hiddenFields)) {
            fieldList.removeAll(hiddenFields);
        }
    }

    private List<String> getShowFields() {
        if (showFields == null) {
            showFields = getAuthorizedFields();
            stopWatch.lap("getShowFields");
        }
        return Lists.newArrayList(showFields);
    }

    private List<String> getFiledList() {
        if (Objects.isNull(defaultLayout)) {
            return objectDescribeExt.getActiveFieldDescribes().stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
        }
        if (internalObjShowFieldsMap.containsKey(arg.getApiName())) {
            return Lists.newArrayList(internalObjShowFieldsMap.get(arg.getApiName()));
        }
        if (!flowInvisibleFieldNameMap.containsKey(arg.getApiName())) {
            return LayoutExt.of(defaultLayout).getFieldList();
        }
        return CollectionUtils.nullToEmpty(LayoutExt.of(defaultLayout).getFieldList()).stream()
                .filter(fieldName -> !flowInvisibleFieldNameMap.get(arg.getApiName()).contains(fieldName))
                .collect(Collectors.toList());
    }

    @SuppressWarnings("unchecked")
    private List<DocumentBaseEntity> getFieldWidth() {
        Map<String, Number> fieldWidth = getFieldWidthConfigs();

        return getShowFields().stream()
                .map(fieldName -> HeadField.fromFieldNameAndWidth(fieldName, fieldWidth.get(fieldName)))
                .map(headField -> new DocumentBaseEntity(headField.toMap()))
                .collect(Collectors.toList());
    }

    private Map<String, Number> getFieldWidthConfigs() {
        List<Tuple<String, Number>> fieldWidthConfig = infraServiceFacade.findFieldWidthConfig(controllerContext.getUser(), objectDescribeExt.getApiName(), arg.getExtendAttribute());
        if (CollectionUtils.empty(fieldWidthConfig)) {
            return Maps.newHashMap();
        }

        Map<String, Number> result = Maps.newHashMap();
        fieldWidthConfig.forEach(tuple -> result.putIfAbsent(tuple.getKey(), tuple.getValue()));
        return result;
    }

    private List<LayoutRuleExt.FieldConfig> getFieldConfigs() {
        if (Objects.nonNull(fieldConfigs)) {
            return fieldConfigs;
        }
        List<Map<String, Object>> fieldListConfig = infraServiceFacade.findFieldListConfig(controllerContext.getUser(),
                objectDescribeExt.getApiName(), arg.getExtendAttribute());
        return fieldConfigs = fieldListConfig.stream().map(LayoutRuleExt.FieldConfig::fromMap).collect(Collectors.toList());
    }

    protected ILayout findLayout(A arg) {
        if (isMobileLayout()) {
            if (AppFrameworkConfig.manyAbstractLayout(controllerContext.getObjectApiName(), controllerContext.getTenantId())) {
                //查默认布局
                abstractLayoutList = findMobileLayouts(objectDescribeExt);
                ILayout layout = abstractLayoutList.stream().filter(x -> BooleanUtils.isTrue(x.isDefault())).findFirst()
                        .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.DEFAULT_LAYOUT_NOT_EXIST)));

                //查根据业务类型分配的布局
                recordLayoutMapping = getMainRoleRecordLayoutMappingByRecordTypes(controllerContext.getUser(),
                        layout.getName(), objectDescribeExt.getApiName());
                //恢复旧的结构
                layout.setAgentType(ILayout.AGENT_TYPE_MOBILE);
                return layout;
            } else {
                List<ILayout> mobileLayouts = findMobileLayouts(objectDescribeExt);
                if (CollectionUtils.empty(mobileLayouts)) {
                    return null;
                }
                return mobileLayouts.get(0);
            }
        }
        if (Strings.isNullOrEmpty(getRecordTypeAPIName(arg))) {
            return defaultLayout;
        }
        return serviceFacade.getLayoutLogicService().findObjectLayoutWithType(buildLayoutContext(), getRecordTypeAPIName(arg),
                objectDescribeExt.getObjectDescribe(), arg.getLayoutType(), null);
    }

    private String getRecordTypeAPIName(A arg) {
        return templates.stream()
                .filter(x -> arg.isFindLayoutByTemplate())
                .filter(x -> Boolean.TRUE.equals(x.getIsDefault()))
                .filter(template -> !SearchTemplateExt.of(template).isUseFieldList())
                .findFirst()
                .map(ISearchTemplate::getRecordType)
                .orElse(arg.getRecordTypeAPIName());
    }

    protected List<ISearchTemplate> findTemplates() {
        List<ISearchTemplate> templates = serviceFacade.findTemplateByDescribeApiName(objectDescribeExt, arg.getExtendAttribute(),
                getShowFields(), getFieldConfigs(), controllerContext.getUser());
        // 根据模板名称过滤场景，没有配置场景则按之前的逻辑走
        String webSiteTemplateApi = arg.fetchWebSiteTemplateApi(listComponentExt);
        String searchTemplateApiName = StringUtils.firstNonBlank(arg.getTemplateApiName(), webSiteTemplateApi);
        if (!Strings.isNullOrEmpty(searchTemplateApiName)) {
            List<ISearchTemplate> searchTemplates = templates.stream()
                    .filter(it -> Objects.equals(searchTemplateApiName, it.getApiName()))
                    .collect(Collectors.toList());
            if (CollectionUtils.notEmpty(searchTemplates)) {
                return searchTemplates;
            }
        }
        List<ISearchTemplate> templateList = handleTemplatesByListLayout(templates);
        // 根据业务类型过滤场景
        if (!isSceneType(arg.getTemplateType())) {
            return templateList.stream().filter(this::filterTemplates).collect(Collectors.toList());
        }
        return templateList.stream().filter(x -> Objects.equals(arg.getTemplateType(), x.getType()))
                .filter(this::filterTemplates)
                .collect(Collectors.toList());
    }

    private List<ISearchTemplate> handleTemplatesByListLayout(List<ISearchTemplate> templates) {
        // 兼容之前提供的查询场景的 rest 接口，查询所有场景，而不是被布局过滤的场景
        if (arg.getLayoutAgentType() == null && arg.isOnlyTemplate()) {
            return templates;
        }
        // 下游不需要按列表页布局的配置，处理场景信息
        // 如果是互联站点的下游, 任需要处理(列表页布局存储在UIPAAS, 通过前端参数传回来, 和本企业逻辑一致)
        if (controllerContext.getUser().isOutUser() && !arg.fromWebSite()) {
            return templates;
        }
        if (listComponentExt == null) {
            return templates;
        }
        SearchTemplateLogicService templateLogicService = serviceFacade.getBean(SearchTemplateLogicService.class);
        CustomSceneConfig customSceneConfig = templateLogicService.getCustomSceneConfig(controllerContext.getUser(), controllerContext.getObjectApiName(), arg.getExtendAttribute());
        return listComponentExt.filterTemplate(templates, customSceneConfig, arg.getListType());
    }

    private boolean filterTemplates(ISearchTemplate x) {
        return StringUtils.isBlank(arg.getThirdRecordType()) ? Boolean.TRUE
                : (StringUtils.isBlank(x.getRecordType()) ? Boolean.TRUE
                : arg.getThirdRecordType().equals(x.getRecordType()));
    }

    protected List<ISearchTemplate> findBaseScenes() {
        return infraServiceFacade.findBaseSearchTemplates(objectDescribeExt.getApiName(), null,
                controllerContext.getUser());
    }

    protected List<IButton> getButtons() {
        ComponentActions listComponentActions = getListComponentActions();
        List<IButton> buttons = serviceFacade.getButtonByComponentActions(controllerContext.getUser(),
                listComponentActions, objectDescribeExt.getObjectDescribe(), null, false);
        List<IButton> resultButtons = appendListButtons(buttons);

        if (isGrayListLayout()) {
            return Optional.ofNullable(listComponentExt)    // 按照前台回传的 list_component (设计器的布局描述) 处理显影并排序
                    .map(it -> it.filterButtonByUsePage(resultButtons, ButtonUsePageType.ListNormal, arg.getListType()))
                    .orElse(resultButtons);
        }
        return resultButtons;
    }

    private ComponentActions getListComponentActions() {
        if (arg.fromWebSite()) {
            return ComponentActions.WEBSITE_LIST_PAGE_HEADER;
        }
        if (isMobileLayout()) {
            return ComponentActions.TERMINAL_LIST_PAGE;
        }
        return ComponentActions.LIST_PAGE_HEADER;
    }

    protected List<IButton> handleButtonsByListLayout(List<IButton> buttons, ButtonUsePageType usePageType) {
        if (listComponentExt == null) {
            return buttons;
        }
        return listComponentExt.filterButtonByUsePage(buttons, usePageType, arg.getListType());
    }

    private boolean isGrayListLayout() {
        return AppFrameworkConfig.isGrayListLayout(controllerContext.getTenantId(), objectDescribeExt.getApiName());
    }


    private List<IButton> appendListButtons(List<IButton> buttons) {
        if (!"list".equals(arg.getLayoutType())) {
            return buttons;
        }
        if (RequestUtil.isMobileRequestBeforeVersion(VERSION_715)) {
            return buttons;
        }
        if (arg.fromWebSite()) { // 站点中只有新建，不应该有别的列表页通用按钮, 故不需要在查询数据库的气态按钮
            return buttons;
        }
        List<IButton> listNormalButtons = serviceFacade.findButtonsByUsePageType(controllerContext.getUser(),
                objectDescribeExt, null, ButtonUsePageType.ListNormal, true);
        return CollectionUtils.addIfAbsent(buttons, listNormalButtons, (x, y) -> Objects.equals(x.getName(), y.getName()));
    }

    @Override
    protected void finallyDo() {
        try {
            if (result != null) {
                checkAndLogBulkButtons();
                finalProcessResult();
            }
            super.finallyDo();
        } finally {
            LayoutContext.remove();
        }
    }

    private void checkAndLogBulkButtons() {
        try {
            List<IButton> buttons = ButtonDocument.toLayoutButtonList(result.getButtons());
            if (CollectionUtils.empty(buttons)) {
                return;
            }
            if (buttons.stream().anyMatch(it -> Strings.isNullOrEmpty(it.getAction()))) {
                log.error("checkAndLogBulkButtons error, ei:{}, objectApiName:{}, buttons:{}",
                        controllerContext.getTenantId(), controllerContext.getObjectApiName(), JacksonUtils.toJson(result.getButtons()));
            }
        } catch (Exception e) {
            log.warn("checkAndLogBulkButtons error, ei:{}, objectApiName:{}", controllerContext.getTenantId(),
                    controllerContext.getObjectApiName(), e);
        }
    }

    private void finalProcessResult() {
        if (arg.isOnlyTemplate()) {
            return;
        }
        //因为很多子类在after方法中用到了result的layout和describe，只能最后再清空这两个属性
        if (!arg.includeLayout()) {
            result.setLayout(null);
        }
        if (!arg.includeDescribe()) {
            result.setObjectDescribe(null);
        } else {
            result.setObjectDescribe(ObjectDescribeDocument.handleDescribeCache(arg.getDescribeVersionMap(),
                    result.getObjectDescribe().toObjectDescribe()));
        }
        filterFieldsAttrInDescribe();
    }

    private void filterFieldsAttrInDescribe() {
        if (!AppFrameworkConfig.isSimpleDescribeGray(controllerContext.getTenantId(), controllerContext.getObjectApiName())) {
            return;
        }
        ObjectDescribeDocument objectDescribe = result.getObjectDescribe();
        if (Objects.isNull(objectDescribe)) {
            return;
        }
        IObjectDescribe copyDescribe = ObjectDescribeExt.of(objectDescribe.toObjectDescribe()).copyOnWrite();
        ObjectDescribeDocument document = ObjectDescribeDocument.of(copyDescribe);
        document.filterFieldsAttributes();
        result.setObjectDescribe(document);
    }

    private Map<String, String> getMainRoleRecordLayoutMappingByRecordTypes(User user, String defaultLayoutApiName, String describeApiName) {
        return serviceFacade.getMainRoleRecordLayoutMappingByRecordTypes(user, defaultLayoutApiName, describeApiName,
                layoutApiNames -> serviceFacade.getLayoutLogicService().findLayoutByApiNames(user.getTenantId(), layoutApiNames, describeApiName));
    }

    @Override
    protected final IObjectDescribe doGetObjectDescribe() {
        if (objectDescribeExt == null) {
            IObjectDescribe describe = findObject();
            objectDescribeExt = ObjectDescribeExt.of(describe);
        }
        return objectDescribeExt.getObjectDescribe();
    }

    @Override
    protected final Handler.Arg<A> buildHandlerArg(SimpleHandlerDescribe handlerDescribe) {
        ListHeaderHandler.Arg handlerArg = ListHeaderHandler.Arg.builder()
                .showFields(showFields)
                .layout(LayoutDocument.of(layoutExt))
                .listLayout(LayoutDocument.of(listLayout))
                .templates(QueryTemplateDocument.ofList(templates))
                .baseScenes(QueryTemplateDocument.ofList(baseScenes))
                .bulkButtonList(ButtonDocument.fromButtons(bulkButtonList))
                .build();
        return (Handler.Arg<A>) handlerArg;
    }

    @Override
    protected final void processHandlerResult(HandlerContext handlerContext, Handler.Arg<A> handlerArg, Handler.Result<Result> handlerResult) {
        if (handlerResult instanceof ListHeaderHandler.Result) {
            ListHeaderHandler.Result listHeaderResult = (ListHeaderHandler.Result) handlerResult;
            if (Objects.nonNull(listHeaderResult.getShowFields())) {
                this.showFields = listHeaderResult.getShowFields();
            }
            if (Objects.nonNull(listHeaderResult.getLayout())) {
                this.layoutExt = LayoutExt.of(listHeaderResult.getLayout());
            }
            if (Objects.nonNull(listHeaderResult.getListLayout())) {
                this.listLayout = LayoutExt.of(listHeaderResult.getListLayout());
            }
            if (Objects.nonNull(listHeaderResult.getTemplates())) {
                this.templates = QueryTemplateDocument.toSearchTemplateList(listHeaderResult.getTemplates());
            }
            if (Objects.nonNull(listHeaderResult.getBaseScenes())) {
                this.baseScenes = QueryTemplateDocument.toSearchTemplateList(listHeaderResult.getBaseScenes());
            }
            if (Objects.nonNull(listHeaderResult.getBulkButtonList())) {
                this.bulkButtonList = ButtonDocument.toLayoutButtonList(listHeaderResult.getBulkButtonList());
            }
        }
    }

    @Override
    protected final void registerHandlerExecuteFunctions(Map<String, HandlerFunctions.ExecuteFunction> executeFunctionMap) {
        executeFunctionMap.put("defaultListHeaderFindListLayoutHandler", this::doFindListLayout);
        executeFunctionMap.put("defaultListHeaderGetShowFieldsHandler", this::getShowFields);
        executeFunctionMap.put("defaultListHeaderFindTemplatesHandler", this::doFindTemplates);
        executeFunctionMap.put("defaultListHeaderFindOtherInfoHandler", this::doFindOtherInfo);
        executeFunctionMap.put("defaultListHeaderBuildResultHandler", () -> this.result = getResult());
    }

}
