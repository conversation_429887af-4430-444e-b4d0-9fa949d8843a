package com.facishare.paas.appframework.core.predef.action.uievent;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-08-12 16:07
 */
@Slf4j
public class DiffProcessor extends AbstractProcessor {

    /*
     * 屏蔽函数修改的字段类型
     */
    private static final Set<String> SHIELD_FIELD_TYPES = ImmutableSet.of(IFieldType.QUOTE, IFieldType.COUNT);

    private boolean diffMaster;  // 是否diff主对象数据
    private boolean diffDetail;  // 是否diff从对象数据
    private boolean shieldFieldsAfterFunction;   // 是否移除某些字段(禁用，引用，统计等), 原则上计算完成后不移除任何字段

    public DiffProcessor(ActionContainer container) {
        this(container, true, true);
    }

    public DiffProcessor(ActionContainer container, boolean diffMaster, boolean diffDetail) {
        super(container);
        this.diffMaster = diffMaster;
        this.diffDetail = diffDetail;
    }

    @Override
    public void invoke(UIEventProcess.ProcessRequest request, ProcessorContext context) {
        StopWatch stopWatch = StopWatch.create("DiffProcessor.invoke");
        // diff 主对象
        if (diffMaster) {
            IObjectData diffedMaster = diffMaster(container.getMasterData(), request.getMasterData());
            container.customHandleDiffedMaster(diffedMaster);
            request.setMasterWithOnlyChangedFields(diffedMaster);
            log.info("DiffProcessor master diff objectApiName:{}, result:{}", container.getObjectDescribe().getApiName(),
                    JacksonUtils.toJson(ObjectDataDocument.of(diffedMaster)));
            stopWatch.lap("diffMaster");
        }
        // diff 从对象
        if (diffDetail || request.isDoCalculate()) {
            Map<String, List<IObjectData>> diffedDetail =
                    diffDetail(container.getDetailData(), request.getDetailDataMap());
            container.customHandleDiffedDetail(diffedDetail);
            request.setDetailWithOnlyChangedFields(diffedDetail);
            diffedDetail.keySet().forEach(key -> {
                List<String> stringList = CollectionUtils.nullToEmpty(diffedDetail.get(key)).stream()
                        .filter(Objects::nonNull)
                        .map(x -> JacksonUtils.toJson(ObjectDataDocument.of(x)))
                        .collect(Collectors.toList());
                log.info("DiffProcessor detail diff objectApiName:{}, result:{}", key, stringList);
            });
            stopWatch.lap("diffDetail");
        }
        stopWatch.logSlow(500);
//        checkDateRangeFieldValue(request);
    }

    private void checkDateRangeFieldValue(UIEventProcess.ProcessRequest request) {
        IObjectData masterWithOnlyChangedFields = request.getMasterWithOnlyChangedFields();
        if (Objects.nonNull(masterWithOnlyChangedFields)) {
            List<String> errorMsg = ObjectDataExt.of(masterWithOnlyChangedFields).validateDateRangeField(objectDescribe);
            if (CollectionUtils.notEmpty(errorMsg)) {
                throw new ValidateException(errorMsg.get(0));
            }
        }
        if (CollectionUtils.notEmpty(detailObjectDescribeMap)) {
            CollectionUtils.nullToEmpty(request.getDetailWithOnlyChangedFields()).entrySet()
                    .stream()
                    .filter(entry -> detailObjectDescribeMap.containsKey(entry.getKey()))
                    .forEach(entry -> {
                        IObjectDescribe detailDescribe = detailObjectDescribeMap.get(entry.getKey());
                        List<IObjectData> detailDataList = entry.getValue();
                        CollectionUtils.nullToEmpty(detailDataList).forEach(detailData -> {
                            List<String> errMsg = ObjectDataExt.of(detailData).validateDateRangeField(detailDescribe);
                            if (CollectionUtils.notEmpty(errMsg)) {
                                throw new ValidateException(detailDescribe.getDisplayName() + errMsg.get(0));
                            }
                        });
                    });
        }
    }

    /*
     * diff 从对象
     * @return
     */
    private Map<String, List<IObjectData>> diffDetail(Map<String, List<IObjectData>> oldDetail,
                                                      Map<String, List<IObjectData>> newDetail) {
        Map<String, List<IObjectData>> result = Maps.newHashMap();
        final Map<String, IObjectDescribe> describeMap = container.getDetailDescribe();
        CollectionUtils.nullToEmpty(oldDetail).forEach((k, v) -> {
            if (!newDetail.containsKey(k)) {
                newDetail.put(k, Lists.newArrayList());
            }
        });

        newDetail.forEach((apiName, details) -> {
            if (Objects.isNull(describeMap.get(apiName))) {
                log.warn("detail object describe does not exist, apiName : {}, all detail object apiName : {}", apiName, describeMap.keySet());
                return;
            }
            List<IObjectData> oldDataList = oldDetail.get(apiName);
            List<IObjectData> diffList = Lists.newArrayList();
            for (IObjectData detail : details) {
                Object mark = detail.get(ObjectDataExt.MARK_API_NAME);
                // mark存在表示回填的从数据，否则为新增数据，不必diff
                if (Objects.nonNull(mark)) {
                    IObjectData oldData = oldDataList.stream()
                            .filter(d -> mark.equals(d.get(ObjectDataExt.MARK_API_NAME))).findFirst().get();
                    Map<String, Object> diffMap = ObjectDataExt.of(oldData).diff(detail,
                            describeMap.get(apiName), false, "UI_EVENT");
                    // 编辑时，使用前端数据覆盖业务类型字段，即从diffMap中将record_type删除
                    diffMap.remove(IFieldType.RECORD_TYPE);
                    // 删除禁用字段以及禁止函数修改的字段
                    removeFields(diffMap, describeMap.get(apiName));
                    // 从对象diff完之后补充mark(必须)
                    diffMap.put(ObjectDataExt.MARK_API_NAME, mark);
                    // 从对象补充单选多选other
                    dealSelectOther(diffMap, oldData);
                    diffList.add(ObjectDataExt.of(diffMap).getObjectData());
                } else {
                    diffList.add(detail);
                }
            }
            result.put(apiName, diffList);
        });
        return result;
    }

    /*
     * diff 主对象
     */
    private IObjectData diffMaster(IObjectData oldMasterData, IObjectData newMasterData) {
        // 如果新数据为null，直接返回老数据
        if (Objects.isNull(newMasterData)) {
            return oldMasterData;
        }
        filterRichTextNullValue(objectDescribe, oldMasterData);
        // 编辑时，使用前端数据覆盖业务类型字段，即从diffMap中将record_type删除
        Map<String, Object> diffMap = ObjectDataExt.of(oldMasterData).diff(newMasterData, objectDescribe, false, "UI_EVENT");
        if (Objects.nonNull(oldMasterData.getId())) {
            // 编辑时
            diffMap.remove(IFieldType.RECORD_TYPE);
            diffMap.remove(IObjectData.OWNER);
        }
        // 删除禁用字段以及屏蔽禁止函数修改的字段
        removeFields(diffMap, objectDescribe, Objects.isNull(oldMasterData.getId()));
        // 补充单选或者其它
        dealSelectOther(diffMap, oldMasterData);
        return ObjectDataExt.of(diffMap);
    }

    private void filterRichTextNullValue(IObjectDescribe objectDescribe, IObjectData oldMasterData) {
        ObjectDescribeExt.of(objectDescribe).getCooperativeRichTextFields().forEach(richText -> {
            Object oldValue = oldMasterData.get(richText.getApiName());
            if (Objects.isNull(oldValue) || StringUtils.isEmpty(oldValue.toString())) {
                return;
            }
            if (oldValue instanceof Map) {
                oldMasterData.set(richText.getApiName(), JSON.parseObject(JSON.toJSONString(oldValue)));
            }
        });
    }

    /*
     * 针对单选或者多选如果包含其它选项，字段api_name不变，但是
     * data 是前端传过来的原始数据
     */
    private void dealSelectOther(Map<String, Object> diffMap, IObjectData data) {
        Map<String, Object> map = Maps.newHashMap();
        diffMap.forEach((k, v) -> {
            if (k.endsWith("__o")) {
                String selectApi = k.substring(0, k.length() - "__o".length());
                if (!diffMap.containsKey(selectApi)) {
                    // 如果diff出来的map中不包含这个单选或者多选字段（即单选多选字段本身没变而other内容变了）才补充这个字段本身
                    map.put(selectApi, data.get(selectApi));
                }
            }
        });
        diffMap.putAll(map);

    }

    /*
     * 移除不需要下发给端上的字段
     */
    private void removeFields(Map<String, Object> diffMap, IObjectDescribe describe) {
        removeFields(diffMap, describe, false);
    }

    private void removeFields(Map<String, Object> diffMap, IObjectDescribe describe, boolean isCreate) {
        List<IFieldDescribe> fields = ObjectDescribeExt.of(describe).getFieldDescribes();
        for (IFieldDescribe field : fields) {
            if (needRemoveField(describe.getApiName(), describe.getTenantId(), field, isCreate)) {
                diffMap.remove(field.getApiName());
            }
        }
    }

    private boolean needRemoveField(String objApiName, String tenantId, IFieldDescribe fieldDescribe, boolean isCreate) {
        if (!fieldDescribe.isActive()) {
            return true;
        }
        if (IFieldType.MASTER_DETAIL.equals(fieldDescribe.getType())) {
            if (AppFrameworkConfig.uiEventSupportModifyMasterDetailFieldGray(tenantId, objApiName)
                    && StringUtils.equals(objectDescribe.getApiName(), objApiName) && isCreate) {
                return false;
            } else {
                return true;
            }
        }
        if (shieldFieldsAfterFunction && SHIELD_FIELD_TYPES.contains(fieldDescribe.getType())) {
            return true;
        }
        if (AppFrameworkConfig.getUiEventCurrencyGrayEi().contains(tenantId) || AppFrameworkConfig.getUiEventCurrencyGrayEi().contains(AppFrameworkConfig.ALL)) {
            if (FieldDescribeExt.of(fieldDescribe).isUIMultiCurrencyFields()) {
                return true;
            }
        } else if (FieldDescribeExt.of(fieldDescribe).isMultiCurrencyFields()) {
            return true;
        }
        // UI 事件不支持修改 创建企业 字段
        if (IObjectData.CREATE_ENTERPRISE.equals(fieldDescribe.getApiName())) {
            return true;
        }
        return false;
    }

    public static class Builder extends AbstractProcessor.Builder {
        private boolean diffMaster;
        private boolean diffDetail;
        private boolean shieldFieldsAfterFunction;

        public Builder(ActionContainer container) {
            super(container);
        }

        @Override
        public DiffProcessor build() {
            return new DiffProcessor(this);
        }

        public Builder setDiffMaster(boolean isDiff) {
            diffMaster = isDiff;
            return this;
        }

        public Builder setDiffDetail(boolean isDiff) {
            diffDetail = isDiff;
            return this;
        }

        public Builder setShieldFieldsAfterFunction(boolean shield) {
            this.shieldFieldsAfterFunction = shield;
            return this;
        }

    }

    private DiffProcessor(Builder builder) {
        super(builder);
        diffMaster = builder.diffMaster;
        diffDetail = builder.diffDetail;
        shieldFieldsAfterFunction = builder.shieldFieldsAfterFunction;
    }

}
