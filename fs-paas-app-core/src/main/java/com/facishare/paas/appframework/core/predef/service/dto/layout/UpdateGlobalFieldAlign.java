package com.facishare.paas.appframework.core.predef.service.dto.layout;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.fieldalign.GlobalFieldAlign;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2022/5/13
 */
public interface UpdateGlobalFieldAlign {

    @Data
    class Arg {
        @JsonProperty("global_field_align")
        private GlobalFieldAlignVO globalFieldAlign;
    }

    class Result {

    }

    @Data
    class GlobalFieldAlignVO {
        /**
         * left 左对齐
         * center 具中
         * top_and_bottom 上下
         */
        @JsonProperty("field_align")
        private String fieldAlign;
        /**
         * responsive 自适应布局
         * static 固定布局
         */
        @JsonProperty("detail_form_layout")
        private String detailFormLayout;
        /**
         * 是否开启多语分配字段布局
         */
        @JsonProperty("field_align_follow_lang")
        private boolean fieldAlignFollowLang;
        /**
         * 语种对应的字段布局
         */
        @JsonProperty("field_align_by_lang")
        private List<FieldLangAlignVO> fieldAlignByLang;

        public static GlobalFieldAlignVO fromGlobalFieldAlign(GlobalFieldAlign fieldAlign) {
            GlobalFieldAlign.FieldAlign align = fieldAlign.getFieldAlign();
            GlobalFieldAlignVO result = new GlobalFieldAlignVO();
            result.setFieldAlign(align.getType());
            result.setDetailFormLayout(fieldAlign.getDetailFormLayout());
            result.setFieldAlignFollowLang(BooleanUtils.isTrue(fieldAlign.getFieldAlignFollowLang()));
            result.setFieldAlignByLang(convert(fieldAlign.getFieldAlignByLangList()));

            return result;
        }

        private static List<FieldLangAlignVO> convert(List<GlobalFieldAlign.FieldLangAlign> fieldLangAligns) {
            if (CollectionUtils.empty(fieldLangAligns)) {
                return Lists.newArrayList();
            }
            return fieldLangAligns.stream()
                    .map(langAlign -> {
                        FieldLangAlignVO vo = new FieldLangAlignVO();
                        vo.lang = langAlign.getLang();
                        vo.fieldAlign = langAlign.getFieldAlign().getType();
                        return vo;
                    })
                    .collect(Collectors.toList());
        }
    }


    @Data
    class FieldLangAlignVO {
        @JsonProperty("lang")
        String lang;
        @JsonProperty("field_align")
        String fieldAlign;

        public FieldLangAlignVO() {
        }

        public FieldLangAlignVO(String lang, String fieldAlign) {
            this.lang = lang;
            this.fieldAlign = fieldAlign;
        }
    }
}
