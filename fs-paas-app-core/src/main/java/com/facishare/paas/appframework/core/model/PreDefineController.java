package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.common.util.UdobjSectionConfig;
import com.facishare.paas.appframework.core.exception.InitHandlerException;
import com.facishare.paas.appframework.core.model.domain.*;
import com.facishare.paas.appframework.core.model.handler.*;
import com.facishare.paas.appframework.core.model.plugin.*;
import com.facishare.paas.appframework.core.predef.controller.StandardController;
import com.facishare.paas.appframework.metadata.domain.SimpleDomainPluginDescribe;
import com.facishare.paas.appframework.metadata.handler.HandlerLogicService;
import com.facishare.paas.appframework.metadata.handler.HandlerType;
import com.facishare.paas.appframework.metadata.handler.SimpleHandlerDescribe;
import com.facishare.paas.appframework.metadata.layout.LayoutContext;
import com.facishare.paas.appframework.metadata.repository.model.HandlerDefinition;
import com.facishare.paas.appframework.metadata.repository.model.MtFunctionPluginConf;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.MoreObjects;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 预定义控制器
 * <p>
 * Created by liyiguang on 2017/7/6.
 */
public abstract class PreDefineController<A, R> extends AbstractController<A, R> {

    private static final Set<String> SUPPORT_DOMAIN_PLUGIN_CONTROLLERS = ImmutableSet.of(StandardController.DescribeLayout.name(),
            StandardController.List.name(), StandardController.RelatedList.name());

    protected StopWatch stopWatch = StopWatch.create(getClass().getSimpleName());

    protected ServiceFacade serviceFacade;
    protected InfraServiceFacade infraServiceFacade;
    protected List<ControllerListener<A, R>> controllerListeners = Lists.newArrayList();
    private boolean skipPluginAfter;

    private DomainPluginManager domainPluginManager;
    private List<SimpleDomainPluginDescribe> domainPluginDescribes;
    private final Map<String, String> domainPluginContextData = Maps.newHashMap();

    private List<String> funcPrivilegeCodes;
    private HandlerLogicService handlerLogicService;
    private HandlerManager handlerManager;
    private List<SimpleHandlerDescribe> handlerDescribes;
    private HandlerContext handlerContext;
    private final Map<String, String> handlerContextData = Maps.newHashMap();
    private Map<String, HandlerFunctions.ExecuteFunction> handlerExecuteFunctions;

    private PluginManager pluginManager;
    private MtFunctionPluginConf pluginConfig;

    protected abstract List<String> getFuncPrivilegeCodes();

    protected List<String> getRecordTypes() {
        return null;
    }

    protected void doInitBefore() {

    }

    protected void doInit() {

    }

    @Override
    protected void before(A arg) {
        doInitBefore();
        controllerListeners.forEach(x -> x.before(this.controllerContext, arg));
        stopWatch.lap("doControllerBeforeListener");
        doFunPrivilegeCheck();
        stopWatch.lap("doFunPrivilegeCheck");
        doInit();
    }

    @Override
    protected final void pluginBefore(A arg) {
        // TODO L: 内存缓存, fast notify 更新数据
        if (UdobjSectionConfig.getFunctionPluginConfig().getExecutedModuleName().contains(controllerContext.getMethodName())) {
            pluginConfig = serviceFacade.getFunctionPluginConfLogicService().findAvailableRuntime(controllerContext.getUser()
                    , controllerContext.getObjectApiName(), controllerContext.getMethodName()).orElse(null);
        }
        runPlugin(ControllerPlugin.BEFORE, true, (plugin, pluginArg) ->
                ((ControllerPlugin) plugin).before(buildPluginContext(), pluginArg));
    }

    @Override
    protected final void pluginAfter(A arg, R result) {
        if (skipPluginAfter()) {
            return;
        }
        runPlugin(ControllerPlugin.AFTER, true, (plugin, pluginArg) ->
                ((ControllerPlugin) plugin).after(buildPluginContext(), pluginArg));
    }

    @Override
    protected R after(A arg, R result) {
        stopWatch.lap("before-doControllerAfterListener");
        controllerListeners.forEach(x -> x.after(this.controllerContext, arg, result));
        stopWatch.lap("doControllerAfterListener");

        return result;
    }

    @Override
    protected void finallyDo() {
        try {
            stopWatch.logSlow(500);
        } finally {
            LayoutContext.remove();
        }
    }

    protected final void runPlugin(String method, boolean needProcessResult, PluginFunction.RunFunction runFunction) {
        PluginRunner.builder()
                .pluginManager(pluginManager)
                .needProcessResult(needProcessResult)
                .tenantId(controllerContext.getTenantId())
                .requestType(RequestType.Controller)
                .objectApiName(controllerContext.getObjectApiName())
                .requestCode(controllerContext.getMethodName())
                .method(method)
                .agentType(getAgentType())
                .buildArgFunction((plugin, methodName) -> {
                    if (plugin instanceof APLControllerPlugin) {
                        return buildAPLPluginArg(methodName);
                    }
                    return buildObjectPluginArg(methodName);
                })
                .runFunction(runFunction)
                .processResultFunction((plugin, methodName, pluginArg, pluginResult) -> {
                    if (Objects.isNull(pluginResult)) {
                        return;
                    }
                    if (plugin instanceof APLControllerPlugin) {
                        processAPLPluginResult(methodName, pluginArg, pluginResult);
                        return;
                    }
                    processObjectPluginResult(methodName, pluginArg, pluginResult);
                })
                .pluginConfig(pluginConfig)
                .build()
                .run();
        stopWatch.lap("runPlugin_" + method);
    }

    protected String getAgentType() {
        return null;
    }

    protected Plugin.Arg buildAPLPluginArg(String method) {
        return null;
    }

    protected Plugin.Arg buildObjectPluginArg(String method) {
        return null;
    }

    protected void processAPLPluginResult(String method, Plugin.Arg pluginArg, Plugin.Result pluginResult) {
        APLControllerPlugin.Result aplPluginResult = (APLControllerPlugin.Result) pluginResult;
        switch (method) {
            case APLControllerPlugin.BEFORE:
                if (!aplPluginResult.supportAfter()) {
                    needSkipPluginAfter();
                }
                mergeAPLPluginArg(aplPluginResult);
                break;
            case APLControllerPlugin.AFTER:
                mergeAPLPluginResult(aplPluginResult);
                break;
            default:
        }
    }

    protected void mergeAPLPluginResult(APLControllerPlugin.Result aplPluginResult) {

    }

    protected void mergeAPLPluginArg(APLControllerPlugin.Result aplPluginResult) {

    }

    protected void processObjectPluginResult(String method, Plugin.Arg pluginArg, Plugin.Result pluginResult) {
    }

    protected APLControllerPlugin.TriggerInfo buildTriggerInfo() {
        APLControllerPlugin.TriggerInfo triggerInfo = new APLControllerPlugin.TriggerInfo();
        triggerInfo.setTriggerObject(controllerContext.getObjectApiName());
        return triggerInfo;
    }

    protected PluginContext buildPluginContext() {
        return PluginContext.fromControllerContext(controllerContext);
    }

    protected boolean skipPluginAfter() {
        return skipPluginAfter;
    }

    protected void needSkipPluginAfter() {
        skipPluginAfter = true;
    }

    @Override
    protected final void domainPluginBefore(A arg) {
        if (!supportDomainPlugin()) {
            return;
        }
        //查询插件信息
        domainPluginDescribes = findDomainPluginDescribes();
        stopWatch.lap("findDomainPluginDescribes");

        if (skipDomainPlugin(ControllerDomainPlugin.BEFORE)) {
            return;
        }

        runDomainPlugin(ControllerDomainPlugin.BEFORE, true, (domainPlugin, pluginArg) ->
                ((ControllerDomainPlugin) domainPlugin).before(controllerContext, pluginArg));
    }

    @Override
    protected final void domainPluginAfter(A arg, R result) {
        if (!supportDomainPlugin()) {
            return;
        }
        if (skipDomainPlugin(ControllerDomainPlugin.AFTER)) {
            return;
        }
        runDomainPlugin(ControllerDomainPlugin.AFTER, true, (domainPlugin, pluginArg) ->
                ((ControllerDomainPlugin) domainPlugin).after(controllerContext, pluginArg));
    }

    protected final void runDomainPlugin(String methodName, boolean needProcessResult, DomainPluginFunction.RunFunction runFunction) {
        DomainPluginRunner.builder()
                .domainPluginManager(domainPluginManager)
                .pluginDescribes(domainPluginDescribes)
                .method(methodName)
                .requestType(RequestType.Controller)
                .requestCode(controllerContext.getMethodName())
                .buildArgFunction((method, recordTypeList) -> {
                    DomainPlugin.Arg pluginArg = buildDomainPluginArg(method, recordTypeList);
                    pluginArg.setObjectApiName(controllerContext.getObjectApiName());
                    pluginArg.setContextData(this.domainPluginContextData);
                    pluginArg.setDoActComplete(this.isDoActComplete());
                    pluginArg.setProcessComplete(this.isProcessComplete());
                    pluginArg.setActByHandler(supportHandler());
                    return pluginArg;
                })
                .runFunction(runFunction)
                .processResultFunction((method, pluginArg, pluginResult) -> {
                    if (Objects.isNull(pluginResult)) {
                        return;
                    }
                    if (CollectionUtils.notEmpty(pluginResult.getContextData())) {
                        this.domainPluginContextData.putAll(pluginResult.getContextData());
                    }
                    if (needProcessResult) {
                        processDomainPluginResult(method, pluginArg, pluginResult);
                    }
                })
                .build()
                .run();
        stopWatch.lap("runDomainPlugin_" + methodName);
    }

    private List<SimpleDomainPluginDescribe> findDomainPluginDescribes() {
        List<String> recordTypeList = getRecordTypes();
        return infraServiceFacade.findSimplePluginByActionCode(controllerContext.getTenantId(), controllerContext.getObjectApiName(),
                controllerContext.getMethodName(), recordTypeList, RequestType.Controller);
    }

    protected DomainPlugin.Arg buildDomainPluginArg(String method, List<String> recordTypeList) {
        return null;
    }

    protected void processDomainPluginResult(String method, DomainPlugin.Arg pluginArg, DomainPlugin.Result pluginResult) {
    }

    private boolean supportDomainPlugin() {
        return SUPPORT_DOMAIN_PLUGIN_CONTROLLERS.contains(controllerContext.getMethodName());
    }

    protected boolean skipDomainPlugin(String method) {
        return false;
    }

    @Override
    protected final void handlerInit() {
        initHandlers();
        doInitBefore();
        controllerListeners.forEach(x -> x.before(controllerContext, arg));
        stopWatch.lap("beforeListener");
        this.funcPrivilegeCodes = getFuncPrivilegeCodes();
    }

    private void initHandlers() {
        try {
            handlerDescribes = handlerLogicService.findHandlerDescribeByInterfaceCode(controllerContext.getTenantId(),
                    controllerContext.getObjectApiName(), controllerContext.getMethodName());
            stopWatch.lap("findHandlerDescribes");
            //如果查不到handler，属于异常情况，抛异常出去
            if (CollectionUtils.empty(handlerDescribes)) {
                throw new InitHandlerException("handlerDescribes is empty");
            }
            //没有平台预置的handler，属于异常情况，抛异常出去
            if (handlerDescribes.stream().noneMatch(x -> HandlerDefinition.PROVIDER_TYPE_SYSTEM.equals(x.getProviderType()))) {
                throw new InitHandlerException("system handlerDescribes is empty");
            }
            this.handlerContext = buildHandlerContext();
            this.handlerExecuteFunctions = getHandlerExecuteFunctions();
        } catch (InitHandlerException e) {
            throw e;
        } catch (Throwable e) {
            throw new InitHandlerException(e);
        }
    }

    private HandlerContext buildHandlerContext() {
        return HandlerContext.builder()
                .requestContext(controllerContext.getRequestContext())
                .interfaceCode(controllerContext.getMethodName())
                .build();
    }

    private Map<String, HandlerFunctions.ExecuteFunction> getHandlerExecuteFunctions() {
        Map<String, HandlerFunctions.ExecuteFunction> executeFunctionMap = Maps.newHashMap();
        registerHandlerExecuteFunctions(executeFunctionMap);
        executeFunctionMap.put(VirtualControllerHandlers.PROCESS_ARG, this::doInit);
        executeFunctionMap.put(VirtualControllerHandlers.PLUGIN_BEFORE, () -> pluginBefore(arg));
        executeFunctionMap.put(VirtualControllerHandlers.DOMAIN_PLUGIN_BEFORE, () -> domainPluginBefore(arg));
        executeFunctionMap.put(VirtualControllerHandlers.PLUGIN_AFTER, () -> pluginAfter(arg, result));
        executeFunctionMap.put(VirtualControllerHandlers.DOMAIN_PLUGIN_AFTER, () -> domainPluginAfter(arg, result));
        return ImmutableMap.copyOf(executeFunctionMap);
    }

    protected void registerHandlerExecuteFunctions(Map<String, HandlerFunctions.ExecuteFunction> executeFunctionMap) {

    }

    @Override
    protected final void handlerBefore() {
        executeHandler(HandlerType.BEFORE.getCode());
    }

    @Override
    protected final void handlerDoAct() {
        executeHandler(HandlerType.ACT.getCode());
    }

    @Override
    protected final void handlerAfter() {
        controllerListeners.forEach(x -> x.after(controllerContext, arg, result));
        stopWatch.lap("afterListener");
        executeHandler(HandlerType.AFTER.getCode());
    }

    @Override
    protected final void handlerFinallyDo() {
        executeHandler(HandlerType.FINALLY.getCode());
        finallyDo();
    }

    private void executeHandler(String handlerType) {
        HandlerExecutor.builder()
                .handlerManager(handlerManager)
                .handlerContext(handlerContext)
                .handlerDescribeList(handlerDescribes)
                .handlerType(handlerType)
                .interfaceCode(controllerContext.getMethodName())
                .objectApiName(controllerContext.getObjectApiName())
                .industryCode(getIndustryCode(arg))
                .buildArgFunction(this::getHandlerArg)
                .processResultFunction((handlerContext, handlerArg, handlerResult) -> {
                    if (Objects.isNull(handlerResult)) {
                        return;
                    }
                    preProcessHandlerResult(handlerResult);
                    processHandlerResult(handlerContext, handlerArg, handlerResult);
                })
                .executeFunctions(this.handlerExecuteFunctions)
                .build()
                .execute();
        stopWatch.lap("executeHandler_" + handlerType);
    }

    protected String getIndustryCode(A arg) {
        return null;
    }

    protected Handler.Arg<A> buildHandlerArg(SimpleHandlerDescribe handlerDescribe) {
        return null;
    }

    protected void processHandlerResult(HandlerContext handlerContext, Handler.Arg<A> handlerArg, Handler.Result<R> handlerResult) {

    }

    protected final Handler.Arg<A> getHandlerArg(SimpleHandlerDescribe handlerDescribe) {
        Handler.Arg<A> handlerArg = buildHandlerArg(handlerDescribe);
        processHandlerArg(handlerArg, handlerDescribe);
        return handlerArg;
    }

    private void processHandlerArg(Handler.Arg<A> handlerArg, SimpleHandlerDescribe handlerDescribe) {
        handlerArg.setContextData(this.handlerContextData);
        handlerArg.setHandlerDescribe(handlerDescribe);
        handlerArg.setObjectApiName(this.controllerContext.getObjectApiName());
        handlerArg.setObjectDescribe(doGetObjectDescribe());
        handlerArg.setInterfaceArg(this.arg);
        handlerArg.setFunctionPrivilegeCodes(this.funcPrivilegeCodes);
        handlerArg.setDoActComplete(isDoActComplete());
        handlerArg.setProcessComplete(isProcessComplete());
        if (handlerArg instanceof ControllerHandler.Arg) {
            ((ControllerHandler.Arg<A, R>) handlerArg).setInterfaceResult(this.result);
        }
    }

    protected IObjectDescribe doGetObjectDescribe() {
        return null;
    }

    private void preProcessHandlerResult(Handler.Result<R> handlerResult) {
        if (CollectionUtils.notEmpty(handlerResult.getContextData())) {
            this.handlerContextData.putAll(handlerResult.getContextData());
        }
        if (Objects.nonNull(handlerResult.getInterfaceResult())) {
            this.result = handlerResult.getInterfaceResult();
        }
        if (Objects.nonNull(handlerResult.getSkipFunctionPrivilegeCheck())) {
            this.handlerContext.setAttribute(HandlerAttributes.SKIP_FUNCTION_PRIVILEGE_CHECK, handlerResult.getSkipFunctionPrivilegeCheck());
        }
    }

    public List<Class<? extends ControllerListener<A, R>>> getControllerListenerClassList() {
        return Lists.newArrayList();
    }

    public void addControllerListener(ControllerListener<A, R> controllerListener) {
        this.controllerListeners.add(controllerListener);
    }

    protected void doFunPrivilegeCheck() {
        serviceFacade.doFunPrivilegeCheck(controllerContext.getUser(), controllerContext.getObjectApiName(), getFuncPrivilegeCodes());
    }

    public final void setServiceFacade(ServiceFacade serviceFacade) {
        this.serviceFacade = serviceFacade;
    }

    public final void setInfraServiceFacade(InfraServiceFacade infraServiceFacade) {
        this.infraServiceFacade = infraServiceFacade;
        SpringBeanHolder springBeanHolder = infraServiceFacade.getSpringBeanHolder();
        this.handlerManager = springBeanHolder.getHandlerManager();
        this.handlerLogicService = springBeanHolder.getHandlerLogicService();
        this.pluginManager = springBeanHolder.getPluginManager();
        this.domainPluginManager = springBeanHolder.getDomainPluginManager();
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("argClass", argClass).add("arg", arg).toString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        PreDefineController<?, ?> that = (PreDefineController<?, ?>) o;
        return Objects.equals(argClass, that.argClass) && Objects.equals(arg, that.arg);
    }

    @Override
    public int hashCode() {
        return Objects.hash(argClass, arg);
    }
}
