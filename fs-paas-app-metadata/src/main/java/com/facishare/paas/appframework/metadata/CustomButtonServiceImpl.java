package com.facishare.paas.appframework.metadata;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.config.PartnerStatusConfig;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.flow.ApprovalFlowService;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.quota.TenantLicenseInfo;
import com.facishare.paas.appframework.license.util.LicenseConstants.ModuleCode;
import com.facishare.paas.appframework.license.util.ModulePara;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.button.ButtonURL;
import com.facishare.paas.appframework.metadata.button.FilterButtonsManager;
import com.facishare.paas.appframework.metadata.button.FilterButtonsProvider;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderLogicService;
import com.facishare.paas.appframework.metadata.config.*;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.expansion.DescribeExpansionRender;
import com.facishare.paas.appframework.metadata.expansion.DescribeExtra;
import com.facishare.paas.appframework.metadata.gdpr.GdprService;
import com.facishare.paas.appframework.metadata.layout.ButtonFilter;
import com.facishare.paas.appframework.metadata.layout.LayoutAgentType;
import com.facishare.paas.appframework.metadata.layout.LayoutContext;
import com.facishare.paas.appframework.metadata.options.SelectFieldDependenceLogicService;
import com.facishare.paas.appframework.metadata.repository.model.GdprLegalBase;
import com.facishare.paas.appframework.metadata.util.TranslateI18nUtils;
import com.facishare.paas.appframework.privilege.EnterpriseRelationLogicService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeServiceImpl;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.api.service.IUdefButtonService;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.UdefButtonPGMapper;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.paas.appframework.common.util.AppFrameworkConfig.isEnableCheckEnterpriseResourcesQuote;
import static com.facishare.paas.appframework.common.util.ObjectAction.*;
import static com.facishare.paas.appframework.core.util.RequestUtil.VERSION_875;


/**
 * Created by linqy on 2018/01/11
 */
@Slf4j
@Service("customButtonService")
public class CustomButtonServiceImpl implements CustomButtonService {

    @Autowired
    private IUdefButtonService buttonService;
    @Autowired
    private CustomButtonService customButtonService;
    @Autowired
    private LogService logService;
    @Autowired
    private DescribeLogicServiceImpl describeLogicService;
    @Autowired
    private FunctionPrivilegeServiceImpl functionPrivilegeService;
    @Autowired
    private JobScheduleService jobScheduleService;
    @Autowired
    private UdefButtonPGMapper udefButtonPGMapper;
    @Autowired
    private LicenseService licenseService;
    @Autowired
    private GdprService gdprService;
    @Autowired
    private EnterpriseRelationLogicService enterpriseRelationLogicService;
    @Autowired
    private OptionalFeaturesService optionalFeaturesService;
    @Autowired
    private FilterButtonsManager findButtonsByDescribeApiNameManager;
    @Autowired
    private ObjectConvertRuleService objectConvertRuleService;
    @Autowired
    private ObjectControlLevelLogicService objectControlLevelLogicService;

    @Autowired
    private MetaDataMiscService metaDataMiscService;
    @Autowired
    private ApprovalFlowService approvalFlowService;
    @Autowired
    private SelectFieldDependenceLogicService selectFieldDependenceLogicService;
    @Resource
    private I18nSettingServiceImpl i18nService;
    @Resource
    private ChangeOrderLogicService changeOrderLogicService;

    @Override
    public IUdefButton createCustomButton(User user, IUdefButton button) {
        checkParam(button);
        try {
            IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), button.getDescribeApiName());
            button.setTenantId(user.getTenantId());
            button.setCreatedBy(user.getUserId());
            button.setLastModifiedBy(user.getUserId());
            IUdefButton result = buttonService.create(button);
            logService.log(user, EventType.ADD, ActionType.CREATE_BUTTON, button.getDescribeApiName(),
                    I18N.text(I18NKey.BUTTON_OBJECT, button.getLabel(), objectDescribe.getDisplayName()));
            return result;
        } catch (MetadataServiceException e) {
            log.warn("createCustomButton error,user:{},button:{}", user, button.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    // 翻译按钮新增的参数
    private void synButtonParamTrans(User user, List<IUdefButton> buttons, IObjectDescribe objectDescribe) {
        if (CollectionUtils.empty(buttons) || Objects.isNull(user) || Objects.isNull(objectDescribe)) {
            return;
        }
        String objApiName = objectDescribe.getApiName();
        String tenantId = user.getTenantId();
        if (StringUtils.isBlank(objApiName) || StringUtils.isBlank(tenantId)) {
            return;
        }
        Map<String, String> keyToNewName = Maps.newHashMap();
        buttons.forEach(button -> {
            if (Objects.isNull(button)) {
                return;
            }
            ButtonExt buttonExt = ButtonExt.of(button);
            String buttonApiName = button.getApiName();
            if (StringUtils.isBlank(buttonApiName) || CollectionUtils.empty(buttonExt.getParamForm())) {
                return;
            }
            buttonExt.getParamForm().stream().forEach(param -> {
                ParamForm paramForm = ParamForm.of(param);
                if (paramForm.isButtonNewField()) {
                    String fieldApiName = paramForm.getApiName();
                    keyToNewName.put(TranslateI18nUtils.getButtonParamKey(tenantId, objApiName, buttonApiName, fieldApiName),
                            paramForm.getLabel());
                }

            });
        });
        i18nService.saveTransValue(keyToNewName, true);
    }

    @Override
    public IUdefButton updateCustomButton(User user, IUdefButton button) {
        checkParam(button);
        try {
            IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), button.getDescribeApiName());
            IUdefButton defButton = findButtonByApiName(user, button.getApiName(), button.getDescribeApiName());
            if (defButton == null) {
                throw new ValidateException(I18N.text(I18NKey.BUTTON_OBJECT));
            }
            synButtonParamTrans(user, Collections.singletonList(button), objectDescribe);
            button.setTenantId(user.getTenantId());
            button.setLastModifiedBy(user.getUserId());
            IUdefButton result = buttonService.update(button);
            logService.log(user, EventType.MODIFY, ActionType.UPDATE_BUTTON, objectDescribe.getApiName(),
                    I18N.text(I18NKey.BUTTON_OBJECT, button.getLabel(), objectDescribe.getDisplayName()));
            return result;
        } catch (MetadataServiceException e) {
            log.warn("updateCustomButton error,user:{},button:{}", user, button.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public boolean deleteCustomButton(User user, String buttonApiName, String describeApiName) {
        IUdefButton button = findButtonByApiName(user, buttonApiName, describeApiName);
        if (button == null || button.isDeleted()) {
            throw new MetaDataBusinessException(I18N.text(I18NKey.BUTTON_NOT_EXIST_OR_DELETED));
        }

        IObjectDescribe objectDescribe = null;
        try {
            objectDescribe = describeLogicService.findObjectIncludeDeleted(user.getTenantId(), button.getDescribeApiName());
        } catch (ObjectDefNotFoundError e) {
            log.warn("deleteCustomButton but object not found,user:{},buttonApiName:{},describeApiName:{}", user, buttonApiName, describeApiName);
        }
        String objectDisplayName = Objects.isNull(objectDescribe) ? describeApiName : objectDescribe.getDisplayName();
        boolean result = buttonService.deleteUdefButton(buttonApiName, describeApiName, user.getTenantId());
        logService.log(user, EventType.DELETE, ActionType.DELETE_BUTTON, describeApiName,
                I18N.text(I18NKey.BUTTON_OBJECT, button.getLabel(), objectDisplayName));

        return result;
    }

    @Override
    public IUdefButton findButtonByApiNameForDesigner(User user, String buttonApiName, String describeApiName) {
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), describeApiName);
        IUdefButton button = findByApiNameAndTenantId(user, buttonApiName, objectDescribe);
        if (button == null) {
            return null;
        }
        ButtonExt buttonExt = ButtonExt.of(button);
        // 预置按钮补充按钮创建时间为对象描述创建时间
        if (buttonExt.isSystemButton()) {
            button.setCreateTime(objectDescribe.getCreateTime());
            button.setCreatedBy(objectDescribe.getCreatedBy());
            // 最后修改时间为空时，使用描述的创建人为最后修改人，描述的创建时间为最后修改时间
            if (Objects.isNull(button.getLastModifiedTime())) {
                button.setLastModifiedBy(objectDescribe.getCreatedBy());
                button.setLastModifiedTime(objectDescribe.getCreateTime());
            }
        }
        // 判断是否购买多语资源包
        Map<String, Boolean> existModule = licenseService.existModule(user.getTenantId(), Sets.newHashSet(ModuleCode.MULTI_LANGUAGE_APP));
        if (existModule.get(ModuleCode.MULTI_LANGUAGE_APP)) {
            buttonExt.fillButtonLabel(objectDescribe.getApiName());
        }
        buttonExt.fillIsShowWhenLock();
        return button;
    }


    @Override
    public IUdefButton findButtonByApiName(User user, String buttonApiName, String describeApiName) {
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), describeApiName);
        return findButtonByApiName(user, buttonApiName, objectDescribe);
    }

    @Override
    public IUdefButton findButtonByApiName(User user, String buttonApiName, IObjectDescribe objectDescribe) {
        IUdefButton button = findByApiNameAndTenantId(user, buttonApiName, objectDescribe);
        if (button == null) {
            return null;
        }
        ButtonExt buttonExt = ButtonExt.of(button);
        // 预置按钮补充按钮创建时间为对象描述创建时间
        if (buttonExt.isSystemButton()) {
            button.setCreateTime(objectDescribe.getCreateTime());
            button.setCreatedBy(objectDescribe.getCreatedBy());
            // 最后修改时间为空时，使用描述的创建人为最后修改人，描述的创建时间为最后修改时间
            if (Objects.isNull(button.getLastModifiedTime())) {
                button.setLastModifiedBy(objectDescribe.getCreatedBy());
                button.setLastModifiedTime(objectDescribe.getCreateTime());
            }
        }
        // 判断是否购买多语资源包
        Map<String, Boolean> existModule = licenseService.existModule(user.getTenantId(), Sets.newHashSet(ModuleCode.MULTI_LANGUAGE_APP));
        if (existModule.get(ModuleCode.MULTI_LANGUAGE_APP)) {
            buttonExt.fillButtonLabel(objectDescribe.getApiName());
        }
        buttonExt.fillIsShowWhenLock();
        return button;
    }

    private IUdefButton findByApiNameAndTenantId(User user, String buttonApiName, IObjectDescribe objectDescribe) {
        IUdefButton button = buttonService.findByApiNameAndTenantId(buttonApiName, objectDescribe.getApiName(), user.getTenantId());
        transButtonParam(user, Collections.singletonList(button), objectDescribe);
        dealButtonTownInfo(objectDescribe, button);
        IUdefButton result = convertAndGenerateButton(button, buttonApiName, objectDescribe, user);
        if (Objects.nonNull(button)) {
            correctFieldByDescribe(objectDescribe, Lists.newArrayList(button));
        }
        return result;
    }

    private void dealButtonTownInfo(IObjectDescribe objectDescribe, IUdefButton button) {
        if (button == null) {
            return;
        }
        List<IParamForm> paramForms = ParamForm.fromList(button.getParamForm());
        //按钮中配了国家省市区并且到区一级才会处理乡镇
        List<String> districtParams = paramForms.stream()
                .filter(x -> Objects.equals(objectDescribe.getApiName(), x.getObjectApiName()))
                .filter(x -> IFieldType.DISTRICT.equals(x.getType()))
                .map(IParamForm::convertToFieldApiName)
                .collect(Collectors.toList());
        if (CollectionUtils.empty(districtParams)) {
            return;
        }
        List<String> groupFieldApiNames = paramForms.stream()
                .filter(x -> Objects.equals(objectDescribe.getApiName(), x.getObjectApiName()))
                .filter(x -> IFieldType.GROUP.equals(x.getType()))
                .map(IParamForm::convertToFieldApiName)
                .collect(Collectors.toList());

        if (CollectionUtils.empty(groupFieldApiNames)) {
            return;
        }
        List<String> townVillageApiNames = ObjectDescribeExt.of(objectDescribe).getGroupFields().stream()
                .filter(x -> groupFieldApiNames.contains(x.getApiName()) && x instanceof Area)
                .map(Area.class::cast)
                .flatMap(x -> Stream.of(
                        BooleanUtils.isTrue(x.getIsSupportTown()) ? x.getAreaTownFieldApiName() : null,
                        BooleanUtils.isTrue(x.getIsSupportVillage()) ? x.getAreaVillageFieldApiName() : null
                ))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtils.empty(townVillageApiNames)) {
            return;
        }
        List<String> townVillageParams = paramForms.stream()
                .filter(x -> Objects.equals(objectDescribe.getApiName(), x.getObjectApiName()))
                .filter(x -> IFieldType.TOWN.equals(x.getType()) || IFieldType.VILLAGE.equals(x.getType()))
                .map(IParamForm::convertToFieldApiName)
                .collect(Collectors.toList());
        townVillageApiNames.forEach(fieldApiName -> {
            if (townVillageParams.contains(fieldApiName)) {
                return;
            }
            ObjectDescribeExt.of(objectDescribe)
                    .getFieldDescribeSilently(fieldApiName)
                    .ifPresent(field -> button.getParamForm().add(generateTownVillageParaForm(field)));
        });

    }

    private Map<String, Object> generateTownVillageParaForm(IFieldDescribe fieldDescribe) {
        IParamForm paramForm = new ParamForm();
        paramForm.setApiName(fieldDescribe.getApiName());
        paramForm.setObjectApiName(fieldDescribe.getDescribeApiName());
        paramForm.setLabel(fieldDescribe.getLabel());
        paramForm.setUsedIn(fieldDescribe.getUsedIn());
        paramForm.setIsRequired(fieldDescribe.isRequired());
        paramForm.setType(fieldDescribe.getType());
        return paramForm.toMap();
    }

    private IUdefButton convertAndGenerateButton(IUdefButton button, String buttonApiName, IObjectDescribe objectDescribe, User user) {
        // 灰度了批量打印的企业，需要处理按钮的 usePage
        if (ButtonConfig.isGrayBulkPrint(user.getTenantId())) {
            Optional.ofNullable(button).ifPresent(it -> ButtonExt.of(it).handlePrintButtonUsePage());
        }
        //是否需要生成按钮
        if (!needGenerateButton(user.getTenantId(), objectDescribe, buttonApiName)) {
            return button;
        }
        //BD没有，生成一个按钮
        if (!isInTenantDb(button)) {
            return ButtonExt.convertAndGenerateButton(buttonApiName, objectDescribe, user);
        }
        return button;
    }

    private boolean needGenerateButton(String tenantId, IObjectDescribe describe, String buttonApiName) {
        String describeApiName = describe.getApiName();
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        return isAddOrEditButton(tenantId, describeApiName, buttonApiName)
                || isRecoverButton(tenantId, describeApiName, buttonApiName)
                || isChangeOrEffectiveButton(describeExt, buttonApiName)
                || isTransFormOrReferenceCreateButton(tenantId, describeApiName, buttonApiName)
                || isStartBpmOrStartStagePropellorButton(tenantId, buttonApiName)
                || isFollowAndUnFollowButton(tenantId, describeApiName, buttonApiName);
    }

    private boolean isFollowAndUnFollowButton(String tenantId, String describeApiName, String buttonApiName) {
        if (Objects.equals(FOLLOW.getButtonApiName(), buttonApiName) || Objects.equals(UNFOLLOW.getButtonApiName(), buttonApiName)) {
            return UdobjGrayUtil.isObjectAndTenantGray(UdobjGrayKey.FOLLOW_GRAY, tenantId, describeApiName);
        }
        return false;
    }

    private boolean isStartBpmOrStartStagePropellorButton(String tenantId, String buttonApiName) {
        if (Objects.equals(START_BPM.getButtonApiName(), buttonApiName)
                || Objects.equals(START_STAGE_PROPELLOR.getButtonApiName(), buttonApiName)) {
            return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FLOW_BUTTON_GRAY, tenantId);
        }
        return false;
    }

    private boolean isTransFormOrReferenceCreateButton(String tenantId, String describeApiName, String buttonApiName) {
        if (Objects.equals(TRANSFORM.getButtonApiName(), buttonApiName)
                || Objects.equals(REFERENCE_CREATE.getButtonApiName(), buttonApiName)) {
            return objectConvertRuleService.count(User.systemUser(tenantId), describeApiName, null)
                    || objectConvertRuleService.count(User.systemUser(tenantId), null, describeApiName);
        }
        return false;
    }

    private boolean isChangeOrEffectiveButton(ObjectDescribeExt describeExt, String buttonApiName) {
        if (describeExt.isSlaveObject()) {
            return false;
        }
        if (describeExt.enabledChangeOrder() && Objects.equals(CHANGE.getButtonApiName(), buttonApiName)) {
            return true;
        }
        return describeExt.isChangeOrderObject() && Objects.equals(EFFECTIVE.getButtonApiName(), buttonApiName);
    }

    private boolean isAddOrEditButton(String tenantId, String describeApiName, String buttonApiName) {
        return AppFrameworkConfig.isAddEditUIActionGray(tenantId, describeApiName)
                && ButtonExt.ADD_EDIT_BUTTON_API_NAME.contains(buttonApiName);
    }

    private boolean isRecoverButton(String tenantId, String describeApiName, String buttonApiName) {
        return AppFrameworkConfig.isGrayRecoverButton(tenantId, describeApiName)
                && ObjectAction.RECOVER.getButtonApiName().equals(buttonApiName);
    }

    private boolean isInTenantDb(IUdefButton button) {
        if (Objects.isNull(button)) {
            return false;
        }
        if (StringUtils.isAnyBlank(button.getTenantId(), button.getId())) {
            return false;
        }
        return udefButtonPGMapper.setTenantId(button.getTenantId()).countById(button.getTenantId(), button.getId()) > 0;
    }

    @Override
    public List<IUdefButton> findButtonList(User user, String describeApiName) {
        return findButtonList(user, describeApiName, false);
    }

    /**
     * @param user
     * @param describeApiName
     * @param checkPartnerStatus 校验是否开启合作伙伴
     * @return
     */
    public List<IUdefButton> findButtonList(User user, String describeApiName, boolean checkPartnerStatus) {
        StopWatch stopWatch = StopWatch.create("findButtonList");
        try {
            IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), describeApiName);
            stopWatch.lap("findObject");
            List<IUdefButton> customButton = findButtonsByDescribeApiName(user, objectDescribe);
            stopWatch.lap("findButtonsByDescribeApiName");
            List<IUdefButton> result = filterAndProcessCustomButtons(user, objectDescribe, checkPartnerStatus, customButton);   // 过滤并排序
            stopWatch.lap("filterAndProcessCustomButtons");
            return result;
        } finally {
            stopWatch.logSlow(100);
        }
    }

    private List<IUdefButton> filterAndProcessCustomButtons(User user, IObjectDescribe objectDescribe, boolean checkPartnerStatus, List<IUdefButton> customButton) {
        String describeApiName = objectDescribe.getApiName();
        customButton.stream().map(ButtonExt::of).filter(ButtonExt::isSystemButton).forEach(button -> {
            button.setCreateTime(objectDescribe.getCreateTime());
            button.setCreatedBy(objectDescribe.getCreatedBy());
        });
        // 屏蔽线索一转三的转换按钮
        customButton.removeIf(it -> ButtonExt.LEADS_CONVERT_BUTTON_API_NAME.contains(it.getApiName()) && ButtonExt.of(it).isConvert());
        ButtonExt.filterButtons(objectDescribe, customButton);
        // 详情页通过功能权限过滤，可以不校验合作伙伴是否开启
        if (!checkPartnerStatus) {
            return ButtonConfig.sortedByDefaultButtonOrder(customButton, describeApiName);
        }
        filterPartnerButtons(user, objectDescribe, customButton);
        return ButtonConfig.sortedByDefaultButtonOrder(customButton, describeApiName);
    }

    private void filterPartnerButtons(User user, IObjectDescribe objectDescribe, List<IUdefButton> customButton) {
        Set<String> removeButtons = Sets.newHashSet();
        // 没有开通互联，移除更换负责人按钮
        if (!enterpriseRelationLogicService.supportInterconnectBaseAppLicense(user.getTenantId())) {
            removeButtons.add(CHANGE_PARTNER_OWNER.getButtonApiName());
        }
        // 只有开启了合作伙伴才需要展示合作伙伴相关的按钮
        if (!ObjectDescribeExt.of(objectDescribe).isPRMEnabled()
                || !ButtonConfig.isSupportPRM(objectDescribe)
                || !PartnerStatusConfig.getInstance().isOpenPartner(user)) {
            removeButtons.add(CHANGE_PARTNER.getButtonApiName());
            removeButtons.add(DELETE_PARTNER.getButtonApiName());
        }
        if (CollectionUtils.notEmpty(removeButtons)) {
            customButton.removeIf(button -> removeButtons.contains(button.getApiName()));
        }
    }

    @Override
    public List<IButton> filterFunPrivilege(User user, IObjectDescribe describe, List<IUdefButton> buttonList) {
        List<IButton> customButtonList = buttonList.stream()
                .map(x -> ButtonExt.of(x).toButton()).collect(Collectors.toList());

        //功能权限过滤
        customButtonList = ButtonFilter.builder()
                .functionPrivilegeService(functionPrivilegeService)
                .buttons(customButtonList)
                .user(user)
                .objectDescribeExt(ObjectDescribeExt.of(describe))
                .build()
                .filterByFunctionPrivilege()
                .getButtons();
        return customButtonList;
    }

    @Override
    public List<IUdefButton> findButtonsByLastModifiedTime(User user, long lastUpdateTime) {
        List<IUdefButton> list = buttonService.findByLastModifiedTime(user.getTenantId(),
                IFieldDescribe.DEFINE_TYPE_SYSTEM, lastUpdateTime);

        return CollectionUtils.nullToEmpty(list);
    }

    private List<IUdefButton> fillDefaultButton(User user, String describeApiName, List<IUdefButton> buttonList, IObjectDescribe objectDescribe) {

        List<IUdefButton> defaultButtonList = Lists.newArrayList();
        if (!Objects.equals(describeApiName, ObjectDescribeExt.PERSONNEL_OBJ_API_NAME)) {
            fillIfNotExist(user, buttonList, defaultButtonList, ButtonExt.DEFAULT_ADD_BUTTON_API_NAME, objectDescribe);
            fillIfNotExist(user, buttonList, defaultButtonList, ObjectAction.CREATE_SAVE.getButtonApiName(), objectDescribe);
        }
        fillIfNotExist(user, buttonList, defaultButtonList, ButtonExt.DEFAULT_EDIT_BUTTON_API_NAME, objectDescribe);
        fillIfNotExist(user, buttonList, defaultButtonList, ObjectAction.UPDATE_SAVE.getButtonApiName(), objectDescribe);

        defaultButtonList.addAll(buttonList);
        return defaultButtonList;
    }

    private void fillIfNotExist(User user, List<IUdefButton> buttonList,
                                List<IUdefButton> defaultButtonList, String buttonApiName, IObjectDescribe objectDescribe) {
        Optional<IUdefButton> anyAdd = buttonList.stream()
                .filter(a -> Objects.equals(a.getApiName(), buttonApiName))
                .findAny();
        if (!anyAdd.isPresent()) {
            defaultButtonList.add(ButtonExt.getDefaultButton(user, buttonApiName, objectDescribe));
        } else {
            defaultButtonList.add(anyAdd.get());
            buttonList.remove(anyAdd.get());
        }
    }

    @Override
    public boolean updateStatus(User user, String buttonApiName, String describeApiName, boolean isActive) {
        IUdefButton button = findButtonByApiName(user, buttonApiName, describeApiName);
        if (button == null || button.isDeleted()) {
            throw new MetaDataBusinessException(I18N.text(I18NKey.BUTTON_NOT_EXIST_OR_DELETED));
        }
        //isActive 为 true 代表启用按钮
        if (!ButtonExt.of(button).isSystemButton() && isActive
                && isEnableCheckEnterpriseResourcesQuote(user.getTenantId())) {
            // 启用按钮校验按钮数量
            List<IUdefButton> buttonList = customButtonService.findButtonList(user, button.getDescribeApiName()).stream()
                    .filter(x -> !ButtonExt.of(x).isSystemButton())
                    .collect(Collectors.toList());
            int size = buttonList.size();
            customButtonService.checkCustomButtonCountLimit(user, describeApiName, --size);
        }
        button.setLastModifiedBy(user.getUserId());

        IObjectDescribe objectDescribe = null;
        try {
            objectDescribe = describeLogicService.findObjectIncludeDeleted(user.getTenantId(), button.getDescribeApiName());
        } catch (ObjectDefNotFoundError e) {
            log.warn("updateCustomButtonStatus but object not found,user:{},buttonApiName:{},describeApiName:{}", user, buttonApiName, describeApiName);
            if (isActive) {
                throw e;
            }
        }
        String objectDisplayName = Objects.isNull(objectDescribe) ? describeApiName : objectDescribe.getDisplayName();
        boolean result = buttonService.changeUdefButtonStatus(buttonApiName, describeApiName, user.getTenantId(), isActive, ActionContextExt.of(user).getContext());
        logService.log(user, EventType.MODIFY, ActionType.CHANGE_BUTTON_STATUS, describeApiName,
                I18N.text(I18NKey.BUTTON_OBJECT, button.getLabel(), objectDisplayName));
        return result;

    }

    @Override
    public List<IUdefButton> filterButtonsForUsePageType(User user, IObjectData objectData, IObjectDescribe describe, String usePageType, List<IUdefButton> buttonList, Set<String> readonlyFields) {
        List<IUdefButton> buttons = Lists.newArrayList(buttonList);
        if (CollectionUtils.empty(buttons)) {
            return Lists.newArrayList();
        }
        buttons.removeIf(a -> CollectionUtils.notEmpty(a.getUsePages()) && !a.getUsePages().contains(usePageType));
        if (CollectionUtils.empty(buttons)) {
            return Lists.newArrayList();
        }

        //从对象和特殊对象的按钮过滤
        ButtonExt.filterButtons(describe, buttons);

        // 不是详情页按钮，直接返回
        if (!ButtonUsePageType.Detail.getId().equals(usePageType)) {
            return buttons;
        }

        if (objectData == null) {
            return buttons;
        }

        if (ObjectDataExt.of(objectData).isLock()) {
            buttons.removeIf(it -> !ButtonExt.of(it).isShowWhenLock());
        }

        List<IUdefButton> ret = ButtonExt.filterButtonsWheres(objectData, describe, buttons);
        Map<String, IObjectFieldExtra> describeExtMap = findDescribeExtra(describe, user, ret);
        ret.forEach(button -> ButtonExt.of(button).handleButtonParam(user, objectData, describe, describeExtMap, readonlyFields, false));
        //按钮参数去掉需要展示掩码的字段
        removeMaskFieldInButtonParamForm(user, objectData, describe, buttonList);
        return ret;
    }

    private void removeMaskFieldInButtonParamForm(User user, IObjectData objectData, IObjectDescribe describe, List<IUdefButton> buttonList) {
        Set<String> fieldsInParamForm = Sets.newHashSet();
        List<IUdefButton> buttonsHasFieldParam = Lists.newArrayList();
        buttonList.stream().filter(x -> CollectionUtils.notEmpty(x.getParamForm())).forEach(x -> {
            buttonsHasFieldParam.add(x);
            ParamForm.fromList(x.getParamForm()).stream()
                    .filter(y -> describe.getApiName().equals(y.getObjectApiName()))
                    .forEach(y -> fieldsInParamForm.add(y.convertToFieldApiName()));

        });
        List<IFieldDescribe> maskFields = ObjectDescribeExt.of(describe).getFieldByApiNames(Lists.newArrayList(fieldsInParamForm))
                .stream().filter(x -> FieldDescribeExt.of(x).isShowMask()).collect(Collectors.toList());
        if (CollectionUtils.empty(maskFields)) {
            return;
        }
        buttonsHasFieldParam.forEach(x -> {
            List<Map> paramForm = x.getParamForm();
            paramForm.removeIf(y -> describe.getApiName().equals(ParamForm.of(y).getObjectApiName())
                    && ObjectDataExt.of(objectData).containsField(FieldDescribeExt.getShowFieldName(ParamForm.of(y).convertToFieldApiName())));
            x.setParamForm(paramForm);
        });
    }

    @Override
    public List<IUdefButton> filterButtonsForCreate(User user, String describeApiName) {
        List<IUdefButton> buttonList = findButtonList(user, describeApiName);
        buttonList.removeIf(a -> !CollectionUtils.nullToEmpty(a.getUsePages()).contains(ButtonUsePageType.Create.getId()));
        return buttonList;
    }

    @Override
    public List<IUdefButton> filterButtonsForEdit(User user, String describeApiName) {
        List<IUdefButton> buttonList = findButtonList(user, describeApiName);
        buttonList.removeIf(a -> !CollectionUtils.nullToEmpty(a.getUsePages()).contains(ButtonUsePageType.Edit.getId()));
        return buttonList;
    }

    @Override
    public Map<String, List<IUdefButton>> findButtonByApiNameListAndType(User user, List<String> apiNameList, String buttonType) {
        Map<String, List<IUdefButton>> buttonMap = buttonService.findButtonsByDescribeApiNamesAndType(
                user.getTenantId(), apiNameList, buttonType);
        return CollectionUtils.empty(buttonMap) ? Maps.newHashMap() : buttonMap;
    }

    @Override
    public IUdefButtonConfig findButtonConfigByApiName(String describeApiName, String buttonApiName, User user) {
        if (Strings.isNullOrEmpty(buttonApiName)) {
            return null;
        }
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), describeApiName);
        IUdefButton button = findByApiNameAndTenantId(user, buttonApiName, describe);
        if (Objects.isNull(button)) {
            return null;
        }
        return findButtonConfig(user, describe, button);
    }

    public List<IUdefButtonConfig> findButtonConfigListByApiName(User user, String describeApiName, List<IUdefButton> buttonList) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CONFIGURATION_PACKAGE, user.getTenantId())) {
            return ButtonExt.findButtonConfig(buttonList);
        }
        List<ObjectControlLevelLogicService.ObjectControlLevelInfo> objectControlLevelInfos = objectControlLevelLogicService.queryControlLevel(
                user, describeApiName, ControlLevelResourceType.BUTTON);
        return buttonList.stream()
                .map(button -> {
                    IUdefButtonConfig buttonConfig = ButtonExt.of(button).generateButtonConfig();
                    return generateAndMergeButtonConfig(button, buttonConfig, objectControlLevelInfos);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private IUdefButtonConfig findButtonConfig(User user, IObjectDescribe describe, IUdefButton button) {
        IUdefButtonConfig buttonConfig = ButtonExt.of(button).generateButtonConfigByBulkConfig(describe, user);
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CONFIGURATION_PACKAGE, user.getTenantId())) {
            return buttonConfig;
        }
        List<ObjectControlLevelLogicService.ObjectControlLevelInfo> objectControlLevelInfos = objectControlLevelLogicService.queryControlLevel(
                user, describe.getApiName(), ControlLevelResourceType.BUTTON);
        return generateAndMergeButtonConfig(button, buttonConfig, objectControlLevelInfos);
    }

    private IUdefButtonConfig generateAndMergeButtonConfig(IUdefButton button, IUdefButtonConfig buttonConfig, List<ObjectControlLevelLogicService.ObjectControlLevelInfo> objectControlLevelInfos) {
        if (CollectionUtils.empty(objectControlLevelInfos)) {
            return buttonConfig;
        }
        for (ObjectControlLevelLogicService.ObjectControlLevelInfo objectControlLevelInfo : objectControlLevelInfos) {
            String buttonApiName = objectControlLevelInfo.getPrimaryKey();
            String describeApiName = objectControlLevelInfo.getParentFieldValue();
            if (!Objects.equals(buttonApiName, button.getApiName()) || !Objects.equals(describeApiName, button.getDescribeApiName())) {
                continue;
            }
            if (Objects.isNull(buttonConfig)) {
                buttonConfig = new UdefButtonConfig();
                buttonConfig.setDescribeApiName(describeApiName);
                buttonConfig.setApiName(buttonApiName);
            }
            buttonConfig.setControlLevel(objectControlLevelInfo.getControlLevel());
            return buttonConfig;
        }
        return buttonConfig;
    }

    @Override
    public List<IUdefButton> findCustomButtonByUsePage(String describeApiName, String usePage, User user) {
        List<IUdefButton> customButton = findButtonsByDescribeApiName(user, describeApiName);
        List<IUdefButton> udefButtons = customButton.stream()
                .filter(IUdefButton::isActive)
                .filter(x -> x.getUsePages().contains(usePage))
                .collect(Collectors.toList());
        if (ButtonUsePageType.DataList.getId().equals(usePage)) {
            Set<String> buttonNames = AppFrameworkConfig.listSingleButtonGray(user.getTenantId(), describeApiName);
            Set<String> supportButtonApiNames = AppFrameworkConfig.getAllObjectSingleButtonGray(user.getTenantId());
            if (CollectionUtils.notEmpty(buttonNames) && !CollectionUtils.isEqual(buttonNames, supportButtonApiNames)) {
                udefButtons.removeIf(button -> ButtonExt.of(button).isSystemButton() && !buttonNames.contains(button.getApiName()));
            }
        }
        return CollectionUtils.sortByGivenOrder(udefButtons, AppFrameworkConfig.getDefaultButtonOrder(), IUdefButton::getDescribeApiName);
    }

    @Override
    public List<IUdefButton> findCustomButtonByUsePageIncludeDisable(String describeApiName, String usePage, User user) {
        List<IUdefButton> customButton = findButtonsByDescribeApiName(user, describeApiName);
        List<IUdefButton> udefButtons = customButton.stream()
                .filter(x -> x.getUsePages().contains(usePage))
                .collect(Collectors.toList());
        return CollectionUtils.sortByGivenOrder(udefButtons, AppFrameworkConfig.getDefaultButtonOrder(), IUdefButton::getDescribeApiName);
    }

    @Override
    public List<IButton> findListBatchButton(IObjectDescribe describe, boolean enableMobileLayout, User user) {
        List<IButton> buttonList = findButtons(describe, ButtonUsePageType.ListBatch, user, true, enableMobileLayout);
        if (gdprService.findGdprComplianceStatusByCache(user, describe.getApiName())) {
            //开启gdpr的对象默认补充一个更换法律基础的按钮，并且放在按钮第一个
            IButton button = createButton(ObjectAction.UPDATE_GDPR.getBulkButtonApiName(), I18N.text(I18NKey.GDPR_BUTTON_DEFAULT), ObjectAction.UPDATE_GDPR.getButtonApiName());
            Map selectOneFieldDescribe = buildSelectOneFields();
            button.set("param_form", Lists.newArrayList(selectOneFieldDescribe));
            buttonList.add(0, button);
        }
        return buttonList;
    }

    public Map buildSelectOneFields() {
        SelectOneFieldDescribe selectOneFieldDescribe = new SelectOneFieldDescribe();
        selectOneFieldDescribe.setApiName(GdprLegalBase.GDPR_LEGAL_BASE);
        List<ISelectOption> selectOptions = Lists.newArrayList();
        SelectOption selectOption1 = new SelectOption();
        selectOption1.setLabel(I18N.text(I18NKey.GDPR_LEGAL_BASE_AGREE));
        selectOption1.setValue("agree");
        selectOptions.add(selectOption1);
        SelectOption selectOption2 = new SelectOption();
        selectOption2.setLabel(I18N.text(I18NKey.GDPR_LEGAL_BASE_LEGAL_BASIS));
        selectOption2.setValue("legalBasis");
        selectOptions.add(selectOption2);
        SelectOption selectOption3 = new SelectOption();
        selectOption3.setLabel(I18N.text(I18NKey.GDPR_LEGAL_BASE_CONTRACT));
        selectOption3.setValue("contract");
        selectOptions.add(selectOption3);
        SelectOption selectOption4 = new SelectOption();
        selectOption4.setLabel(I18N.text(I18NKey.GDPR_LEGAL_BASE_LEGAL_OBLIGATION));
        selectOption4.setValue("legalObligation");
        selectOptions.add(selectOption4);
        SelectOption selectOption5 = new SelectOption();
        selectOption5.setLabel(I18N.text(I18NKey.GDPR_LEGAL_BASE_IMPORTANT_INTERESTS));
        selectOption5.setValue("importantInterests");
        selectOptions.add(selectOption5);
        SelectOption selectOption6 = new SelectOption();
        selectOption6.setLabel(I18N.text(I18NKey.GDPR_LEGAL_BASE_COMMON_INTEREST));
        selectOption6.setValue("commonInterest");
        selectOptions.add(selectOption6);
        selectOneFieldDescribe.setSelectOptions(selectOptions);
        return selectOneFieldDescribe.getContainerDocument();
    }


//    public List<IButton> processGdprButton(IObjectDescribe describe, User user, List<IButton> buttonList, ButtonUsePageType usePageType) {
//        //处理gdpr相关逻辑
//        Optional<GdprCompliance> userGdpr = gdprService.findGdprComplianceData(user);
//        if (userGdpr.isPresent()) {
//            GdprCompliance gdprCompliance = userGdpr.get();
//            if (gdprCompliance.isOpenStatus()
//                    && CollectionUtils.notEmpty(gdprCompliance.getApiNames())
//                    && gdprCompliance.getApiNames().contains(describe.getApiName())) {
//
//                if (ButtonUsePageType.ListBatch.equals(usePageType)) {
//                    //开启gdpr的对象默认补充一个更换法律基础的按钮，并且放在按钮第一个
//                    buttonList.add(0, createButton(ObjectAction.UPDATE_GDPR.getActionCode(), I18N.text(I18NKey.GDPR_BUTTON_DEFAULT), ObjectAction.UPDATE_GDPR.getButtonApiName()));
//                }
//
//                if (CollectionUtils.notEmpty(gdprCompliance.getUnusableOperation()) &&
//                        gdprCompliance.getUnusableOperation().contains(ObjectAction.BATCH_EXPORT.getActionCode())) {
//                    buttonList.removeIf(x -> ObjectAction.BATCH_EXPORT.getDefaultButtonApiName().equals(x.getName())
//                            || ObjectAction.EXPORT_FILE.getDefaultButtonApiName().equals(x.getName()));
//                }
//            }
//        }
//        return buttonList;
//    }

    private List<IButton> findButtons(IObjectDescribe describe, ButtonUsePageType usePage, User user, boolean includeSystemButton, boolean enableMobileLayout) {
        // 微信小程序，灰度下发新建页UI按钮
        /* 这里不判断了、新建、编辑、列表页批量都下发按钮
        if (RequestUtil.isMobileOrH5Request() && user.isOutUser() && !isWXMiniProgram(user.getTenantId())) {
            // 移动端需要
            if (!((ButtonUsePageType.Edit == usePage || ButtonUsePageType.Create == usePage)
                    && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OUTER_MOBILE_EDIT_UI_ACTION_GRAY, user.getTenantId()))) {
                return Lists.newArrayList();
            }
        }
        */
        if (RequestUtil.isMobileOrH5Request() && ButtonConfig.isMobileBulkButtonBlackActionObject(describe.getApiName())) {
            return Lists.newArrayList();
        }
        Set<String> readonlyFields = functionPrivilegeService.getReadonlyFields(user, describe.getApiName());
        List<IUdefButton> udefButtons = findCustomButtonByUsePage(describe.getApiName(), usePage.getId(), user);    // 从db查询按钮并排序 (DataList特殊处理)
        Map<String, IObjectFieldExtra> extraHashMap = findDescribeExtra(describe, user, udefButtons);
        udefButtons.stream().map(ButtonExt::of).forEach(buttonExt -> buttonExt.handleButtonParam(user, null, describe, extraHashMap, readonlyFields, false));
        ButtonExt.filterButtons(describe, udefButtons);
        // 设置显示字段开关
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CUSTOM_BUTTON_FIND_DESCRIBE_EXTRA_GRAY, user.getTenantId())) {
            fillDisplayNameSwitch(user, udefButtons);
        }
        List<IButton> buttons = appendButtonsByUsePage(describe, udefButtons, usePage); // 通过 ObjectAction 将 IUdefButton 转化为 IButton, 补充配置文件中配置的需要生成的系统按钮(increment), 并排序
        //功能权限过滤
        List<IButton> buttonList = ButtonFilter.builder()
                .functionPrivilegeService(functionPrivilegeService)
                .buttons(buttons)
                .user(user)
                .objectDescribeExt(ObjectDescribeExt.of(describe))
                .enableMobileLayout(enableMobileLayout)
                .build()
                .filterByFunctionPrivilege()
                .filterByClientInfo(usePage)
                .filterBulkButtonByPRM(usePage)
                .includeSystemButton(includeSystemButton)
                .handleActionCode(usePage)
                .getButtons();
        ButtonExt.filterButtonsByBlacklist(buttonList, describe.isBigObject());
        if (Lists.newArrayList(Utils.ACCOUNT_API_NAME, Utils.CONTACT_API_NAME, Utils.LEADS_API_NAME).contains(describe.getApiName()) &&
                usePage.equals(ButtonUsePageType.ListBatch)) {
            //线索、客户、联系人特殊处理列表页按钮添加到市场活动
            Map<String, Map<String, Boolean>> objApiNameAndActionCodePrivilegeMapping =
                    functionPrivilegeService.batchFunPrivilegeCheck(user,
                            Lists.newArrayList(Utils.CAMPAIGN_MEMBERS_OBJ_API_NAME, Utils.MARKETING_EVENT_API_NAME),
                            Lists.newArrayList(ObjectAction.VIEW_LIST.getActionCode(), ObjectAction.CREATE.getActionCode()));
            if (!objApiNameAndActionCodePrivilegeMapping.isEmpty() &&
                    objApiNameAndActionCodePrivilegeMapping.get(Utils.MARKETING_EVENT_API_NAME).get(ObjectAction.VIEW_LIST.getActionCode()) &&
                    objApiNameAndActionCodePrivilegeMapping.get(Utils.CAMPAIGN_MEMBERS_OBJ_API_NAME).get(ObjectAction.CREATE.getActionCode())) {
                buttonList.add(createButton("AddCampaignMembers",
                        I18N.text("sfa.udobj.action.saveasobject",
                                I18N.text(Utils.MARKETING_EVENT_API_NAME + ".attribute.self.display_name")),
                        "AddCampaignMembers_button_default"));
            }
        }

        if (RequestUtil.isMobileOrH5Request() && Utils.LEADS_API_NAME.equals(describe.getApiName())) {
            //手机端批量按钮移除合并
            buttonList.removeIf(x -> x.getName().equals("Merge_button_default"));
        }

        return buttonList;
    }

    private Map<String, IObjectFieldExtra> findDescribeExtra(IObjectDescribe describe, User user, List<IUdefButton> buttonList) {
        DescribeExtra describeExtra;
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CUSTOM_BUTTON_FIND_DESCRIBE_EXTRA_GRAY, user.getTenantId())) {
            CustomButtonDescribeExtraOption customButtonDescribeExtraOption = findCustomButtonDescribeExtraOption(describe, buttonList);
            describeExtra = DescribeExpansionRender.builder()
                    .user(user)
                    .fieldDependence(customButtonDescribeExtraOption.isFieldDependence())
                    .addExtProperty(customButtonDescribeExtraOption.isAddExtProperty())
                    .fillOptionalFeatures(false)
                    .describeLogicService(describeLogicService)
                    .selectFieldDependenceLogicService(selectFieldDependenceLogicService)
                    .describeCacheable(true)
                    .build()
                    .render(describe, Collections.emptyList());
        } else {
            describeExtra = describeLogicService.findDescribeExtra(user, describe);
        }

        return Optional.ofNullable(describeExtra)
                .map(it -> it.getDescribeExtra(describe.getApiName()))
                .orElseGet(Maps::newHashMap);
    }

    private CustomButtonDescribeExtraOption findCustomButtonDescribeExtraOption(IObjectDescribe describe, List<IUdefButton> buttonList) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        boolean needAddExtProperty = buttonList.stream()
                .flatMap(it -> ParamForm.fromList(it.getParamForm()).stream())
                .filter(it -> Objects.equals(describeExt.getApiName(), it.getObjectApiName()))
                .map(IParamForm::convertToFieldApiName)
                .anyMatch(fieldName -> needAddExtProperty(describeExt, fieldName));

        boolean fieldDependence = buttonList.stream().anyMatch(button -> {
            List<IParamForm> paramForms = ParamForm.fromList(button.getParamForm());
            Map<String, FieldDescribeExt> fieldMap = paramForms.stream()
                    .filter(paramForm -> Objects.equals(describeExt.getApiName(), paramForm.getObjectApiName()))
                    .map(IParamForm::convertToFieldApiName)
                    .map(fieldName -> describeExt.getFieldDescribeSilently(fieldName)
                            .map(FieldDescribeExt::of)
                            .filter(fieldDescribeExt -> fieldDescribeExt.isSelectOne() || fieldDescribeExt.isSelectMany()).orElse(null))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(FieldDescribeExt::getApiName, Function.identity()));

            return fieldMap.values().stream()
                    .flatMap(it -> it.getCascadeParentApiNames().stream())
                    .anyMatch(fieldMap::containsKey);
        });

        return CustomButtonDescribeExtraOption.builder()
                .addExtProperty(needAddExtProperty)
                .fieldDependence(fieldDependence)
                .build();
    }

    private boolean needAddExtProperty(ObjectDescribeExt describeExt, String fieldName) {
        return describeExt.getFieldDescribeSilently(fieldName)
                .map(FieldDescribeExt::of)
                .filter(it -> it.isObjectReferenceManyField() || it.isDepartmentManyField() || it.isEmployeeManyField())
                .isPresent();
    }

    private boolean isWXMiniProgram(String tenantId) {
        return RequestUtil.isWXMiniProgram();
    }

    private void fillDisplayNameSwitch(User user, List<IUdefButton> buttons) {
        buttons.stream().filter(button -> CollectionUtils.notEmpty(button.getParamForm()))
                .forEach(button -> setDisplayNameSwitch(user, button));
    }

    private void setDisplayNameSwitch(User user, IUdefButton button) {
        button.getParamForm().stream().map(FieldDescribeFactory::newInstance)
                .filter(fieldDescribe -> FieldDescribeExt.of(fieldDescribe).isRefObjectField())
                .forEach(field -> {
                    IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), FieldDescribeExt.of(field).getRefObjTargetApiName());
                    field.set(IObjectDescribe.IS_OPEN_DISPLAY_NAME, ObjectDescribeExt.of(objectDescribe).isSupportDisplayName());
                });
    }

    private List<IButton> appendButtonsByUsePage(IObjectDescribe describe, List<IUdefButton> udefButtons, ButtonUsePageType usePage) {
        if (ButtonUsePageType.ListBatch == usePage) {
            return ButtonConfig.generateByListBatchButtonOrder(udefButtons, describe);
        }
        return CollectionUtils.nullToEmpty(udefButtons).stream()
                .map(ButtonExt::of)
                .map(ButtonExt::toButton)
                .collect(Collectors.toList());
    }

    private IButton createButton(String actionCode, String actionLabel, String actionName) {
        IButton button = new Button();
        button.setAction(actionCode);
        button.setActionType(IButton.ACTION_TYPE_DEFAULT);
        button.setName(actionName);
        button.setLabel(actionLabel);
        return button;
    }

    @Override
    public List<IButton> findButtonsForCreate(IObjectDescribe describe, User user) {
        if (!AppFrameworkConfig.isGrayCreateEditSystemButton(user.getTenantId())) {
            return findButtons(describe, ButtonUsePageType.Create, user, false, false);
        }
        return filterByButtons(findButtons(describe, ButtonUsePageType.Create, user, true, false), describe.getApiName());
    }

    @Override
    public List<IButton> findButtonsForEdit(IObjectDescribe describe, User user) {
        if (!AppFrameworkConfig.isGrayCreateEditSystemButton(user.getTenantId())) {
            return findButtons(describe, ButtonUsePageType.Edit, user, false, false);
        }
        return filterByButtons(findButtons(describe, ButtonUsePageType.Edit, user, true, false), describe.getApiName());
    }

    @Override
    public List<IButton> findButtonsForRecycleBin(IObjectDescribe describe, User user) {
        if (!AppFrameworkConfig.isGrayRecoverButton(user.getTenantId(), describe.getApiName())) {
            return Lists.newArrayList();
        }
        List<IButton> buttons = findButtons(describe, ButtonUsePageType.RecycleBin, user, true, false);
        buttons.add(ObjectAction.DELETE.createButton());
        return buttons;
    }

    private List<IButton> filterByButtons(List<IButton> buttons, String objectApiName) {
        return CollectionUtils.nullToEmpty(buttons).stream()
                .filter(it -> LayoutButtonExt.of(it).isCustomButton()
                        || AppFrameworkConfig.isGrayCreateEditSystemButton(objectApiName, it.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<IButton> findTeamMemberButton(IObjectDescribe describe, IObjectData data, User user) {
        List<IUdefButton> customButton = findButtonsByDescribeApiName(user, describe);
        return customButton.stream()
                .filter(x -> ButtonExt.TEAM_MEMBER_BUTTON_API_NAME.contains(x.getApiName()))
                .map(ButtonExt::of)
                .filter(buttonExt -> Objects.nonNull(buttonExt.filterButtonByWheres(data, describe)))
                .map(ButtonExt::toButton)
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, List<IUdefButton>> findListButtonByButtonFilter(IObjectDescribe describe, List<IObjectData> dataList, User user, String usePageType) {
        if (CollectionUtils.empty(dataList)) {
            return Maps.newHashMap();
        }
        List<IUdefButton> udefButtons = findCustomButtonByUsePageIncludeDisable(describe.getApiName(), usePageType, user);
        return filterListButton(udefButtons, describe, dataList, user, usePageType);
    }

    @Override
    public Map<String, List<IUdefButton>> filterListButton(List<IUdefButton> udefButtons, IObjectDescribe describe,
                                                           List<IObjectData> dataList, User user, String usePageType) {
        if (CollectionUtils.empty(udefButtons)) {
            return Maps.newHashMap();
        }
        // 忽略 id 不存在的数据
        dataList = CollectionUtils.nullToEmpty(dataList).stream()
                .filter(data -> !Strings.isNullOrEmpty(data.getId()))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(dataList)) {
            return Maps.newHashMap();
        }

        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FILTER_LIST_BUTTON_GRAY, user.getTenantId())) {
            return filterListButtonGray(user, describe, udefButtons, dataList);
        }

        List<IButton> buttons = udefButtons.stream()
                .map(ButtonExt::of)
                .map(ButtonExt::toButton)
                .collect(Collectors.toList());
        //功能权限过滤
        List<IButton> buttonList = ButtonFilter.builder()
                .functionPrivilegeService(functionPrivilegeService)
                .changeOrderLogicService(changeOrderLogicService)
                .buttons(buttons)
                .user(user)
                .objectDescribeExt(ObjectDescribeExt.of(describe))
                .build()
                .filterByObjectDescribe()
                .filterByFunctionPrivilege()
                .getButtons();

        udefButtons.removeIf(x -> buttonList.stream().noneMatch(button -> Objects.equals(button.getName(), x.getApiName())));
        HashMap<String, List<IUdefButton>> resultMap = Maps.newHashMap();
        Map<String, List<Wheres>> wheresMap = Maps.newHashMap();
        udefButtons.forEach(x -> wheresMap.put(x.getApiName(), ButtonExt.of(x).handleWheres(describe)));

        Map<String, Map<String, Permissions>> dataPrivilege = getDataPrivilege(describe, dataList, user, buttons);
//        Map<String, List<String>> approvalFlowWhitePeople = getApprovalFlowWhitePeople(describe, dataList, user);
        Map<String, List<String>> approvalFlowWhitePeople = Collections.emptyMap();
        for (IObjectData data : dataList) {
            Set<String> buttonName = ButtonFilter.builder()
                    .functionPrivilegeService(functionPrivilegeService)
                    .buttons(buttonList)
                    .data(data)
                    .user(user)
                    .objectDescribeExt(ObjectDescribeExt.of(describe))
                    .dataPrivilegeFunction((objectData, functionCode) -> dataPrivilege)
                    .approvalFlowWhitePeopleFunction(objectData -> approvalFlowWhitePeople.getOrDefault(data.getId(), Collections.emptyList()))
                    .customButtonFunction(x -> udefButtons)
                    .build()
                    .filterByDataPrivilege()
                    .filterByLockAndLifeStatus()
                    .filterByButtonFilter()
                    .getButtons()
                    .stream()
                    .map(IButton::getName)
                    .collect(Collectors.toSet());
            List<IUdefButton> resultButtons = udefButtons.stream()
                    .filter(it -> buttonName.contains(it.getApiName()))
                    .filter(it -> ButtonExt.of(it).isSystemButton() || !ObjectDataExt.of(data).isLock() || ButtonExt.of(it).isShowWhenLock())
                    .collect(Collectors.toList());
            resultMap.put(data.getId(), resultButtons);
        }
        return resultMap;
    }

    private Map<String, List<IUdefButton>> filterListButtonGray(User user, IObjectDescribe describe, List<IUdefButton> udefButtons,
                                                                List<IObjectData> dataList) {
        Map<String, List<IUdefButton>> resultMap = Maps.newHashMap();
        List<IButton> buttons = udefButtons.stream()
                .map(ButtonExt::of)
                .map(ButtonExt::toButton)
                .collect(Collectors.toList());
        //功能权限过滤
        Set<String> hasFuncPrivilegeButtonNames = ButtonFilter.builder()
                .functionPrivilegeService(functionPrivilegeService)
                .changeOrderLogicService(changeOrderLogicService)
                .buttons(buttons)
                .user(user)
                .objectDescribeExt(ObjectDescribeExt.of(describe))
                .build()
                .filterByObjectDescribe()
                .filterByFunctionPrivilege()
                .getButtons()
                .stream()
                .map(IButton::getName)
                .collect(Collectors.toSet());
        udefButtons.removeIf(x -> !hasFuncPrivilegeButtonNames.contains(x.getApiName()));
        // 将按钮分为自定义按钮和预设按钮
        List<IUdefButton> customButtons = Lists.newArrayList();
        List<IButton> systemButtons = Lists.newArrayList();
        for (IUdefButton button : udefButtons) {
            if (ButtonExt.of(button).isSystemButton()) {
                systemButtons.add(ButtonExt.of(button).toButton());
            } else {
                customButtons.add(button);
            }
        }
        Map<String, List<Wheres>> wheresMap = customButtons.stream()
                .map(ButtonExt::of)
                .collect(Collectors.toMap(ButtonExt::getApiName, it -> it.handleWheres(describe), (x, y) -> y));
        Map<String, Map<String, Permissions>> dataPrivilege = getDataPrivilege(describe, dataList, user, systemButtons);
        Map<String, List<String>> approvalFlowWhitePeople = Collections.emptyMap();
        for (IObjectData data : dataList) {
            // 过滤预设按钮
            Set<String> systemButtonNames = filterSystemButtons(user, describe, udefButtons, systemButtons, dataPrivilege, approvalFlowWhitePeople, data);
            // 过滤自定义按钮
            Set<String> buttonNames = filterByLockAndWheres(data, customButtons, describe, wheresMap).stream()
                    .map(IUdefButton::getApiName)
                    .collect(Collectors.toSet());
            buttonNames.addAll(systemButtonNames);

            List<IUdefButton> resultButtons = udefButtons.stream()
                    .filter(it -> buttonNames.contains(it.getApiName()))
                    .collect(Collectors.toList());
            resultMap.put(data.getId(), resultButtons);
        }
        return resultMap;
    }

    private Set<String> filterSystemButtons(User user, IObjectDescribe describe, List<IUdefButton> udefButtons, List<IButton> systemButtons, Map<String, Map<String, Permissions>> dataPrivilege, Map<String, List<String>> approvalFlowWhitePeople, IObjectData data) {
        if (CollectionUtils.empty(systemButtons)) {
            return Collections.emptySet();
        }
        return ButtonFilter.builder()
                .functionPrivilegeService(functionPrivilegeService)
                .buttons(systemButtons)
                .data(data)
                .user(user)
                .objectDescribeExt(ObjectDescribeExt.of(describe))
                .dataPrivilegeFunction((objectData, functionCode) -> dataPrivilege)
                .approvalFlowWhitePeopleFunction(objectData -> approvalFlowWhitePeople.getOrDefault(data.getId(), Collections.emptyList()))
                .customButtonFunction(x -> udefButtons)
                .build()
                .filterByDataPrivilege()
                .filterByLockAndLifeStatus()
                .filterByButtonFilter()
                .getButtons()
                .stream()
                .map(IButton::getName)
                .collect(Collectors.toSet());
    }

    private Map<String, Map<String, Permissions>> getDataPrivilege(IObjectDescribe describe, List<IObjectData> dataList, User user, List<IButton> buttons) {
        List<String> actionCodes = buttons.stream()
                .map(x -> x.getAction())
                .distinct().collect(Collectors.toList());
        return metaDataMiscService.checkDataPrivilege(user, Lists.newArrayList(dataList), describe, actionCodes);
    }

    /**
     * 简单处理，锁定的数据不下发列表页按钮
     * 后期预置按钮支持配置显示位置时，需要修改
     *
     * @param data
     * @param buttonList
     * @param describe
     * @return
     */
    private List<IUdefButton> filterByLockAndWheres(IObjectData data, List<IUdefButton> buttonList, IObjectDescribe describe,
                                                    Map<String, List<Wheres>> wheresMap) {
        if (CollectionUtils.empty(buttonList)) {
            return Collections.emptyList();
        }

        // 锁定数据是否下发按钮更具按钮配置来处理
        List<IUdefButton> buttons = buttonList.stream()
                .filter(x -> !ObjectDataExt.of(data).isLock() || ButtonExt.of(x).isShowWhenLock())
                .collect(Collectors.toList());
        return ButtonExt.filterButtonsWheres(data, describe, buttons, wheresMap);
    }

    @Override
    public String submitBulkActionJob(User user, CallBackActionParams callBackActionParams, String objectApiName) {
        return jobScheduleService.submitBulkActionJob(user, objectApiName, callBackActionParams.toJson(),
                ButtonConfig.isRealTimeJob(callBackActionParams.getActionParamMap().size()));
    }

    private void checkParam(IUdefButton button) {
        List<Map> paramForm = button.getParamForm();
        if (CollectionUtils.notEmpty(paramForm) && paramForm.size() > 25) {
            throw new ValidateException(I18N.text(I18NKey.BUTTON_PARA_CANNOT_EXCEED));
        }
    }

    @Transactional
    @Override
    public void updateButtonUrl(User user, String objectDescribeApiName, ButtonURL buttonURL, Set<String> buttonApiNameSet) {
        if (CollectionUtils.empty(buttonApiNameSet)) {
            return;
        }
        if (user.getTenantIdInt() < 0) {
            throw new ValidateException("Do not update system DB");
        }
        List<IUdefButton> buttonList = findButtonsByDescribeApiName(user, objectDescribeApiName);
        List<IUdefButton> buttons = buttonList.stream()
                .filter(a -> buttonApiNameSet.contains(a.getApiName()))
                .collect(Collectors.toList());

        buttons.forEach(button -> {
            String url = buttonURL.toJsonString();
            button.setUrl(buttonURL.empty() ? "" : url);
            try {
                buttonService.update(button);
            } catch (MetadataServiceException e) {
                log.warn("update updateButtonUrl fail, tenantId:{}, describe:{}, buttons:{}, buttonUrl:{}",
                        user.getTenantId(), objectDescribeApiName, buttonApiNameSet, url, e);
            }
        });

    }

    @Override
    public IUdefButton findButtonByApiNameInDesigner(User user, String buttonApiName, String describeApiName) {
        IUdefButton button = buttonService.findByApiNameAndTenantId(buttonApiName, describeApiName, user.getTenantId());
        ButtonConfig.generatedButtonSystemFilter(button);
        return button;
    }

    @Override
    public List<IUdefButton> findButtonsByDescribeApiName(User user, String objectDescribeApiName) {
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), objectDescribeApiName);
        return findButtonsByDescribeApiName(user, describe);
    }

    private void transButtonParam(User user, List<IUdefButton> buttonList, IObjectDescribe describe) {
        if (CollectionUtils.empty(buttonList) || Objects.isNull(describe) || Objects.isNull(user)) {
            return;
        }
        String objApiName = describe.getApiName();
        String tenantId = user.getTenantId();
        if (StringUtils.isBlank(objApiName) || StringUtils.isBlank(tenantId)) {
            return;
        }
        boolean ontime = RequestUtil.isFromManage();

        List<String> keys = new ArrayList<>();
        for (IUdefButton button : buttonList) {
            if (Objects.isNull(button)) {
                continue;
            }
            ButtonExt buttonExt = ButtonExt.of(button);
            String buttonApi = button.getApiName();
            if (StringUtils.isBlank(buttonApi) || Objects.isNull(buttonExt.getParamForm())) {
                continue;
            }
            keys.addAll(buttonExt.getParamForm().stream()
                    .map(ParamForm::of)
                    .filter(ParamForm::isButtonNewField)
                    .map(x -> TranslateI18nUtils.getButtonParamKey(tenantId, objApiName, buttonApi, x.getApiName()))
                    .collect(Collectors.toList()));
        }

        Map<String, String> transMap = i18nService.getTransValue(keys, false, ontime);
        for (IUdefButton button : buttonList) {
            if (Objects.isNull(button)) {
                continue;
            }
            ButtonExt buttonExt = ButtonExt.of(button);
            String buttonApi = button.getApiName();
            if (StringUtils.isBlank(buttonApi) || CollectionUtils.empty(buttonExt.getParamForm())) {
                continue;
            }
            buttonExt.getParamForm().forEach(x -> {
                ParamForm paramForm = ParamForm.of(x);
                if (paramForm.isButtonNewField()) {
                    String key = TranslateI18nUtils.getButtonParamKey(tenantId, objApiName, buttonApi, paramForm.getApiName());
                    paramForm.setLabel(transMap.getOrDefault(key, paramForm.getLabel()));
                }
            });
        }
    }

    @Override
    public List<IUdefButton> findButtonsByDescribeApiName(User user, IObjectDescribe describe) {
        String objectDescribeApiName = describe.getApiName();
        List<IUdefButton> buttons = buttonService.findButtonsByDescribeApiName(objectDescribeApiName, user.getTenantId());
        return processButtonsForObjectDescribe(user, describe, buttons);
    }

    private List<IUdefButton> processButtonsForObjectDescribe(User user, IObjectDescribe describe, List<IUdefButton> buttons) {
        return processButtonsForObjectDescribe(user, describe, buttons, true);
    }

    private List<IUdefButton> processButtonsForObjectDescribe(User user, IObjectDescribe describe, List<IUdefButton> buttons, boolean processConvertButtons) {
        String objectDescribeApiName = describe.getApiName();
        transButtonParam(user, buttons, describe);
        if (describe.isBigObject()) {
            buttons.removeIf(button -> !ButtonConfig.getBigObjectSupportButtonList().contains(button.getApiName()));
        }
        //处理乡镇字段
        buttons.forEach(button -> dealButtonTownInfo(describe, button));
        if (AppFrameworkConfig.isAddEditUIActionGray(user.getTenantId(), objectDescribeApiName)) {
            buttons.stream()
                    .map(ButtonExt::of)
                    .filter(buttonExt -> buttonExt.isCommonButton() && ButtonExt.DEFAULT_BUTTON_API_NAME_LIST.contains(buttonExt.getApiName()))
                    .forEach(ButtonExt::changeButtonDescribe);
            buttons = fillDefaultButton(user, objectDescribeApiName, buttons, describe);
        }
        // 补充恢复按钮
        if (AppFrameworkConfig.isGrayRecoverButton(user.getTenantId(), describe.getApiName())) {
            List<IUdefButton> defaultButtonList = Lists.newArrayList();
            fillIfNotExist(user, buttons, defaultButtonList, ObjectAction.RECOVER.getButtonApiName(), describe);
            buttons.addAll(defaultButtonList);
        }
        // 开启了变更单的原单对象
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        if (!describeExt.isSlaveObject() && describeExt.enabledChangeOrder()) {
            List<IUdefButton> defaultButtonList = Lists.newArrayList();
            fillIfNotExist(user, buttons, defaultButtonList, CHANGE.getButtonApiName(), describe);
            buttons.addAll(defaultButtonList);
        }
        // 变更单对象
        if (!describeExt.isSlaveObject() && describeExt.isChangeOrderObject()) {
            List<IUdefButton> defaultButtonList = Lists.newArrayList();
            fillIfNotExist(user, buttons, defaultButtonList, EFFECTIVE.getButtonApiName(), describe);
            buttons.addAll(defaultButtonList);
        }

        // 只在需要时处理转换按钮和参照新建按钮，避免批量查询时的性能问题
        if (processConvertButtons) {
            // 推拉单
            // 转换按钮
            if (objectConvertRuleService.count(user, describe.getApiName(), null)) {
                List<IUdefButton> defaultButtonList = Lists.newArrayList();
                fillIfNotExist(user, buttons, defaultButtonList, TRANSFORM.getButtonApiName(), describe);
                buttons.addAll(defaultButtonList);
            }
            // 参照新建按钮
            if (objectConvertRuleService.count(user, null, describe.getApiName())) {
                List<IUdefButton> defaultButtonList = Lists.newArrayList();
                fillIfNotExist(user, buttons, defaultButtonList, REFERENCE_CREATE.getButtonApiName(), describe);
                defaultButtonList.forEach(button -> {
                    List<String> usePages = ButtonExt.of(button).getUsePages();
                    if (CollectionUtils.notEmpty(usePages) &&
                            !(usePages.contains(ButtonUsePageType.Create.getId()) || usePages.contains(ButtonUsePageType.Edit.getId()))) {
                        usePages.addAll(Lists.newArrayList(ButtonUsePageType.Create.getId(), ButtonUsePageType.Edit.getId()));
                    }
                });
                buttons.addAll(defaultButtonList);
            }
        }

        // 判断是否购买多语资源包
        Map<String, Boolean> existModule = licenseService.existModule(user.getTenantId(), Sets.newHashSet(ModuleCode.MULTI_LANGUAGE_APP));
        if (existModule.get(ModuleCode.MULTI_LANGUAGE_APP)) {
            buttons.forEach(x -> ButtonExt.of(x).fillButtonLabel(objectDescribeApiName));
        }

        // 移动端支持批量打印
        if (ButtonConfig.isGrayBulkPrint(user.getTenantId())) {
            if (RequestUtil.isMobileRequest()) {
                buttons.removeIf(btn ->
                        StringUtils.equals(btn.getApiName(), PRINT.getButtonApiName())
                                && (!ButtonConfig.listLayoutButtonDisplayGray(btn.getApiName(), LayoutAgentType.MOBILE.getCode(), user.getTenantId())
                                || RequestUtil.isMobileRequestBeforeVersion(VERSION_875)));

                Optional<IUdefButton> printButton = buttons.stream().filter(a -> Objects.equals(PRINT.getButtonApiName(), a.getApiName())).findFirst();
                if (printButton.isPresent()) {
                    List<String> usePages = Lists.newArrayList(ButtonUsePageType.Detail.getId(), ButtonUsePageType.ListBatch.getId());
                    printButton.get().setUsePages(usePages);
                }
            } else {
                buttons.forEach(it -> ButtonExt.of(it).handlePrintButtonUsePage());
            }

        }
        // 根据相关团队开关过滤按钮 最底层处理？
        if (AppFrameworkConfig.isOptionalFeaturesSupport(user.getTenantId()) || describeExt.isChangeOrderObject()) {
            OptionalFeaturesSwitchDTO optionalFeaturesSwitch = optionalFeaturesService.findOptionalFeaturesSwitch(user.getTenantId(), describe);
            buttons.removeIf(button -> !optionalFeaturesSwitch.getIsRelatedTeamEnabled() && ButtonExt.TEAM_MEMBER_BUTTON_API_NAME.contains(button.getApiName())
                    || !optionalFeaturesSwitch.getIsFollowUpDynamicEnabled() && StringUtils.equals(button.getApiName(), SALE_RECORD.getDefaultButtonApiName()));

        }
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FLOW_BUTTON_GRAY, user.getTenantId())) {
            List<IUdefButton> defaultButtonList = Lists.newArrayList();
            fillIfNotExist(user, buttons, defaultButtonList, START_BPM.getButtonApiName(), describe);
            fillIfNotExist(user, buttons, defaultButtonList, START_STAGE_PROPELLOR.getButtonApiName(), describe);
            buttons.addAll(defaultButtonList);
        }
        if (UdobjGrayUtil.isObjectAndTenantGray(UdobjGrayKey.FOLLOW_GRAY, user.getTenantId(), describe.getApiName())) {
            List<IUdefButton> defaultButtonList = Lists.newArrayList();
            fillIfNotExist(user, buttons, defaultButtonList, FOLLOW.getButtonApiName(), describe);
            fillIfNotExist(user, buttons, defaultButtonList, UNFOLLOW.getButtonApiName(), describe);
            buttons.addAll(defaultButtonList);
        } else {
            buttons.removeIf(x -> FOLLOW.getButtonApiName().equals(x.getApiName()));
            buttons.removeIf(x -> UNFOLLOW.getButtonApiName().equals(x.getApiName()));
        }
        // 业务用于过滤按钮的provider
        FilterButtonsProvider provider = findButtonsByDescribeApiNameManager.getProvider(user.getTenantId(), describe.getApiName());
        if (Objects.nonNull(provider)) {
            buttons = provider.getButtons(user, buttons);
        }
        //处理已经禁用或删除的字段
        correctFieldByDescribe(describe, buttons);
        return buttons;
    }

    @Override
    public Map<String, List<IUdefButton>> findButtonsByDescribeApiNames(User user, List<String> describeApiNames, boolean checkPartnerStatus) {
        Map<String, IObjectDescribe> objectDescribeMap = describeLogicService.findObjects(user.getTenantId(), describeApiNames);
        Map<String, List<IUdefButton>> buttonMap = buttonService.findButtonsByDescribeApiNames(describeApiNames, user.getTenantId(), false);

        Map<String, List<IUdefButton>> resultMap = Maps.newHashMap();
        buttonMap.forEach((describeApiName, buttons) -> {
            IObjectDescribe describe = objectDescribeMap.get(describeApiName);
            if (Objects.isNull(describe)) {
                return;
            }
            // 批量查询场景跳过转换按钮和参照新建按钮的处理，避免性能问题
            List<IUdefButton> buttonsList = processButtonsForObjectDescribe(user, describe, buttons, false);
            resultMap.put(describeApiName, filterAndProcessCustomButtons(user, describe, checkPartnerStatus, buttonsList));
        });
        return resultMap;
    }

    /**
     * 根据对象描述纠正按钮参数
     * 处理已经禁用或删除的字段
     * 纠正字段类型
     *
     * @param describe
     * @param buttons
     */
    private void correctFieldByDescribe(IObjectDescribe describe, List<IUdefButton> buttons) {
        if (CollectionUtils.empty(buttons)) {
            return;
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        buttons.forEach(button -> {
            List<IParamForm> paramForm = ParamForm.fromList(button.getParamForm());
            if (CollectionUtils.empty(paramForm)) {
                return;
            }
            List<Map> activeParamForm = Lists.newArrayList();
            for (IParamForm param : paramForm) {
                // 预置参数暂不处理
                if (IObjectDescribe.DEFINE_TYPE_SYSTEM.equals(param.getDefineType())) {
                    activeParamForm.add(param.toMap());
                    continue;
                }
                if (Objects.equals(param.getObjectApiName(), describe.getApiName())) {
                    IFieldDescribe fieldDescribe = describeExt.getFieldDescribe(param.convertToFieldApiName());
                    // 对象下禁用\删除的字段需要在按钮参数中移除
                    if (Objects.isNull(fieldDescribe) || BooleanUtils.isNotTrue(fieldDescribe.isActive())) {
                        continue;
                    }
                    // 优先使用描述中的字段类型
                    param.setType(fieldDescribe.getType());
                }
                activeParamForm.add(param.toMap());
            }
            ButtonExt.of(button).setParamForm(activeParamForm);
        });
    }

    @Override
    public List<IButton> findButtonsByUsePageType(User user, IObjectDescribe describe, IObjectData objectData, ButtonUsePageType usePageType) {
        return findButtonsByUsePageType(user, describe, objectData, usePageType, false);
    }

    @Override
    public List<IButton> findButtonsByUsePageType(User user, IObjectDescribe describe, IObjectData objectData, ButtonUsePageType usePageType, boolean isOnlyActivate) {
        return findButtonsByUsePageType(user, describe, objectData, usePageType, isOnlyActivate, true);
    }

    @Override
    public List<IButton> findButtonsByUsePageType(User user, IObjectDescribe describe, IObjectData objectData, ButtonUsePageType usePageType, boolean isOnlyActivate, boolean filterFunPrivilege) {
        StopWatch stopWatch = StopWatch.create("findButtonsByUsePageType");
        Set<String> readonlyFields = Collections.emptySet();
        if (ButtonUsePageType.Detail == usePageType) {
            readonlyFields = functionPrivilegeService.getReadonlyFields(user, describe.getApiName());
            stopWatch.lap("getReadonlyFields");
        }
        List<IUdefButton> buttonList = findButtonList(user, describe.getApiName(), false);
        stopWatch.lap("findButtonList");
        if (isOnlyActivate) {
            buttonList.removeIf(it -> !it.isActive());
        }
        buttonList = filterButtonsForUsePageType(user, objectData, describe, usePageType.getId(), buttonList, readonlyFields);
        stopWatch.lap("filterButtonsForUsePageType");
        List<IButton> buttons;
        if (filterFunPrivilege) {
            buttons = filterFunPrivilege(user, describe, buttonList);
            stopWatch.lap("filterFunPrivilege");
        } else {
            buttons = buttonList.stream()
                    .map(x -> ButtonExt.of(x).toButton()).collect(Collectors.toList());
        }
        buttons = filterByUsePage(user, buttons, usePageType);
        stopWatch.lap("filterByUsePage");
        stopWatch.logSlow(100);

        return buttons;
    }

    private List<IButton> filterByUsePage(User user, List<IButton> buttons, ButtonUsePageType usePageType) {
        if (ButtonUsePageType.ListNormal != usePageType) {
            return buttons;
        }
        // 小程序独立于 H5
        boolean isRealH5 = !UdobjGrayConfig.isAllow(UdobjGrayConfigKey.H5_UI_PAAS_ACTION_GRAY_EI, user.getTenantId()) &&
                RequestUtil.isH5MobileRequest() && !RequestUtil.isWXMiniProgram();

        if (isRealH5 || (RequestUtil.isMobileRequest() && !LayoutContext.isEnableMobileLayout())) {
            buttons.removeIf(it -> LayoutButtonExt.of(it).isUIPaaS());
        }
        return buttons;
    }

    @Override
    public void checkCustomButtonCountLimit(User user, String describeApiName, int count) {
        TenantLicenseInfo.builder()
                .licenseService(licenseService)
                .user(user)
                .build()
                .init(Sets.newHashSet(ModulePara.ModuleBiz.UDOBJ.getBizCode()))
                .checkCustomButtonCountLimit(count, describeApiName);
    }

    @Builder
    @Data
    private static class CustomButtonDescribeExtraOption {
        private boolean fieldDependence;
        private boolean addExtProperty;
    }
}
