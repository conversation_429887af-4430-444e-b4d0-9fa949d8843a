package com.facishare.paas.appframework.metadata.plugin;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.LanguageClientUtil;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.layout.I18nInfo;
import com.facishare.paas.appframework.metadata.relation.SourceTypes;
import com.facishare.paas.appframework.metadata.relation.TargetTypes;
import com.facishare.paas.appframework.metadata.repository.api.IRepository;
import com.facishare.paas.appframework.metadata.repository.model.MtFunctionPluginConf;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.repository.model.MtFunctionPluginConf.*;

/**
 * <AUTHOR> ZhenHui
 * @Data : 2025/4/25
 * @Description :
 */
@Slf4j
@Service("functionPluginConfLogicService")
public class FunctionPluginConfLogicServiceImpl implements FunctionPluginConfLogicService {
    @Resource
    private IRepository<MtFunctionPluginConf> repository;
    @Resource
    private FunctionLogicService functionLogicService;
    @Resource
    private LogService logService;

    private static final Map<String, MtFunctionPluginConf.RuleConfig> dataValidator =
            MtFunctionPluginConf.RuleConfig.createDefaultRuleConfigs();

    private Query buildQuery(User user) {
        SearchQuery searchQuery = SearchQueryImpl.filters(Lists.newArrayList(
                FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter(),
                FilterExt.of(Operator.EQ, IObjectData.IS_DELETED, String.valueOf(DELETE_STATUS.NORMAL.getValue())).getFilter()
        ));

        return Query.builder()
                .searchQuery(searchQuery)
                .limit(AppFrameworkConfig.DEFAULT_MAX_QUERY_LIMIT)
                .offset(0)
                .needReturnCountNum(false)
                .needReturnQuote(false)
                .build();
    }

    private List<MtFunctionPluginConf> findAllByTenantId(User user, Query query, boolean needI18) {
        List<MtFunctionPluginConf> result = CollectionUtils.nullToEmpty(repository.findBy(user, query, MtFunctionPluginConf.class));
        if (needI18) {
            result = setI18n(result, user.getTenantId());
        }
        return result;
    }

    private Optional<MtFunctionPluginConf> findByApiName(User user, String apiName, boolean needI18) {
        if (StringUtils.isBlank(apiName)) {
            return Optional.empty();
        }
        Query query = buildQuery(user);
        query.and(
                FilterExt.of(Operator.EQ, MtFunctionPluginConf.API_NAME, apiName).getFilter()
        );
        return findOne(user, query, needI18);
    }

    private Optional<MtFunctionPluginConf> findOne(User user, Query query, boolean needI18) {
        query.setLimit(1);
        return findAllByTenantId(user, query, needI18).stream().findFirst();
    }

    private List<MtFunctionPluginConf> setI18n(List<MtFunctionPluginConf> datas, String tenantId) {
        datas = CollectionUtils.nullToEmpty(datas);
        if (!LanguageClientUtil.hasLanguage(tenantId)) {
            return datas;
        }
        String languageTag = RequestUtil.getLanguageTag();
        for (MtFunctionPluginConf conf : datas) {
            Map<String, I18nInfo> i18nMap = CollectionUtils.nullToEmpty(conf.getI18nInfo())
                    .stream().collect(Collectors.toMap(I18nInfo::getApiName, x -> x, (x, y) -> x));
            conf.setName(Optional.ofNullable(i18nMap.get(MtFunctionPluginConf.NAME))
                    .map(x -> CollectionUtils.nullToEmpty(x.getLanguageInfo()).get(languageTag))
                    .orElse(conf.getName()));
            conf.setDescription(Optional.ofNullable(i18nMap.get(MtFunctionPluginConf.DESCRIPTION))
                    .map(x -> CollectionUtils.nullToEmpty(x.getLanguageInfo()).get(languageTag))
                    .orElse(conf.getDescription()));
        }
        return datas;
    }

    /**
     * 查询所有绑定关系
     *
     * @param user 用户信息
     * @param refObjApi  关联对象, 传 ALL 查询当前租户所有
     * @return db中非deleted的数据列表
     */
    private List<MtFunctionPluginConf> findAllByRefObj(User user, String refObjApi) {
        if (StringUtils.isBlank(refObjApi)) {
            return Lists.newArrayList();
        }
        Query query = buildQuery(user);
        if (!"ALL".equals(refObjApi)) {
            query.and(
                    FilterExt.of(Operator.EQ, REF_OBJECT_API_NAME, refObjApi).getFilter()
            );
        }
        return findAllByTenantId(user, query, false);
    }

    /**
     * 通过请求类型, 查询使用的绑定关系
     *
     * @param user       用户信息
     * @param refObjApi  关联对象
     * @param moduleName 页面类型
     */
    private Optional<MtFunctionPluginConf> findByModuleName(User user, String refObjApi, String moduleName) {
        if (StringUtils.isBlank(moduleName) || StringUtils.isBlank(refObjApi)) {
            return Optional.empty();
        }
        Query query = buildQuery(user);
        query.and(
                FilterExt.of(Operator.EQ, MtFunctionPluginConf.MODULE_NAME, moduleName).getFilter(),
                FilterExt.of(Operator.EQ, REF_OBJECT_API_NAME, refObjApi).getFilter()
        );
        return findOne(user, query, false);
    }



    private ReferenceData buildReferenceData(MtFunctionPluginConf config) {
        return ReferenceData.builder()
                .sourceType(SourceTypes.OBJECT_EXTENSION)
                .sourceLabel(config.getName())
                .sourceValue(config.getRefObjectApiName() + "." + config.getApiName())
                .targetType(TargetTypes.FUNCTION)
                .targetValue(config.getFunctionApiName())
                .build();
    }

    // 创建和函数的引用关系
    private void createFunctionReference(User user, List<MtFunctionPluginConf> configs) {
        if (CollectionUtils.empty(configs)) {
            return;
        }
        List<ReferenceData> referenceDataList = configs.stream()
                .map(this::buildReferenceData)
                .collect(Collectors.toList());
        functionLogicService.deleteAndCreateRelation(user, referenceDataList);
    }

    private void verifyModuleNameUnique(User user, MtFunctionPluginConf config) {
        // 相同 module_name 只能存在一个
        findByModuleName(user, config.getRefObjectApiName(), config.getModuleName()).ifPresent(conf -> {
            throw new ValidateException(I18N.text(I18NKey.EXTENSION_MODULE_NAME_EXIST));
        });
    }

    @Override
    public List<MtFunctionPluginConf> findAllForList(String refObjApi, User user) {
        if (StringUtils.isBlank(refObjApi)) {
            return Lists.newArrayList();
        }
        Query query = buildQuery(user);
        query.and(
                FilterExt.of(Operator.EQ, REF_OBJECT_API_NAME, refObjApi).getFilter()
        );
        List<MtFunctionPluginConf> result = findAllByTenantId(user, query, true);
        result.removeIf(x -> !x.show());
        return result;
    }
    @Override
    public Optional<MtFunctionPluginConf> findAvailableRuntime(User user,
                                                               String objectApiName, String requestCode) {
        log.debug("{} search for {}", user.getTenantId(), requestCode);
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OBJECT_EXTENSION, user.getTenantId())) {
            return Optional.empty();
        }
        return findByModuleName(user, objectApiName, requestCode)
                .filter(MtFunctionPluginConf::enabled);
    }


    /**
     * 按照api查询关系(非删除数据api全租户唯一)
     *
     * @param user    用户信息
     * @param apiName 数据api
     */
    @Override
    public Optional<MtFunctionPluginConf> findByApiName(User user, String apiName) {
        return findByApiName(user, apiName, true);
    }

    @Override
    @Transactional
    public MtFunctionPluginConf create(User user, MtFunctionPluginConf config) {
        validateData(Lists.newArrayList(config));
        verifyModuleNameUnique(user, config);
        config.setTenantId(user.getTenantId());
        config.setIsActive(true);
        MtFunctionPluginConf pluginConf;
        try {
            pluginConf = repository.create(user, config);
            createFunctionReference(user, Lists.newArrayList(config));
            logService.log(user, EventType.ADD, ActionType.CREATE_EXTENSION, config.getRefObjectApiName(), I18N.text(I18NKey.OBJECT_EXTENSION, config.getName(), config.getApiName()));
        } catch (MetaDataBusinessException e) {
            throw new ValidateException(I18N.text(I18NKey.EXTENSION_API_NAME_EXIST, config.getApiName()));
        }
        return pluginConf;
    }

    @Override
    @Transactional
    public MtFunctionPluginConf update(User user, MtFunctionPluginConf config) {
        validateData(Lists.newArrayList(config));
        MtFunctionPluginConf oldData = findByApiName(user, config.getApiName(), false)
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.EXTENSION_API_NAME_NOT_EXIST,  config.getApiName())));
        // pluginProvider, moduleName, apiName 不允许更新
        if (!oldData.getPluginProvider().equals(config.getPluginProvider()) ||
                !oldData.getModuleName().equals(config.getModuleName())) {
            throw new ValidateException(I18N.text(I18NKey.EXTENSION_FUNCTION_TYPE_AND_MODULE_TYPE_NOT_UPDATE));
        }
        List<MtFunctionPluginConf> pluginConfs = repository.bulkUpdateExceptIgnoreFields(user, Lists.newArrayList(config),
                Sets.newHashSet(PLUGIN_PROVIDER, MODULE_NAME, API_NAME, REF_OBJECT_API_NAME, IS_ACTIVE));
        MtFunctionPluginConf updateRet = pluginConfs.stream().findFirst().orElse(null);
        if (Objects.isNull(updateRet)) {
            throw new ValidateException(I18N.text(I18NKey.UPDATE_FAILED));
        }
        createFunctionReference(user, Lists.newArrayList(config));
        logService.log(user, EventType.MODIFY, ActionType.UPDATE_EXTENSION, config.getRefObjectApiName(),
                I18N.text(I18NKey.OBJECT_EXTENSION, config.getName(), config.getApiName()));
        return updateRet;
    }

    @Override
    public MtFunctionPluginConf enable(User user, String apiName) {
        return findByApiName(user, apiName, false)
                .map(config -> {
                    config.setIsActive(true);
                    List<MtFunctionPluginConf> pluginConfs = repository.bulkUpdateByFields(user, Lists.newArrayList(config), Lists.newArrayList(IS_ACTIVE));
                    logService.log(user, EventType.ENABLE, ActionType.ENABLE_EXTENSION, config.getRefObjectApiName(),
                            I18N.text(I18NKey.OBJECT_EXTENSION, config.getName(), config.getApiName()));
                    return pluginConfs.stream().findFirst().orElse(null);
                })
                .orElse(null);
    }

    @Override
    public MtFunctionPluginConf disable(User user, String apiName) {
        return findByApiName(user, apiName, false)
                .map(config -> {
                    config.setIsActive(false);
                    List<MtFunctionPluginConf> pluginConfs = repository.bulkUpdateByFields(user, Lists.newArrayList(config), Lists.newArrayList(IS_ACTIVE));
                    logService.log(user, EventType.DISABLE, ActionType.DISABLE_EXTENSION, config.getRefObjectApiName(), I18N.text(I18NKey.OBJECT_EXTENSION, config.getName(), config.getApiName()));
                    return pluginConfs.stream().findFirst().orElse(null);
                })
                .orElse(null);
    }

    private void bulkInvalidAndDelete(User user, List<MtFunctionPluginConf> configs) {
        if (CollectionUtils.empty(configs)) {
            return;
        }
        List<ReferenceData> referenceDataList = configs.stream()
                .map(this::buildReferenceData)
                .collect(Collectors.toList());
        functionLogicService.batchDeleteRelation(user, referenceDataList);
        repository.bulkInvalidAndDelete(user, configs);
    }

    @Override
    public void deleteAll(User user, String refObjApi) {
        if (StringUtils.isBlank(user.getTenantId())) {
            return;
        }
        bulkInvalidAndDelete(user, findAllByRefObj(user, refObjApi));
    }

    @Override
    @Transactional
    public void delete(User user, String apiName) {
        MtFunctionPluginConf config = findByApiName(user, apiName, false)
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.EXTENSION_API_NAME_NOT_EXIST, apiName)));
        if (config.enabled()) {
            throw new ValidateException(I18N.text(I18NKey.EXTENSION_ENABLE_STATUS_NOT_DELETE, apiName));
        }
        bulkInvalidAndDelete(user, Lists.newArrayList(config));
        logService.log(user, EventType.DELETE, ActionType.DELETE_EXTENSION, config.getRefObjectApiName(), I18N.text(I18NKey.OBJECT_EXTENSION, config.getName(), config.getApiName()));
    }

    @Override
    @Transactional
    public List<MtFunctionPluginConf> brush(String tenantId, List<MtFunctionPluginConf> configs) {
        if (StringUtils.isBlank(tenantId) || CollectionUtils.empty(configs)) {
            return Lists.newArrayList();
        }
        validateData(configs);
        User user = User.systemUser(tenantId);

        Set<String> uniqueItems = new HashSet<>();

        List<String> duplicates = configs.stream()
                .map(MtFunctionPluginConf::UniqueId)
                .filter(item -> !uniqueItems.add(item))    // 检查重复数据
                .collect(Collectors.toList());
        if (!duplicates.isEmpty()) {
            throw new ValidateException(String.format("[%s]has duplicated items：[%s]", tenantId, duplicates));
        }

        Set<String> dbDataIds = findAllByRefObj(user, "ALL").stream()
                .map(MtFunctionPluginConf::UniqueId)
                .collect(Collectors.toSet());
        configs.removeIf(x -> dbDataIds.contains(x.UniqueId()));    // 和db数据去重

        // 若函数被删除, 则不处理数据, 禁用则没关系
        List<String> functionApis = configs.stream().map(x -> x.getFunctionApiName()).collect(Collectors.toList());
        Set<String> exitFunApis = CollectionUtils.nullToEmpty(functionLogicService.findFunctionByApiNames(user, functionApis, null))    // 之前的数据都是绑定 [全部]
                .stream().map(x -> x.getApiName()).collect(Collectors.toSet());
        configs.removeIf(x -> !exitFunApis.contains(x.getFunctionApiName()));

        if (CollectionUtils.empty(configs)) {
            return Lists.newArrayList();
        }

        List<MtFunctionPluginConf> pluginConfs = repository.bulkCreate(user, configs);
        createFunctionReference(user, configs);
        return pluginConfs;
    }

    private static void validateData(List<MtFunctionPluginConf> datas) {
        if (CollectionUtils.empty(datas)) {
            return;
        }
        datas.removeIf(Objects::isNull);
        for (MtFunctionPluginConf config : datas) {
            MtFunctionPluginConf.RuleConfig currentRule = dataValidator.get(config.getPluginProvider());

            String errorMessage = I18N.text(I18NKey.EXTENSION_DATA_ERROR,
                    config.getApiName(), config.getName(),
                    config.getPluginProvider(), config.getMethods(), config.getModuleName(), config.getModuleType());

            if (Objects.isNull(currentRule)) {
                throw new ValidateException(errorMessage);
            }

            // 验证 moduleName 和对应的配置
            MtFunctionPluginConf.RuleConfig.ModuleConfig moduleConfig = currentRule.getModuleNameConfig().get(config.getModuleName());
            if (moduleConfig == null) {
                throw new ValidateException(errorMessage);
            }

            // 验证 methods
            if (CollectionUtils.empty(config.getMethods())
                    || config.getMethods().contains(null)
                    || !moduleConfig.getMethods().containsAll(config.getMethods())) {
                throw new ValidateException(errorMessage);
            }

            // 验证 moduleType
            Set<String> supportModuleType = moduleConfig.getModuleType();
            if (CollectionUtils.empty(config.getModuleType()) && CollectionUtils.empty(supportModuleType)) {
                return;
            }
            if (config.getModuleType().contains(null) || !supportModuleType.containsAll(config.getModuleType())) {
                throw new ValidateException(errorMessage);
            }
        }
    }
}
