package com.facishare.paas.appframework.metadata

import com.facishare.crm.userdefobj.DefObjConstants
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.license.LicenseService
import com.facishare.paas.appframework.log.ActionType
import com.facishare.paas.appframework.log.EventType
import com.facishare.paas.appframework.log.LogService
import com.facishare.paas.appframework.metadata.button.FilterButtonsManager
import com.facishare.paas.appframework.metadata.config.ButtonConfig
import com.facishare.paas.appframework.metadata.config.IUdefButtonConfig
import com.facishare.paas.appframework.metadata.config.UdefButtonConfig
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException
import com.facishare.paas.metadata.api.service.IUdefButtonService
import com.facishare.paas.metadata.impl.UdefButton
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.google.common.collect.Lists
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

/**
 * create by zhaoju on 2019/09/18
 */
class CustomButtonServiceTest extends Specification {

    @Shared
    String addButtonJson = '''{"wheres":[],"param_form":[],"actions":[],"_id":"standard_add_button_id","api_name":"Add_button_default","description":"","label":"新建保存","button_type":"common","use_pages":"create","is_active":true,"is_deleted":false,"version":1,"tenant_id":"71698","created_by":"1000","last_modified_by":"1000","create_time":*************,"last_modified_time":*************,"describe_api_name":"object_1xq2p__c","define_type":"system"}'''
    @Shared
    String convertButtonJson = '''{"wheres":[],"param_form":[],"actions":["5b39d165a5083db01f1352ad"],"_id":"5b39d165a5083db01f1352ae","api_name":"btn_2QMUm__c","tenant_id":"71698","describe_api_name":"AccountObj","description":"","label":"客户映射工单","created_by":"1000","create_time":*************,"last_modified_time":*************,"button_type":"convert","use_pages":["detail"],"jump_url":"","define_type":null,"last_modified_by":"1000","is_active":false,"is_deleted":false,"version":2,"is_batch":null,"url":null}'''
    @Shared
    String commonButtonJson = '''{"wheres":[],"param_form":[],"actions":["5c370230a5083d427d8daff9","5c370230a5083d427d8daffa"],"_id":"5c0f3188a5083d602c0c209a","api_name":"button_O27as__c","tenant_id":"71698","describe_api_name":"AccountObj","description":"","label":"按钮4","created_by":"1000","create_time":*************,"last_modified_time":*************,"button_type":"common","use_pages":["detail"],"jump_url":null,"define_type":null,"last_modified_by":"1000","is_active":true,"is_deleted":false,"version":3,"is_batch":null,"url":null}'''
    @Shared
    String redirectButtonJson = '''{"wheres":[],"param_form":[],"actions":["5cb82283a5083dab4b0d4958"],"_id":"5c18be71a5083db4882b52fa","api_name":"button_dMTc2__c","tenant_id":"71698","describe_api_name":"AccountObj","description":"","label":"网址跳转","created_by":"1000","create_time":*************,"last_modified_time":*************,"button_type":"redirect","use_pages":["detail"],"jump_url":null,"define_type":null,"last_modified_by":"1000","is_active":true,"is_deleted":false,"version":2,"is_batch":null,"url":null}'''

    IUdefButtonService buttonService = Mock(IUdefButtonService)
    LogService logService = Mock(LogService)
    DescribeLogicServiceImpl describeLogicService = Mock(DescribeLogicServiceImpl)
    LicenseService licenseService = Mock(LicenseService)
    I18nSettingServiceImpl i18nService = Mock(I18nSettingServiceImpl)
    ObjectConvertRuleService objectConvertRuleService = Mock(ObjectConvertRuleService)
    FilterButtonsManager findButtonsByDescribeApiNameManager = Mock(FilterButtonsManager)
    CustomButtonService customButtonService

    def setup() {
        initButtonConfig()
        customButtonService = new CustomButtonServiceImpl(
                buttonService: buttonService,
                logService: logService,
                describeLogicService: describeLogicService,
                licenseService: licenseService,
                i18nService: i18nService,
                objectConvertRuleService: objectConvertRuleService,
                findButtonsByDescribeApiNameManager: findButtonsByDescribeApiNameManager
        )

        // Mock i18nService 的默认行为
        i18nService.getTransValue(_, _, _) >> [:]

        // Mock objectConvertRuleService 的默认行为
        objectConvertRuleService.count(_, _, _) >> 0

        // Mock licenseService 的默认行为
        licenseService.existModule(_, _) >> [(com.facishare.paas.appframework.license.util.LicenseConstants.ModuleCode.MULTI_LANGUAGE_APP): false]
        licenseService.batchGetModuleLicenses(_, _) >> [
            (com.facishare.paas.appframework.license.util.LicenseConstants.ModuleCode.CUSTOM_BUTTON): [
                new com.facishare.paas.appframework.license.dto.ModuleParaLicense(
                    paraKey: "custom_button_limit",
                    paraValue: "100"
                )
            ]
        ]

        // Mock findButtonsByDescribeApiNameManager 的默认行为
        findButtonsByDescribeApiNameManager.getProvider(_, _) >> null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建自定义按钮的正常场景
     */
    @Unroll
    def "createCustomButtonTest"() {
        given:
        def user = createUser(tenantId, userId)
        def button = createButton(describeApiName, buttonApiName, buttonJson, user)
        def objectDescribe = createObjectDescribe(describeApiName)

        when:
        def result = customButtonService.createCustomButton(user, button)

        then:
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, describeApiName) >> objectDescribe
        1 * buttonService.create(button) >> button
        1 * logService.log(user, EventType.ADD, ActionType.CREATE_BUTTON, describeApiName, _)

        result.apiName == buttonApiName
        result.tenantId == tenantId
        result.createdBy == userId
        result.lastModifiedBy == userId

        where:
        describeApiName   | buttonApiName     | tenantId | userId | buttonJson
        "object_1xq2p__c" | "custom_button_1" | "71698"  | "1000" | commonButtonJson
        "AccountObj"      | "custom_button_2" | "71698"  | "1000" | redirectButtonJson
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建自定义按钮时参数过多的异常场景
     */
    def "createCustomButtonErrorWithTooManyParams"() {
        given:
        def user = createUser("71698", "1000")
        def button = createButton("AccountObj", "test_button", commonButtonJson, user)
        def paramForm = []
        for (int i = 0; i < 26; i++) {
            paramForm.add([:])
        }
        button.setParamForm(paramForm)

        when:
        customButtonService.createCustomButton(user, button)

        then:
        thrown(ValidateException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建自定义按钮时元数据服务异常的场景
     */
    def "createCustomButtonErrorWithMetadataException"() {
        given:
        def user = createUser("71698", "1000")
        def button = createButton("AccountObj", "test_button", commonButtonJson, user)
        def objectDescribe = createObjectDescribe("AccountObj")

        when:
        customButtonService.createCustomButton(user, button)

        then:
        1 * describeLogicService.findObjectWithoutCopyIfGray("71698", "AccountObj") >> objectDescribe
        1 * buttonService.create(button) >> { throw new MetaDataBusinessException("Test exception") }

        thrown(MetaDataBusinessException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新自定义按钮的正常场景
     */
    @Unroll
    def "updateCustomButtonTest"() {
        given:
        def user = createUser(tenantId, userId)
        def button = createButton(describeApiName, buttonApiName, buttonJson, user)
        def objectDescribe = createObjectDescribe(describeApiName)
        def existingButton = createButton(describeApiName, buttonApiName, buttonJson, user)

        when:
        def result = customButtonService.updateCustomButton(user, button)

        then:
        (1.._) * describeLogicService.findObjectWithoutCopyIfGray(tenantId, describeApiName) >> objectDescribe
        1 * buttonService.findByApiNameAndTenantId(buttonApiName, describeApiName, tenantId) >> existingButton
        1 * buttonService.update(button) >> button
        1 * logService.log(user, EventType.MODIFY, ActionType.UPDATE_BUTTON, describeApiName, _)

        result.lastModifiedBy == userId

        where:
        describeApiName   | buttonApiName     | tenantId | userId | buttonJson
        "object_1xq2p__c" | "custom_button_1" | "71698"  | "1000" | commonButtonJson
        "AccountObj"      | "custom_button_2" | "71698"  | "1000" | redirectButtonJson
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新不存在的按钮时的异常场景
     */
    def "updateCustomButtonErrorWithButtonNotFound"() {
        given:
        def user = createUser("71698", "1000")
        def button = createButton("AccountObj", "non_exist_button", commonButtonJson, user)
        def objectDescribe = createObjectDescribe("AccountObj")

        when:
        customButtonService.updateCustomButton(user, button)

        then:
        (1.._) * describeLogicService.findObjectWithoutCopyIfGray("71698", "AccountObj") >> objectDescribe
        1 * buttonService.findByApiNameAndTenantId("non_exist_button", "AccountObj", "71698") >> null

        thrown(ValidateException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除自定义按钮的正常场景
     */
    @Unroll
    def "deleteCustomButtonTest"() {
        given:
        def user = createUser(tenantId, userId)
        def button = createButton(describeApiName, buttonApiName, buttonJson, user)
        def objectDescribe = createObjectDescribe(describeApiName)

        when:
        def result = customButtonService.deleteCustomButton(user, buttonApiName, describeApiName)

        then:
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, describeApiName) >> objectDescribe
        1 * buttonService.findByApiNameAndTenantId(buttonApiName, describeApiName, tenantId) >> button
        1 * describeLogicService.findObjectIncludeDeleted(tenantId, describeApiName) >> objectDescribe
        1 * buttonService.deleteUdefButton(buttonApiName, describeApiName, tenantId) >> true
        1 * logService.log(user, EventType.DELETE, ActionType.DELETE_BUTTON, describeApiName, _)

        result == true

        where:
        describeApiName   | buttonApiName     | tenantId | userId | buttonJson
        "object_1xq2p__c" | "custom_button_1" | "71698"  | "1000" | commonButtonJson
        "AccountObj"      | "custom_button_2" | "71698"  | "1000" | redirectButtonJson
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除不存在的按钮时的异常场景
     */
    def "deleteCustomButtonErrorWithButtonNotFound"() {
        given:
        def user = createUser("71698", "1000")
        def objectDescribe = createObjectDescribe("AccountObj")

        when:
        customButtonService.deleteCustomButton(user, "non_exist_button", "AccountObj")

        then:
        (1.._) * describeLogicService.findObjectWithoutCopyIfGray("71698", "AccountObj") >> objectDescribe
        1 * buttonService.findByApiNameAndTenantId("non_exist_button", "AccountObj", "71698") >> null

        thrown(MetaDataBusinessException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除已删除的按钮时的异常场景
     */
    def "deleteCustomButtonErrorWithButtonDeleted"() {
        given:
        def user = createUser("71698", "1000")
        def button = createButton("AccountObj", "deleted_button", commonButtonJson, user)
        button.setDeleted(true)
        def objectDescribe = createObjectDescribe("AccountObj")

        when:
        customButtonService.deleteCustomButton(user, "deleted_button", "AccountObj")

        then:
        (1.._) * describeLogicService.findObjectWithoutCopyIfGray("71698", "AccountObj") >> objectDescribe
        1 * buttonService.findByApiNameAndTenantId("deleted_button", "AccountObj", "71698") >> button

        thrown(MetaDataBusinessException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据API名称查找按钮的正常场景
     */
    @Unroll
    def "findButtonByApiNameTest"() {
        given:
        def user = createUser(tenantId, userId)
        def button = createButton(describeApiName, buttonApiName, buttonJson, user)
        def objectDescribe = createObjectDescribe(describeApiName)

        when:
        def result = customButtonService.findButtonByApiName(user, buttonApiName, describeApiName)

        then:
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, describeApiName) >> objectDescribe
        1 * buttonService.findByApiNameAndTenantId(buttonApiName, describeApiName, tenantId) >> button
        1 * licenseService.existModule(tenantId, _) >> [(com.facishare.paas.appframework.license.util.LicenseConstants.ModuleCode.MULTI_LANGUAGE_APP): false]

        result.apiName == buttonApiName
        result.describeApiName == describeApiName

        where:
        describeApiName   | buttonApiName     | tenantId | userId | buttonJson
        "object_1xq2p__c" | "custom_button_1" | "71698"  | "1000" | commonButtonJson
        "AccountObj"      | "custom_button_2" | "71698"  | "1000" | redirectButtonJson
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找不存在的按钮时返回null
     */
    def "findButtonByApiNameTestWithButtonNotFound"() {
        given:
        def user = createUser("71698", "1000")
        def objectDescribe = createObjectDescribe("AccountObj")

        when:
        def result = customButtonService.findButtonByApiName(user, "non_exist_button", "AccountObj")

        then:
        1 * describeLogicService.findObjectWithoutCopyIfGray("71698", "AccountObj") >> objectDescribe
        1 * buttonService.findByApiNameAndTenantId("non_exist_button", "AccountObj", "71698") >> null

        result == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新按钮状态的正常场景
     */
    @Unroll
    def "updateStatusTest"() {
        given:
        def user = createUser(tenantId, userId)
        def button = createButton(describeApiName, buttonApiName, buttonJson, user)
        def objectDescribe = createObjectDescribe(describeApiName)

        when:
        def result = customButtonService.updateStatus(user, buttonApiName, describeApiName, isActive)

        then:
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, describeApiName) >> objectDescribe
        1 * buttonService.findByApiNameAndTenantId(buttonApiName, describeApiName, tenantId) >> button
        1 * describeLogicService.findObjectIncludeDeleted(tenantId, describeApiName) >> objectDescribe
        1 * buttonService.changeUdefButtonStatus(buttonApiName, describeApiName, tenantId, isActive, _) >> true
        1 * logService.log(user, EventType.MODIFY, ActionType.CHANGE_BUTTON_STATUS, describeApiName, _)

        result == true

        where:
        describeApiName   | buttonApiName     | tenantId | userId | isActive | buttonJson
        "object_1xq2p__c" | "custom_button_1" | "71698"  | "1000" | true     | commonButtonJson
        "AccountObj"      | "custom_button_2" | "71698"  | "1000" | false    | redirectButtonJson
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新已删除按钮状态时的异常场景
     */
    def "updateStatusErrorWithButtonDeleted"() {
        given:
        def user = createUser("71698", "1000")
        def button = createButton("AccountObj", "deleted_button", commonButtonJson, user)
        def objectDescribe = createObjectDescribe("AccountObj")
        button.setDeleted(true)

        when:
        customButtonService.updateStatus(user, "deleted_button", "AccountObj", true)

        then:
        (1.._) * describeLogicService.findObjectWithoutCopyIfGray("71698", "AccountObj") >> objectDescribe
        1 * buttonService.findByApiNameAndTenantId("deleted_button", "AccountObj", "71698") >> button

        thrown(MetaDataBusinessException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找按钮列表的正常场景
     */
    @Unroll
    def "findButtonListTest"() {
        given:
        def user = createUser(tenantId, userId)
        def buttons = createButtonList(describeApiName, user)
        def objectDescribe = createObjectDescribe(describeApiName)

        when:
        def result = customButtonService.findButtonList(user, describeApiName)

        then:
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, describeApiName) >> objectDescribe
        1 * buttonService.findButtonsByDescribeApiName(describeApiName, tenantId) >> buttons
        1 * licenseService.existModule(tenantId, _) >> [(com.facishare.paas.appframework.license.util.LicenseConstants.ModuleCode.MULTI_LANGUAGE_APP): false]

        result.size() >= 2  // 至少包含系统默认按钮

        where:
        describeApiName   | tenantId | userId
        "object_1xq2p__c" | "71698"  | "1000"
        "AccountObj"      | "71698"  | "1000"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据使用页面查找自定义按钮的正常场景
     */
    @Unroll
    def "findCustomButtonByUsePageTest"() {
        given:
        def user = createUser(tenantId, userId)
        def buttons = createButtonList(describeApiName, user)
        def objectDescribe = createObjectDescribe(describeApiName)

        when:
        def result = customButtonService.findCustomButtonByUsePage(describeApiName, usePage, user)

        then:
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, describeApiName) >> objectDescribe
        1 * buttonService.findButtonsByDescribeApiName(describeApiName, tenantId) >> buttons

        result.size() >= 0

        where:
        describeApiName   | usePage      | tenantId | userId
        "object_1xq2p__c" | "detail"     | "71698"  | "1000"
        "AccountObj"      | "list_batch" | "71698"  | "1000"
        "AccountObj"      | "create"     | "71698"  | "1000"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试过滤创建页面按钮的正常场景
     */
    @Unroll
    def "filterButtonsForCreateTest"() {
        given:
        def user = createUser(tenantId, userId)
        def buttons = createButtonsForCreate(describeApiName, user)
        def objectDescribe = createObjectDescribe(describeApiName)

        when:
        def result = customButtonService.filterButtonsForCreate(user, describeApiName)

        then:
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, describeApiName) >> objectDescribe
        1 * buttonService.findButtonsByDescribeApiName(describeApiName, tenantId) >> buttons
        1 * licenseService.existModule(tenantId, _) >> [(com.facishare.paas.appframework.license.util.LicenseConstants.ModuleCode.MULTI_LANGUAGE_APP): false]

        result.size() >= 0
        result.each { button ->
            assert button.usePages.contains("create")
        }

        where:
        describeApiName   | tenantId | userId
        "object_1xq2p__c" | "71698"  | "1000"
        "AccountObj"      | "71698"  | "1000"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试过滤编辑页面按钮的正常场景
     */
    @Unroll
    def "filterButtonsForEditTest"() {
        given:
        def user = createUser(tenantId, userId)
        def buttons = createButtonsForEdit(describeApiName, user)
        def objectDescribe = createObjectDescribe(describeApiName)

        when:
        def result = customButtonService.filterButtonsForEdit(user, describeApiName)

        then:
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, describeApiName) >> objectDescribe
        1 * buttonService.findButtonsByDescribeApiName(describeApiName, tenantId) >> buttons
        1 * licenseService.existModule(tenantId, _) >> [(com.facishare.paas.appframework.license.util.LicenseConstants.ModuleCode.MULTI_LANGUAGE_APP): false]

        result.size() >= 0
        result.each { button ->
            assert button.usePages.contains("edit")
        }

        where:
        describeApiName   | tenantId | userId
        "object_1xq2p__c" | "71698"  | "1000"
        "AccountObj"      | "71698"  | "1000"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据描述API名称查找按钮的正常场景
     */
    @Unroll
    def "findButtonsByDescribeApiNameTest"() {
        given:
        def user = createUser(tenantId, userId)
        def buttons = createButtonList(describeApiName, user)
        def objectDescribe = createObjectDescribe(describeApiName)

        when:
        def result = customButtonService.findButtonsByDescribeApiName(user, describeApiName)

        then:
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, describeApiName) >> objectDescribe
        1 * buttonService.findButtonsByDescribeApiName(describeApiName, tenantId) >> buttons
        1 * licenseService.existModule(tenantId, _) >> [(com.facishare.paas.appframework.license.util.LicenseConstants.ModuleCode.MULTI_LANGUAGE_APP): false]

        result.size() >= 0

        where:
        describeApiName   | tenantId | userId
        "object_1xq2p__c" | "71698"  | "1000"
        "AccountObj"      | "71698"  | "1000"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查自定义按钮数量限制的正常场景
     */
    @Unroll
    def "checkCustomButtonCountLimitTest"() {
        given:
        def user = createUser(tenantId, userId)

        when:
        customButtonService.checkCustomButtonCountLimit(user, describeApiName, buttonCount)

        then:
        noExceptionThrown()

        where:
        describeApiName   | tenantId | userId | buttonCount
        "object_1xq2p__c" | "71698"  | "1000" | 10
        "AccountObj"      | "71698"  | "1000" | 5
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据API名称列表和类型查找按钮的正常场景
     */
    @Unroll
    def "findButtonByApiNameListAndTypeTest"() {
        given:
        def user = createUser(tenantId, userId)
        def buttonMap = createButtonMap(apiNameList, buttonType)

        when:
        def result = customButtonService.findButtonByApiNameListAndType(user, apiNameList, buttonType)

        then:
        1 * buttonService.findButtonsByDescribeApiNamesAndType(tenantId, apiNameList, buttonType) >> buttonMap

        result.size() >= 0

        where:
        apiNameList         | buttonType | tenantId | userId
        ["object_1xq2p__c"] | "common"   | "71698"  | "1000"
        ["AccountObj"]      | "redirect" | "71698"  | "1000"
        ["obj1", "obj2"]    | "convert"  | "71698"  | "1000"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试包含禁用按钮的使用页面查找的正常场景
     */
    @Unroll
    def "findCustomButtonByUsePageIncludeDisableTest"() {
        given:
        def user = createUser(tenantId, userId)
        def buttons = createButtonsWithDisabled(describeApiName, user)
        def objectDescribe = createObjectDescribe(describeApiName)

        when:
        def result = customButtonService.findCustomButtonByUsePageIncludeDisable(describeApiName, usePage, user)

        then:
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, describeApiName) >> objectDescribe
        1 * buttonService.findButtonsByDescribeApiName(describeApiName, tenantId) >> buttons

        result.size() >= 0

        where:
        describeApiName   | usePage      | tenantId | userId
        "object_1xq2p__c" | "detail"     | "71698"  | "1000"
        "AccountObj"      | "list_batch" | "71698"  | "1000"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据最后修改时间查找按钮的正常场景
     */
    @Unroll
    def "findButtonsByLastModifiedTimeTest"() {
        given:
        def user = createUser(tenantId, userId)
        def buttons = createButtonList("TestObj", user)
        def lastUpdateTime = System.currentTimeMillis() - 3600000 // 1 hour ago

        when:
        def result = customButtonService.findButtonsByLastModifiedTime(user, lastUpdateTime)

        then:
        1 * buttonService.findByLastModifiedTime(tenantId, com.facishare.paas.metadata.api.describe.IFieldDescribe.DEFINE_TYPE_SYSTEM, lastUpdateTime) >> buttons

        result.size() >= 0

        where:
        tenantId | userId
        "71698"  | "1000"
        "71699"  | "1001"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试为设计器查找按钮的正常场景
     */
    @Unroll
    def "findButtonByApiNameForDesignerTest"() {
        given:
        def user = createUser(tenantId, userId)
        def button = createButton(describeApiName, buttonApiName, buttonJson, user)
        def objectDescribe = createObjectDescribe(describeApiName)

        when:
        def result = customButtonService.findButtonByApiNameForDesigner(user, buttonApiName, describeApiName)

        then:
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, describeApiName) >> objectDescribe
        1 * buttonService.findByApiNameAndTenantId(buttonApiName, describeApiName, tenantId) >> button
        1 * licenseService.existModule(tenantId, _) >> [(com.facishare.paas.appframework.license.util.LicenseConstants.ModuleCode.MULTI_LANGUAGE_APP): false]

        result.apiName == buttonApiName
        result.describeApiName == describeApiName

        where:
        describeApiName   | buttonApiName     | tenantId | userId | buttonJson
        "object_1xq2p__c" | "custom_button_1" | "71698"  | "1000" | commonButtonJson
        "AccountObj"      | "custom_button_2" | "71698"  | "1000" | redirectButtonJson
    }

    def createButtonsForCreate(String describeApiName, User user) {
        def buttons = []
        def createButton = createButton(describeApiName, "create_button", commonButtonJson, user)
        createButton.setUsePages(Lists.newArrayList("create"))
        buttons.add(createButton)
        return buttons
    }

    def createButtonsForEdit(String describeApiName, User user) {
        def buttons = []
        def editButton = createButton(describeApiName, "edit_button", commonButtonJson, user)
        editButton.setUsePages(Lists.newArrayList("edit"))
        buttons.add(editButton)
        return buttons
    }

    def createButtonsWithDisabled(String describeApiName, User user) {
        def buttons = []
        def activeButton = createButton(describeApiName, "active_button", commonButtonJson, user)
        activeButton.setIsActive(true)
        buttons.add(activeButton)

        def disabledButton = createButton(describeApiName, "disabled_button", commonButtonJson, user)
        disabledButton.setIsActive(false)
        buttons.add(disabledButton)

        return buttons
    }

    def createButtonMap(List<String> apiNameList, String buttonType) {
        def buttonMap = [:]
        apiNameList.each { apiName ->
            def buttons = []
            def button = createButton(apiName, "test_button", commonButtonJson, createUser("71698", "1000"))
            button.setButtonType(buttonType)
            buttons.add(button)
            buttonMap.put(apiName, buttons)
        }
        return buttonMap
    }

    def createUser(String tenantId, String userId) {
        return new User(tenantId, userId)
    }

    def createButton(String describeApiName, String buttonApiName, String buttonJson, User user) {
        UdefButton button = new UdefButton()
        button.fromJsonString(buttonJson)
        button.setDescribeApiName(describeApiName)
        button.setApiName(buttonApiName)
        button.setTenantId(user.getTenantId())
        if (isDefaultButton(buttonApiName)) {
            button.setDefineType("system")
        } else {
            button.setDefineType("custom")
        }
        return button
    }

    def createObjectDescribe(String describeApiName) {
        def describe = new ObjectDescribe()
        describe.setApiName(describeApiName)
        describe.setDisplayName("测试对象")
        describe.setCreateTime(System.currentTimeMillis())
        describe.setCreatedBy("testUser")
        return describe
    }

    def createButtonList(String describeApiName, User user) {
        def buttons = []
        buttons.add(createButton(describeApiName, "Add_button_default", addButtonJson, user))
        buttons.add(createButton(describeApiName, "Edit_button_default", commonButtonJson, user))
        return buttons
    }

    def isDefaultButton(String buttonApiName) {
        // 使用硬编码的默认按钮列表，避免静态字段访问问题
        def defaultButtons = ["Add_button_default", "Edit_button_default"]
        return defaultButtons.contains(buttonApiName)
    }

    def customButton() {
        def buttonJson = '''{"_id":"5af42526a5083db98b3b019e","api_name":"button_5wly1__c","tenant_id":"71698",
"describe_api_name":"object_1xq2p__c","description":"","label":"自定义按钮1","created_by":"1000","create_time":1525949734135,"last_modified_time":1568974804527,"button_type":"redirect","use_pages":["detail","list_batch"],"jump_url":null,"define_type":null,"last_modified_by":"1000","is_active":true,"is_deleted":false,"version":90,"is_batch":null,"url":null}'''
        def button = new UdefButton()
        button.fromJsonString(buttonJson)
        return button
    }

    def lockButton() {
        def buttonJson = '''{"_id":"5af42526a5083db98b3b019e","api_name":"Lock_button_default","tenant_id":"71698","describe_api_name":"object_1xq2p__c","description":"","label":"自定义按钮1","created_by":"1000","create_time":1525949734135,"last_modified_time":1568974804527,"button_type":"redirect","use_pages":["detail"],"jump_url":null,"define_type":null,"last_modified_by":"1000","is_active":true,"is_deleted":false,"version":90,"is_batch":null,"url":null}'''
        def button = new UdefButton()
        button.fromJsonString(buttonJson)
        return button
    }

    def editButton() {
        def buttonJson = '''{"wheres":[],"param_form":[],"actions":[],"_id":"standard_edit_button_id","api_name":"Edit_button_default","description":"","label":"编辑保存","button_type":"common","use_pages":["edit"],"is_active":true,"is_deleted":false,"version":1,"tenant_id":"71698","created_by":"1000","last_modified_by":"1000","create_time":1554949897535,"last_modified_time":1569231167255,"describe_api_name":"object_e9J2F__c","define_type":"system"}'''
        def button = new UdefButton()
        button.fromJsonString(buttonJson)
        return button
    }

    private void initButtonConfig() {
        def udefButtenConfigJson = '''{"api_name":"udef_button","descrieb_api_name":"udef","tenant_id":"-100",
"type":"udef_button_config","config":{"type":"button_config","delete":0,"edit":1,"attr":{"api_name":{"edit":0},
"description":{"edit":1},"label":{"edit":1},"button_type":{"edit":0},"roles":{"edit":0},"wheres":{"edit":1},
"param_form":{"edit":1},"use_pages":{"edit":0,"position":0,"position_pages":0},"stage_actions":{"pre":{"edit":1},
"current":{"edit":0},"post":{"edit":1}}}},"edit":1}'''
        def addButtonConfigJson = '''{"api_name":"Add_button_default","descrieb_api_name":"udef",
"tenant_id":"-100","type":"udef_button_config","config":{"type":"button_config","delete":0,"edit":1,
"attr":{"api_name":{"edit":0},"description":{"edit":1},"label":{"edit":1},"button_type":{"edit":0},"roles":{"edit":0},
"wheres":{"edit":1},"param_form":{"edit":1},"use_pages":{"edit":0,"position":0,"position_pages":0},
"stage_actions":{"pre":{"edit":1},"current":{"edit":0},"post":{"edit":1}}}},"edit":0}'''
        def allocateButtonConfigJson = '''{"api_name":"AccountObj_Allocate_button_default","descrieb_api_name":"AccountObj",
"tenant_id":"-100","type":"udef_button_config","config":{"type":"button_config","delete":0,"edit":1,
"attr":{"api_name":{"edit":0},"description":{"edit":1},"label":{"edit":1},"button_type":{"edit":0},"roles":{"edit":0},
"wheres":{"edit":1},"param_form":{"edit":1},"use_pages":{"edit":0,"position":0,"position_pages":0},
"stage_actions":{"pre":{"edit":1},"current":{"edit":0},"post":{"edit":1}}}},"edit":0}'''

        def udefButtenConfig = UdefButtonConfig.fromJson(udefButtenConfigJson)
        ButtonConfig.BUTTON_CONFIG_TABLE.put(DefObjConstants.UDOBJ, ButtonConfig.UDEF_BUTTON, udefButtenConfig)
        def addButtonConfig = UdefButtonConfig.fromJson(addButtonConfigJson)
        ButtonConfig.BUTTON_CONFIG_TABLE.put(DefObjConstants.UDOBJ, "Add_button_default", addButtonConfig)
        def allocateButtonConfig = UdefButtonConfig.fromJson(allocateButtonConfigJson)
        ButtonConfig.BUTTON_CONFIG_TABLE.put("AccountObj", "AccountObj_Allocate_button_default", allocateButtonConfig)
    }

    def "查询按钮config 根据buttonType判断按钮是否支持编辑"() {
        given:
        when:
        IUdefButtonConfig config = customButtonService.findButtonConfigByApiName(describeApiName, buttonApiName, createUser(tenantId, userId))
        then:
        1 * describeLogicService.findObjectWithoutCopyIfGray(tenantId, describeApiName) >> createObjectDescribe(describeApiName)
        1 * buttonService.findByApiNameAndTenantId(_ as String, _ as String, _ as String) >> {
            return createButton(describeApiName, buttonApiName, buttonJson, createUser(tenantId, userId))
        }
        config.getEdit() == edit
        where:
        describeApiName   | buttonApiName        | tenantId | userId | buttonJson         | edit
        "object_1xq2p__c" | "Add_button_default" | "71698"  | "1000" | addButtonJson      | 0
        "AccountObj"      | "btn_2QMUm__c"       | "71698"  | "1000" | convertButtonJson  | 1
        "AccountObj"      | "button_O27as__c"    | "71698"  | "1000" | commonButtonJson   | 1
        "AccountObj"      | "button_dMTc2__c"    | "71698"  | "1000" | redirectButtonJson | 0
    }

    def "findCustomButtonByUsePage"() {
        given:
        def user = createUser(tenantId, userId)
        def objectDescribe = createObjectDescribe(describeApiName)
        when:
        def buttons = customButtonService.findCustomButtonByUsePage(describeApiName, usePage, user)
        then:
        (1.._) * describeLogicService.findObjectWithoutCopyIfGray(tenantId, describeApiName) >> objectDescribe
        1 * buttonService.findButtonsByDescribeApiName(describeApiName, tenantId) >> resultButton

        buttons.size() == buttonCount
        where:
        tenantId | userId | describeApiName   | usePage      | resultButton     || buttonCount
        "71698"  | "1000" | "object_e9J2F__c" | "detail"     | []               || 6
        "71698"  | "1000" | "object_e9J2F__c" | "list_batch" | []               || 6
        "71698"  | "1000" | "object_e9J2F__c" | "detail"     | [editButton()]   || 6
        "71698"  | "1000" | "object_e9J2F__c" | "detail"     | [lockButton()]   || 6
        "71698"  | "1000" | "object_e9J2F__c" | "list_batch" | [lockButton()]   || 5
        "71698"  | "1000" | "object_e9J2F__c" | "list_batch" | [customButton()] || 7
    }
}
