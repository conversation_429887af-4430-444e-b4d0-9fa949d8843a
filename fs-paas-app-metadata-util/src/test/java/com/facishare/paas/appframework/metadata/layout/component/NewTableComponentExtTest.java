package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.Quote;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import com.facishare.paas.metadata.impl.describe.SignInFieldDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * NewTableComponentExt单元测试类
 */
@ExtendWith(MockitoExtension.class)
class NewTableComponentExtTest {

  private NewTableComponentExt newTableComponentExt;

  @BeforeEach
  void setUp() {
    newTableComponentExt = new NewTableComponentExt();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试默认构造函数创建对象
   */
  @Test
  @DisplayName("测试默认构造函数")
  void testDefaultConstructor() {
    NewTableComponentExt component = new NewTableComponentExt();
    assertNotNull(component);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试Map构造函数创建对象
   */
  @Test
  @DisplayName("测试Map构造函数")
  void testMapConstructor() {
    Map<String, Object> map = new HashMap<>();
    map.put("show_image", "testImage");
    
    NewTableComponentExt component = new NewTableComponentExt(map);
    
    assertNotNull(component);
    assertEquals("testImage", component.getShowImage());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试showImage字段的getter和setter方法
   */
  @Test
  @DisplayName("测试showImage字段的getter和setter")
  void testShowImageGetterAndSetter() {
    String testShowImage = "testShowImage";
    
    newTableComponentExt.setShowImage(testShowImage);
    
    assertEquals(testShowImage, newTableComponentExt.getShowImage());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getRowSections方法当rowSections为null时返回空列表
   */
  @Test
  @DisplayName("测试getRowSections方法 - rowSections为null时返回空列表")
  void testGetRowSectionsWhenNull() {
    List<RowSectionComponentExt> result = newTableComponentExt.getRowSections();
    
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getRowSections方法当rowSections不为空时返回RowSectionComponentExt列表
   */
  @Test
  @DisplayName("测试getRowSections方法 - rowSections不为空时返回RowSectionComponentExt列表")
  void testGetRowSectionsWhenNotEmpty() {
    // 准备测试数据
    Map<String, Object> rowSectionMap1 = new HashMap<>();
    rowSectionMap1.put("field_section", Lists.newArrayList());
    
    Map<String, Object> rowSectionMap2 = new HashMap<>();
    rowSectionMap2.put("field_section", Lists.newArrayList());
    
    List<Map> rowSectionsList = Lists.newArrayList(rowSectionMap1, rowSectionMap2);
    newTableComponentExt.set("row_sections", rowSectionsList);
    
    // 执行测试
    List<RowSectionComponentExt> result = newTableComponentExt.getRowSections();
    
    // 验证结果
    assertNotNull(result);
    assertEquals(2, result.size());
    assertTrue(result.get(0) instanceof RowSectionComponentExt);
    assertTrue(result.get(1) instanceof RowSectionComponentExt);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试addRowSection方法添加RowSectionComponentExt到空列表
   */
  @Test
  @DisplayName("测试addRowSection方法 - 添加到空列表")
  void testAddRowSectionToEmptyList() {
    // 准备测试数据
    RowSectionComponentExt rowSection = new RowSectionComponentExt();

    // 执行测试
    newTableComponentExt.addRowSection(rowSection);

    // 验证结果
    List<RowSectionComponentExt> rowSections = newTableComponentExt.getRowSections();
    assertNotNull(rowSections);
    assertEquals(1, rowSections.size());
    assertEquals(rowSection, rowSections.get(0));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertToTableColumn方法当没有字段时返回空列表
   */
  @Test
  @DisplayName("测试convertToTableColumn方法 - 没有字段时返回空列表")
  void testConvertToTableColumnWhenNoFields() {
    List<ITableColumn> result = newTableComponentExt.convertToTableColumn();
    
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertToTableColumn方法当有字段时返回TableColumn列表
   */
  @Test
  @DisplayName("测试convertToTableColumn方法 - 有字段时返回TableColumn列表")
  void testConvertToTableColumnWhenHasFields() {
    // 准备测试数据 - 创建包含字段的行和节
    FieldComponentExt fieldComponent1 = new FieldComponentExt();
    fieldComponent1.setType("field");
    fieldComponent1.setApiName("field1");
    fieldComponent1.setRenderType("text");
    
    FieldComponentExt fieldComponent2 = new FieldComponentExt();
    fieldComponent2.setType("field");
    fieldComponent2.setApiName("field2");
    fieldComponent2.setRenderType("text");
    
    FieldSectionComponentExt fieldSection = new FieldSectionComponentExt();
    fieldSection.addFields(fieldComponent1);
    fieldSection.addFields(fieldComponent2);
    
    RowSectionComponentExt rowSection = new RowSectionComponentExt();
    rowSection.addFieldSection(fieldSection);
    
    newTableComponentExt.addRowSection(rowSection);
    
    // 执行测试
    List<ITableColumn> result = newTableComponentExt.convertToTableColumn();
    
    // 验证结果
    assertNotNull(result);
    assertEquals(2, result.size());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeInactiveFields方法移除非活跃字段
   */
  @Test
  @DisplayName("测试removeInactiveFields方法")
  void testRemoveInactiveFields() {
    try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {
      // 准备测试数据
      IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
      ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
      
      mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockDescribeExt);
      when(mockDescribeExt.isFieldActive(anyString())).thenReturn(true);
      
      // 设置showImage字段
      newTableComponentExt.setShowImage("testImageField");
      
      // 执行测试
      newTableComponentExt.removeInactiveFields(mockDescribe);
      
      // 验证ObjectDescribeExt.of被调用
      mockedObjectDescribeExt.verify(() -> ObjectDescribeExt.of(mockDescribe));
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeByFieldNames方法当fieldNames为空时不执行删除
   */
  @Test
  @DisplayName("测试removeByFieldNames方法 - fieldNames为空时不执行删除")
  void testRemoveByFieldNamesWhenEmpty() {
    try (MockedStatic<CollectionUtils> mockedCollectionUtils = mockStatic(CollectionUtils.class)) {
      mockedCollectionUtils.when(() -> CollectionUtils.empty(any(Collection.class))).thenReturn(true);
      
      // 执行测试
      newTableComponentExt.removeByFieldNames(null);
      
      // 验证没有异常抛出即可
      assertNotNull(newTableComponentExt);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeByFieldNames方法当fieldNames不为空时执行删除
   */
  @Test
  @DisplayName("测试removeByFieldNames方法 - fieldNames不为空时执行删除")
  void testRemoveByFieldNamesWhenNotEmpty() {
    try (MockedStatic<CollectionUtils> mockedCollectionUtils = mockStatic(CollectionUtils.class)) {
      mockedCollectionUtils.when(() -> CollectionUtils.empty(any(Collection.class))).thenReturn(false);
      
      Set<String> fieldNames = new HashSet<>();
      fieldNames.add("field1");
      fieldNames.add("field2");
      
      // 执行测试
      newTableComponentExt.removeByFieldNames(fieldNames);
      
      // 验证CollectionUtils.empty被调用
      mockedCollectionUtils.verify(() -> CollectionUtils.empty(fieldNames));
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeShowImage方法
   */
  @Test
  @DisplayName("测试removeShowImage方法")
  void testRemoveShowImage() {
    // 先设置showImage
    newTableComponentExt.setShowImage("testImage");
    assertEquals("testImage", newTableComponentExt.getShowImage());
    
    // 执行removeShowImage
    newTableComponentExt.removeShowImage();
    
    // 验证showImage被设置为null
    assertNull(newTableComponentExt.getShowImage());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试isShowTag方法当没有tag字段时返回false
   */
  @Test
  @DisplayName("测试isShowTag方法 - 没有tag字段时返回false")
  void testIsShowTagWhenNoTag() {
    boolean result = newTableComponentExt.isShowTag();
    
    assertFalse(result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试isShowTag方法当有tag字段时返回true
   */
  @Test
  @DisplayName("测试isShowTag方法 - 有tag字段时返回true")
  void testIsShowTagWhenHasTag() {
    // 准备测试数据 - 创建包含tag字段的行和节
    FieldComponentExt tagField = new FieldComponentExt();
    tagField.setType("tag");
    tagField.setApiName("tagField");
    
    FieldSectionComponentExt fieldSection = new FieldSectionComponentExt();
    fieldSection.addFields(tagField);
    
    RowSectionComponentExt rowSection = new RowSectionComponentExt();
    rowSection.addFieldSection(fieldSection);
    
    newTableComponentExt.addRowSection(rowSection);
    
    // 执行测试
    boolean result = newTableComponentExt.isShowTag();
    
    // 验证结果
    assertTrue(result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getFieldList方法返回字段API名称列表
   */
  @Test
  @DisplayName("测试getFieldList方法")
  void testGetFieldList() {
    // 准备测试数据
    FieldComponentExt fieldComponent1 = new FieldComponentExt();
    fieldComponent1.setType("field");
    fieldComponent1.setApiName("field1");
    
    FieldComponentExt fieldComponent2 = new FieldComponentExt();
    fieldComponent2.setType("field");
    fieldComponent2.setApiName("field2");
    
    FieldComponentExt tagComponent = new FieldComponentExt();
    tagComponent.setType("tag");
    tagComponent.setApiName("tagField");
    
    FieldSectionComponentExt fieldSection = new FieldSectionComponentExt();
    fieldSection.addFields(fieldComponent1);
    fieldSection.addFields(fieldComponent2);
    fieldSection.addFields(tagComponent);
    
    RowSectionComponentExt rowSection = new RowSectionComponentExt();
    rowSection.addFieldSection(fieldSection);
    
    newTableComponentExt.addRowSection(rowSection);
    
    // 执行测试
    List<String> result = newTableComponentExt.getFieldList();
    
    // 验证结果
    assertNotNull(result);
    assertEquals(2, result.size()); // 只包含field类型的字段，不包含tag
    assertTrue(result.contains("field1"));
    assertTrue(result.contains("field2"));
    assertFalse(result.contains("tagField"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setDefaultFieldListIfEmpty方法当字段为空时设置默认字段
   */
  @Test
  @DisplayName("测试setDefaultFieldListIfEmpty方法")
  void testSetDefaultFieldListIfEmpty() {
    // 执行测试 - 当前没有任何字段
    newTableComponentExt.setDefaultFieldListIfEmpty();

    // 验证结果 - 应该添加了默认的name字段
    List<RowSectionComponentExt> rowSections = newTableComponentExt.getRowSections();
    assertNotNull(rowSections);
    assertEquals(1, rowSections.size());

    RowSectionComponentExt rowSection = rowSections.get(0);
    List<FieldSectionComponentExt> fieldSections = rowSection.getFieldSection();
    assertEquals(1, fieldSections.size());

    FieldSectionComponentExt fieldSection = fieldSections.get(0);
    List<FieldComponentExt> fields = fieldSection.getFields();
    assertEquals(1, fields.size());

    FieldComponentExt field = fields.get(0);
    assertEquals("name", field.getApiName());
    assertEquals("field", field.getType());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeByTypes方法按字段类型移除字段
   */
  @Test
  @DisplayName("测试removeByTypes方法按字段类型移除字段")
  void testRemoveByTypes() {
    try (MockedStatic<CollectionUtils> mockedCollectionUtils = mockStatic(CollectionUtils.class);
         MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {

      // 准备测试数据
      IObjectDescribe mockDescribe = mock(IObjectDescribe.class);

      mockedCollectionUtils.when(() -> CollectionUtils.empty(any(Collection.class))).thenReturn(false);

      Set<String> fieldTypes = new HashSet<>();
      fieldTypes.add("text");
      fieldTypes.add("number");

      // 执行测试
      newTableComponentExt.removeByTypes(mockDescribe, fieldTypes);

      // 验证CollectionUtils.empty被调用
      mockedCollectionUtils.verify(() -> CollectionUtils.empty(fieldTypes));
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeByTypes方法当fieldTypes为空时不执行删除
   */
  @Test
  @DisplayName("测试removeByTypes方法当fieldTypes为空时不执行删除")
  void testRemoveByTypesWhenEmpty() {
    try (MockedStatic<CollectionUtils> mockedCollectionUtils = mockStatic(CollectionUtils.class)) {
      mockedCollectionUtils.when(() -> CollectionUtils.empty(any(Collection.class))).thenReturn(true);

      IObjectDescribe mockDescribe = mock(IObjectDescribe.class);

      // 执行测试
      newTableComponentExt.removeByTypes(mockDescribe, null);

      // 验证没有异常抛出即可
      assertNotNull(newTableComponentExt);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试adjustFieldRenderType方法调整字段渲染类型
   */
  @Test
  @DisplayName("测试adjustFieldRenderType方法调整字段渲染类型")
  void testAdjustFieldRenderType() {
    try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
         MockedStatic<LayoutExt> mockedLayoutExt = mockStatic(LayoutExt.class)) {

      // 准备测试数据
      IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
      ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
      IFieldDescribe mockFieldDescribe = mock(IFieldDescribe.class);

      when(mockDescribe.getApiName()).thenReturn("test_object");
      mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockDescribeExt);
      when(mockDescribeExt.getFieldDescribeSilently(anyString())).thenReturn(Optional.of(mockFieldDescribe));
      when(mockFieldDescribe.getType()).thenReturn("text");
      mockedLayoutExt.when(() -> LayoutExt.getRenderType(anyString(), anyString(), anyString())).thenReturn("text");

      // 创建包含字段的测试数据
      FieldComponentExt fieldComponent = new FieldComponentExt();
      fieldComponent.setType("field");
      fieldComponent.setApiName("test_field");
      fieldComponent.setRenderType("old_type");

      FieldSectionComponentExt fieldSection = new FieldSectionComponentExt();
      fieldSection.addFields(fieldComponent);

      RowSectionComponentExt rowSection = new RowSectionComponentExt();
      rowSection.addFieldSection(fieldSection);

      newTableComponentExt.addRowSection(rowSection);

      // 执行测试
      newTableComponentExt.adjustFieldRenderType(mockDescribe);

      // 验证ObjectDescribeExt.of被调用
      mockedObjectDescribeExt.verify(() -> ObjectDescribeExt.of(mockDescribe));
      // 验证LayoutExt.getRenderType被调用
      mockedLayoutExt.verify(() -> LayoutExt.getRenderType(eq("test_object"), eq("test_field"), eq("text")));
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试correctLabel方法修正字段标签
   */
  @Test
  @DisplayName("测试correctLabel方法修正字段标签")
  void testCorrectLabel() {
    try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {
      // 准备测试数据
      IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
      ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
      IFieldDescribe mockFieldDescribe = mock(IFieldDescribe.class);

      mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockDescribeExt);
      when(mockDescribeExt.getFieldDescribeSilently(anyString())).thenReturn(Optional.of(mockFieldDescribe));
      when(mockFieldDescribe.getLabel()).thenReturn("测试标签");

      // 创建包含字段的测试数据
      FieldComponentExt fieldComponent = new FieldComponentExt();
      fieldComponent.setType("field");
      fieldComponent.setApiName("test_field");
      fieldComponent.setLabel("旧标签");

      FieldSectionComponentExt fieldSection = new FieldSectionComponentExt();
      fieldSection.addFields(fieldComponent);

      RowSectionComponentExt rowSection = new RowSectionComponentExt();
      rowSection.addFieldSection(fieldSection);

      newTableComponentExt.addRowSection(rowSection);

      // 执行测试
      newTableComponentExt.correctLabel(mockDescribe);

      // 验证ObjectDescribeExt.of被调用
      mockedObjectDescribeExt.verify(() -> ObjectDescribeExt.of(mockDescribe));
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertToTableColumn方法超过8个字段时的限制
   */
  @Test
  @DisplayName("测试convertToTableColumn方法超过8个字段时的限制")
  void testConvertToTableColumnWithMoreThan8Fields() {
    // 准备测试数据 - 创建超过8个字段
    FieldSectionComponentExt fieldSection = new FieldSectionComponentExt();
    for (int i = 1; i <= 10; i++) {
      FieldComponentExt fieldComponent = new FieldComponentExt();
      fieldComponent.setType("field");
      fieldComponent.setApiName("field" + i);
      fieldComponent.setRenderType("text");
      fieldSection.addFields(fieldComponent);
    }

    RowSectionComponentExt rowSection = new RowSectionComponentExt();
    rowSection.addFieldSection(fieldSection);

    newTableComponentExt.addRowSection(rowSection);

    // 执行测试
    List<ITableColumn> result = newTableComponentExt.convertToTableColumn();

    // 验证结果 - 应该只返回8个字段
    assertNotNull(result);
    assertEquals(8, result.size());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试formatNewLayoutByDescribe方法的核心功能
   */
  @Test
  @DisplayName("测试formatNewLayoutByDescribe方法的核心功能")
  void testFormatNewLayoutByDescribe() {
    try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
         MockedStatic<RequestUtil> mockedRequestUtil = mockStatic(RequestUtil.class);
         MockedStatic<LayoutExt> mockedLayoutExt = mockStatic(LayoutExt.class)) {

      // 准备测试数据
      IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
      ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
      IFieldDescribe mockFieldDescribe = mock(IFieldDescribe.class);
      User mockUser = mock(User.class);

      when(mockDescribe.getApiName()).thenReturn("test_object");
      mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockDescribeExt);
      when(mockDescribeExt.isFieldActive(anyString())).thenReturn(true);
      when(mockDescribeExt.getFieldDescribeSilently(anyString())).thenReturn(Optional.of(mockFieldDescribe));
      when(mockDescribeExt.getSignInFieldDescribe()).thenReturn(Optional.empty());
      when(mockDescribeExt.getQuoteFieldDescribes()).thenReturn(new ArrayList<>());
      when(mockFieldDescribe.getType()).thenReturn("text");
      when(mockFieldDescribe.getLabel()).thenReturn("测试标签");

      mockedRequestUtil.when(() -> RequestUtil.isMobileRequestBeforeVersion(anyString())).thenReturn(false);
      when(mockUser.isOutUser()).thenReturn(false);
      mockedLayoutExt.when(() -> LayoutExt.getRenderType(anyString(), anyString(), anyString())).thenReturn("text");

      // 创建包含字段的测试数据
      FieldComponentExt fieldComponent = new FieldComponentExt();
      fieldComponent.setType("field");
      fieldComponent.setApiName("test_field");
      fieldComponent.setRenderType("old_type");

      FieldSectionComponentExt fieldSection = new FieldSectionComponentExt();
      fieldSection.addFields(fieldComponent);

      RowSectionComponentExt rowSection = new RowSectionComponentExt();
      rowSection.addFieldSection(fieldSection);

      newTableComponentExt.addRowSection(rowSection);
      newTableComponentExt.setShowImage("test_image_field");

      Set<String> unauthorizedFields = new HashSet<>();
      unauthorizedFields.add("unauthorized_field");

      // 执行测试
      newTableComponentExt.formatNewLayoutByDescribe(mockDescribe, unauthorizedFields, mockUser, true);

      // 验证关键方法被调用
      mockedObjectDescribeExt.verify(() -> ObjectDescribeExt.of(mockDescribe), times(5));
      verify(mockDescribeExt, atLeast(1)).isFieldActive(anyString());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试formatNewLayoutByDescribe方法处理移动端版本限制
   */
  @Test
  @DisplayName("测试formatNewLayoutByDescribe方法处理移动端版本限制")
  void testFormatNewLayoutByDescribeWithMobileVersionLimits() {
    try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
         MockedStatic<RequestUtil> mockedRequestUtil = mockStatic(RequestUtil.class)) {

      // 准备测试数据
      IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
      ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
      User mockUser = mock(User.class);

      mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockDescribeExt);
      when(mockDescribeExt.getSignInFieldDescribe()).thenReturn(Optional.empty());
      when(mockDescribeExt.getQuoteFieldDescribes()).thenReturn(new ArrayList<>());

      // 模拟移动端版本限制 - 使用实际的版本号格式
      mockedRequestUtil.when(() -> RequestUtil.isMobileRequestBeforeVersion("735000")).thenReturn(true);

      Set<String> unauthorizedFields = new HashSet<>();

      // 执行测试
      newTableComponentExt.formatNewLayoutByDescribe(mockDescribe, unauthorizedFields, mockUser, false);

      // 验证版本检查被调用
      mockedRequestUtil.verify(() -> RequestUtil.isMobileRequestBeforeVersion("735000"));
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试formatNewLayoutByDescribe方法处理外部用户限制
   */
  @Test
  @DisplayName("测试formatNewLayoutByDescribe方法处理外部用户限制")
  void testFormatNewLayoutByDescribeWithOutUser() {
    try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {

      // 准备测试数据
      IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
      ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
      User mockUser = mock(User.class);

      mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockDescribeExt);
      when(mockDescribeExt.getSignInFieldDescribe()).thenReturn(Optional.empty());
      when(mockDescribeExt.getQuoteFieldDescribes()).thenReturn(new ArrayList<>());

      // 模拟外部用户
      when(mockUser.isOutUser()).thenReturn(true);

      Set<String> unauthorizedFields = new HashSet<>();

      // 执行测试
      newTableComponentExt.formatNewLayoutByDescribe(mockDescribe, unauthorizedFields, mockUser, false);

      // 验证外部用户检查被调用
      verify(mockUser).isOutUser();
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试removeInactiveFields方法移除非活跃的图片字段
   */
  @Test
  @DisplayName("测试removeInactiveFields方法移除非活跃的图片字段")
  void testRemoveInactiveFieldsWithInactiveImageField() {
    try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
         MockedStatic<Strings> mockedStrings = mockStatic(com.google.common.base.Strings.class)) {

      // 准备测试数据
      IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
      ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);

      mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockDescribeExt);
      when(mockDescribeExt.isFieldActive("testImageField")).thenReturn(false);
      mockedStrings.when(() -> Strings.isNullOrEmpty("testImageField")).thenReturn(false);

      // 设置图片字段
      newTableComponentExt.setShowImage("testImageField");

      // 执行测试
      newTableComponentExt.removeInactiveFields(mockDescribe);

      // 验证图片字段被移除
      assertNull(newTableComponentExt.getShowImage());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setDefaultFieldListIfEmpty方法当已有字段时不添加默认字段
   */
  @Test
  @DisplayName("测试setDefaultFieldListIfEmpty方法当已有字段时不添加默认字段")
  void testSetDefaultFieldListIfEmptyWhenHasFields() {
    // 准备测试数据 - 添加一个现有字段
    FieldComponentExt existingField = new FieldComponentExt();
    existingField.setType("field");
    existingField.setApiName("existing_field");

    FieldSectionComponentExt fieldSection = new FieldSectionComponentExt();
    fieldSection.addFields(existingField);

    RowSectionComponentExt rowSection = new RowSectionComponentExt();
    rowSection.addFieldSection(fieldSection);

    newTableComponentExt.addRowSection(rowSection);

    // 执行测试
    newTableComponentExt.setDefaultFieldListIfEmpty();

    // 验证结果 - 应该仍然只有一个字段
    List<RowSectionComponentExt> rowSections = newTableComponentExt.getRowSections();
    assertEquals(1, rowSections.size());

    RowSectionComponentExt resultRowSection = rowSections.get(0);
    List<FieldSectionComponentExt> fieldSections = resultRowSection.getFieldSection();
    assertEquals(1, fieldSections.size());

    FieldSectionComponentExt resultFieldSection = fieldSections.get(0);
    List<FieldComponentExt> fields = resultFieldSection.getFields();
    assertEquals(1, fields.size());

    FieldComponentExt field = fields.get(0);
    assertEquals("existing_field", field.getApiName());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试setDefaultFieldListIfEmpty方法当有tag字段时不添加默认字段
   */
  @Test
  @DisplayName("测试setDefaultFieldListIfEmpty方法当有tag字段时不添加默认字段")
  void testSetDefaultFieldListIfEmptyWhenHasTagFields() {
    // 准备测试数据 - 添加一个tag字段
    FieldComponentExt tagField = new FieldComponentExt();
    tagField.setType("tag");
    tagField.setApiName("tag_field");

    FieldSectionComponentExt fieldSection = new FieldSectionComponentExt();
    fieldSection.addFields(tagField);

    RowSectionComponentExt rowSection = new RowSectionComponentExt();
    rowSection.addFieldSection(fieldSection);

    newTableComponentExt.addRowSection(rowSection);

    // 执行测试
    newTableComponentExt.setDefaultFieldListIfEmpty();

    // 验证结果 - 应该仍然只有一个tag字段
    List<RowSectionComponentExt> rowSections = newTableComponentExt.getRowSections();
    assertEquals(1, rowSections.size());

    RowSectionComponentExt resultRowSection = rowSections.get(0);
    List<FieldSectionComponentExt> fieldSections = resultRowSection.getFieldSection();
    assertEquals(1, fieldSections.size());

    FieldSectionComponentExt resultFieldSection = fieldSections.get(0);
    List<FieldComponentExt> fields = resultFieldSection.getFields();
    assertEquals(1, fields.size());

    FieldComponentExt field = fields.get(0);
    assertEquals("tag_field", field.getApiName());
    assertEquals("tag", field.getType());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试convertToTableColumn方法当字段的toTableColumn返回null时
   */
  @Test
  @DisplayName("测试convertToTableColumn方法当字段的toTableColumn返回null时")
  void testConvertToTableColumnWhenFieldReturnsNull() {
    // 准备测试数据 - 创建非field类型的字段
    FieldComponentExt nonFieldComponent = new FieldComponentExt();
    nonFieldComponent.setType("custom"); // 不是field类型
    nonFieldComponent.setApiName("custom_field");

    FieldSectionComponentExt fieldSection = new FieldSectionComponentExt();
    fieldSection.addFields(nonFieldComponent);

    RowSectionComponentExt rowSection = new RowSectionComponentExt();
    rowSection.addFieldSection(fieldSection);

    newTableComponentExt.addRowSection(rowSection);

    // 执行测试
    List<ITableColumn> result = newTableComponentExt.convertToTableColumn();

    // 验证结果 - 应该返回空列表，因为非field类型的字段toTableColumn返回null
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试JsonCompatible接口的实现
   */
  @Test
  @DisplayName("测试JsonCompatible接口实现")
  void testJsonCompatibleInterface() {
    // 设置一些数据
    newTableComponentExt.setShowImage("test_image");

    // 测试toJsonString方法
    String jsonString = newTableComponentExt.toJsonString();
    assertNotNull(jsonString);
    assertTrue(jsonString.contains("test_image"));

    // 测试fromJsonString方法
    NewTableComponentExt newComponent = new NewTableComponentExt();
    newComponent.fromJsonString(jsonString);
    assertEquals("test_image", newComponent.getShowImage());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试formatNewLayoutByDescribe方法处理签到字段lambda
   */
  @Test
  @DisplayName("测试formatNewLayoutByDescribe方法处理签到字段lambda")
  void testFormatNewLayoutByDescribeWithSignInField() {
    try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
         MockedStatic<RequestUtil> mockedRequestUtil = mockStatic(RequestUtil.class)) {

      // 准备测试数据
      IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
      when(mockDescribe.getApiName()).thenReturn("TestObject");

      ObjectDescribeExt mockObjectDescribeExt = mock(ObjectDescribeExt.class);
      mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockObjectDescribeExt);

      // 创建签到字段描述
      SignInFieldDescribe mockSignInFieldDescribe = mock(SignInFieldDescribe.class);
      when(mockSignInFieldDescribe.getSignInInfoListFieldApiName()).thenReturn("sign_in_field");
      when(mockObjectDescribeExt.getSignInFieldDescribe()).thenReturn(Optional.of(mockSignInFieldDescribe));

      // 设置其他必要的mock
      when(mockObjectDescribeExt.getQuoteFieldDescribes()).thenReturn(Lists.newArrayList());
      mockedRequestUtil.when(() -> RequestUtil.isMobileRequestBeforeVersion(anyString())).thenReturn(false);

      User mockUser = mock(User.class);
      when(mockUser.isOutUser()).thenReturn(false);

      Set<String> unauthorizedFields = new HashSet<>();

      // 执行测试
      newTableComponentExt.formatNewLayoutByDescribe(mockDescribe, unauthorizedFields, mockUser, false);

      // 验证签到字段描述被调用
      verify(mockObjectDescribeExt).getSignInFieldDescribe();
      verify(mockSignInFieldDescribe).getSignInInfoListFieldApiName();
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试formatNewLayoutByDescribe方法处理不支持的引用字段lambda
   */
  @Test
  @DisplayName("测试formatNewLayoutByDescribe方法处理不支持的引用字段lambda")
  void testFormatNewLayoutByDescribeWithUnsupportedQuoteField() {
    try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
         MockedStatic<RequestUtil> mockedRequestUtil = mockStatic(RequestUtil.class);
         MockedStatic<CollectionUtils> mockedCollectionUtils = mockStatic(CollectionUtils.class)) {

      // 准备测试数据
      IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
      when(mockDescribe.getApiName()).thenReturn("TestObject");

      ObjectDescribeExt mockObjectDescribeExt = mock(ObjectDescribeExt.class);
      mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockObjectDescribeExt);

      // 创建不支持的引用字段
      Quote mockQuote = mock(Quote.class);
      when(mockQuote.getQuoteFieldType()).thenReturn("file_attachment"); // 使用实际的不支持类型
      when(mockQuote.getApiName()).thenReturn("unsupported_quote_field");

      List<Quote> quoteFields = Lists.newArrayList(mockQuote);
      when(mockObjectDescribeExt.getQuoteFieldDescribes()).thenReturn(quoteFields);
      when(mockObjectDescribeExt.getSignInFieldDescribe()).thenReturn(Optional.empty());

      // Mock CollectionUtils.nullToEmpty
      mockedCollectionUtils.when(() -> CollectionUtils.nullToEmpty(quoteFields)).thenReturn(quoteFields);

      mockedRequestUtil.when(() -> RequestUtil.isMobileRequestBeforeVersion(anyString())).thenReturn(false);

      User mockUser = mock(User.class);
      when(mockUser.isOutUser()).thenReturn(false);

      Set<String> unauthorizedFields = new HashSet<>();

      // 执行测试
      newTableComponentExt.formatNewLayoutByDescribe(mockDescribe, unauthorizedFields, mockUser, false);

      // 验证引用字段描述被调用
      verify(mockObjectDescribeExt).getQuoteFieldDescribes();
      verify(mockQuote).getQuoteFieldType();
      verify(mockQuote).getApiName();
    }
  }
}
