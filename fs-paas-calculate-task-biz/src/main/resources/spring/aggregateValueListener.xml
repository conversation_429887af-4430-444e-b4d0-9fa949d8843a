<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!--增量计算预处理-->
    <bean id="aggregateValueMetadataListener"
          class="com.facishare.paas.task.calculate.listener.AggregateValueMetadataListener"/>
    <bean id="concurrentlyAggregateValueMetadataListener" class="com.facishare.paas.task.calculate.listener.ConcurrentlyListener"
          p:listener-ref="aggregateValueMetadataListener"/>

    <!--增量计算-->
    <bean id="aggregateValueCalculateListener"
          class="com.facishare.paas.task.calculate.listener.AggregateValueCalculateListener"/>
    <bean id="concurrentlyAggregateValueCalculateListener" class="com.facishare.paas.task.calculate.listener.ConcurrentlyListener"
          p:listener-ref="aggregateValueCalculateListener"/>

    <!--全量计算预处理-->
    <bean id="aggregateRuleChangeListener"
          class="com.facishare.paas.task.calculate.listener.AggregateRuleChangeListener"/>
    <bean id="concurrentlyAggregateRuleChangeListener" class="com.facishare.paas.task.calculate.listener.ConcurrentlyListener"
          p:listener-ref="aggregateRuleChangeListener"/>

    <!--全量计算-->
    <bean id="aggregateValueHistoryCalculateListener"
          class="com.facishare.paas.task.calculate.listener.AggregateValueHistoryCalculateListener"/>
    <bean id="concurrentlyAggregateValueHistoryCalculateListener" class="com.facishare.paas.task.calculate.listener.ConcurrentlyListener"
          p:listener-ref="aggregateValueHistoryCalculateListener"/>

</beans>