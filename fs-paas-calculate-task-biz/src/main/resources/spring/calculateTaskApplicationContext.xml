<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:c="http://www.springframework.org/schema/c"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.1.xsd">

    <import resource="classpath:spring/calculateDataListener.xml"/>
    <import resource="classpath:spring/calculateMetadataListener.xml"/>
    <import resource="classpath:spring/calculateJobSchedule.xml"/>
    <import resource="classpath:spring/app-task-async.xml"/>
    <import resource="classpath:spring/aggregateValueListener.xml"/>
    <import resource="classpath:spring/calculateDispatchConfig.xml"/>
    <import resource="classpath:spring/calculateXxlJob.xml"/>

    <bean id="calculateTaskMqMessageSender" class="com.facishare.paas.task.calculate.util.AppDefaultRocketMQProducer"
          init-method="init"
          destroy-method="close"
          c:config="fs-paas-calculate-task-new-rocketmq"/>

</beans>

