<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!--上报错误日志-->
    <bean id="metricsConfiguration" class="com.fxiaoke.metrics.MetricsConfiguration"/>

    <bean id="calculateRedissonClient" class="com.fxiaoke.common.redisson.RedissonFactoryBean">
        <property name="configName" value="fs-paas-metadata-service-redis"/>
    </bean>

    <bean id="calculateDataEventParser" class="com.facishare.paas.task.calculate.model.CalculateDataEventParser"/>

    <bean id="calculateDataEventListener"
          class="com.facishare.paas.task.calculate.listener.CalculateDataEventListener"/>

    <bean id="calculateEventPorter" class="com.fxiaoke.dispatcher.EventPorter">
        <constructor-arg name="configName" value="dispatcher-calculate-task.ini"/>
        <constructor-arg name="eventParser" ref="calculateDataEventParser"/>
        <constructor-arg name="eventListener" ref="calculateDataEventListener"/>
        <constructor-arg name="redissonClient" ref="calculateRedissonClient"/>
        <constructor-arg name="counter" ref="getMetrics"/>
    </bean>

</beans>