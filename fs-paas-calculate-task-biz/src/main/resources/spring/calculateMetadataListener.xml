<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.1.xsd">

    <bean id="fieldMetadataListener" class="com.facishare.paas.task.calculate.listener.FieldMetadataListener"/>

    <!--监听元数据人工操作db变化服务配置-->
    <bean id="calculateFieldMetadataListener" class="com.facishare.paas.task.calculate.listener.ConcurrentlyListener"
          p:listener-ref="fieldMetadataListener"/>

    <!--监听元数据批量导入或接口操作db变化服务配置-->
    <bean id="batchCalculateFieldMetadataListener" class="com.facishare.paas.task.calculate.listener.ConcurrentlyListener"
          p:listener-ref="fieldMetadataListener"/>

    <!--<bean id="metadata-and-calculate-consumer" class="com.fxiaoke.rocketmq.consumer.AutoConfMultiMQPushConsumer">-->
        <!--<property name="configName" value="fs-paas-calculate-task-data-processing-consumer"/>-->
    <!--</bean>-->

    <!--监听字段变更消息-->
    <bean id="fieldDescribeChangeListener" class="com.facishare.paas.task.calculate.listener.FieldChangeListener"/>
    <bean id="fieldChangeListener" class="com.facishare.paas.task.calculate.listener.ConcurrentlyListener"
          p:listener-ref="fieldDescribeChangeListener"/>

    <bean id="field-change-consumer" class="com.fxiaoke.rocketmq.consumer.AutoConfMultiMQPushConsumer">
        <property name="configName" value="fs-paas-calculate-task-field-change-consumer"/>
    </bean>

</beans>

