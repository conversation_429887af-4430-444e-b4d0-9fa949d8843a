<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.1.xsd">

    <bean id="calculateDataChangeListener"
          class="com.facishare.paas.task.calculate.listener.CalculateDataChangeListener"/>
    <!--实时topic消息-->
    <bean id="calculateDataNormalListener" class="com.facishare.paas.task.calculate.listener.OrderlyListener" p:listener-ref="calculateDataChangeListener"/>

    <!--批量topic消息-->
    <bean id="calculateDataBatchListener" class="com.facishare.paas.task.calculate.listener.OrderlyListener" p:listener-ref="calculateDataChangeListener"/>

    <!--并发批量topic消息-->
    <bean id="concurrentCalculateDataBatchListener" class="com.facishare.paas.task.calculate.listener.ConcurrentlyListener" p:listener-ref="calculateDataChangeListener"/>

</beans>

