<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:c="http://www.springframework.org/schema/c"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="calculateMonitorXxlJobExecutor"
          class="com.facishare.paas.task.calculate.monitor.CalculateMonitorXxlJobExecutor"
          c:configName="fs-paas-calculate-task-xxl-job"
          init-method="start" destroy-method="destroy"/>
</beans>