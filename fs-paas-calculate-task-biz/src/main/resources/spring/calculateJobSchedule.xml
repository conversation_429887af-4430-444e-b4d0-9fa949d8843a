<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.1.xsd">

    <!-- 计算任务服务配置 -->
    <bean id="jobListener" class="com.facishare.paas.task.calculate.listener.CalculationJobListener"/>
    <bean id="calculateJobListener" class="com.facishare.paas.task.calculate.listener.ConcurrentlyListener" p:listener-ref="jobListener"/>
    <!-- 计算任务服务配置 -->
    <bean id="taskJobListener" class="com.facishare.paas.task.calculate.listener.CalculateTaskJobListener"/>
    <bean id="calculateTaskJobListener" class="com.facishare.paas.task.calculate.listener.ConcurrentlyListener"
          p:listener-ref="taskJobListener"/>

    <bean id="restServiceProxyFactory" class="com.facishare.rest.core.RestServiceProxyFactory"
          p:configName="fs-paas-appframework-rest" init-method="init"/>

    <!--更新计算任务状态接口-->
    <bean id="updateJobStatus" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.task.calculate.proxy.FormulaJobScheduleProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <!--流程提供的 企信消息服务-->
    <bean id="messageProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.task.calculate.proxy.MessageProxy"
          p:factory-ref="restServiceProxyFactory"/>
</beans>

