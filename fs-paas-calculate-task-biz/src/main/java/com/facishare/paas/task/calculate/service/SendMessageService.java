package com.facishare.paas.task.calculate.service;

import com.facishare.paas.task.calculate.config.CalculateConfig;
import com.facishare.paas.task.calculate.config.CalculateTaskApplication;
import com.facishare.paas.task.calculate.util.AppDefaultRocketMQProducer;
import com.facishare.paas.task.calculate.util.AuditLogUtil;
import com.facishare.paas.task.calculate.util.MessageSupport;
import com.facishare.paas.task.calculate.util.TopicConstant;
import com.fxiaoke.dispatcher.route.CollectionRouter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.message.MessageQueue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * create by zhaoju on 2020/09/27
 */
@Slf4j
@Service
public class SendMessageService {
    @Autowired
    private AppDefaultRocketMQProducer calculateTaskMqMessageSender;

    public void resendMessage(MessageExt message) {
        Message newMessage = buildNewMessages(message);
        log.info("resend message,oldTopic:{},newTopic:{},origMsgId:{},ei:{}", message.getTopic(),
                newMessage.getTopic(), MessageSupport.getOriginalMessageId(message), MessageSupport.getTenantId(message));
        sendMessage(newMessage);
    }

    public String sendMessage(Message message) {
        String tenantId = MessageSupport.getTenantId(message);
        String msgId = sendMessage(message, tenantId);
        AuditLogUtil.sendPaaSCalculateLog(message);
        return msgId;
    }

    private boolean isBatch(Message message) {
        return TopicConstant.isBatch(message.getTopic());
    }

    private String sendMessage(Message msg, String tenantId) {
        SendResult sendResult = calculateTaskMqMessageSender.sendMessage(msg, (mqs -> {
            int index = getIndex(tenantId, mqs.size());
            MessageQueue messageQueue = mqs.get(index);
            log.debug("sendMessage,topic:{},tenantId:{},index:{},brokerName:{},queueId:{}", msg.getTopic(), tenantId, index,
                    messageQueue.getBrokerName(), messageQueue.getQueueId());
            String brokerName = String.format("%s-%s", messageQueue.getBrokerName(), messageQueue.getQueueId());
            MessageSupport.setBrokerName(msg, brokerName);
            return messageQueue;
        }));
        return sendResult != null ? sendResult.getMsgId() : null;
    }

    private int getIndex(String tenantId, int queueSize) {
        try {
            int index = CalculateConfig.getTenantGroupIndex(tenantId);
            if (index < 0) {
                // 配置中没有企业信息，则根据数据库集群 ip 路由，如果查不到数据库ip，则直接使用tenantId路由
                String group = CollectionRouter.getTopicName(tenantId);
                if (StringUtils.isEmpty(group)) {
                    index = NumberUtils.toInt(tenantId);
                } else {
                    String subGroup = StringUtils.replace(group, "buf_fsdb", "");
                    if (subGroup.length() > 3) {
                        if (subGroup.length() == 6 || subGroup.length() == 9) {
                            subGroup = subGroup.substring(3, 6);
                        } else {
                            subGroup = subGroup.substring(0, 3);
                        }
                    }
                    if (NumberUtils.isDigits(subGroup)) {
                        index = NumberUtils.toInt(subGroup);
                    } else {
                        index = Objects.hash(subGroup);
                    }
                }
//                MessageQueueScale messageQueueIndex = MessageQueueScale.fromMessage(msg, isBatch(msg));
//                index = messageQueueIndex.getIndex(group, mqs.size());
            }
            return Math.abs(index % queueSize);
        } catch (Exception e) {
            log.error("get msg queue index failed,tenantId:{}", tenantId, e);
            return 0;
        }
    }

    private Message buildNewMessages(MessageExt message) {
        Message newMessage = new Message(getTopic(message), message.getTags(), message.getKeys(), message.getFlag(),
                message.getBody(), true);
        MessageSupport.setObjectIds(newMessage, MessageSupport.getObjectIds(message));
        MessageSupport.setDescribeApiName(newMessage, MessageSupport.getDescribeApiName(message));
        MessageSupport.setTenantId(newMessage, MessageSupport.getTenantId(message));
        MessageSupport.setOriginalMessageId(newMessage, MessageSupport.getOriginalMessageId(message));
        MessageSupport.setReSendTimes(newMessage, MessageSupport.getReSendTimes(message) + 1);
        return newMessage;
    }

    private String getTopic(MessageExt message) {
        String topic = message.getTopic();
        String baseTopic = TopicConstant.getBaseTopic(topic);
        if (Objects.isNull(baseTopic)) {
            return topic;
        }
        return CalculateTaskApplication.getTopic(baseTopic, MessageSupport.getTenantId(message));
    }
}
