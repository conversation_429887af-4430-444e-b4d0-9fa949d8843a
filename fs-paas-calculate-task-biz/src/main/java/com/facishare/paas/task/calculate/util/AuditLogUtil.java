package com.facishare.paas.task.calculate.util;

import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.task.calculate.config.Namespaces;
import com.facishare.paas.task.calculate.model.CalculateDataChangeMessage;
import com.facishare.paas.task.calculate.model.CalculationJobMessage;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.PaaSCalculateLog;
import com.fxiaoke.log.dto.PaaSCalculateLogDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.helper.ConfigHelper;
import com.github.trace.TraceContext;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * Created by liwei on 2019/6/25
 */
@Slf4j
public class AuditLogUtil {
    private static String appName = ConfigHelper.getProcessInfo().getName();
    private static String serverIp = ConfigHelper.getProcessInfo().getIp();
    private static String profile = ConfigHelper.getProcessInfo().getProfile();

    private static final Joiner JOINER = Joiner.on(",").skipNulls();

    private static String join(Collection collection) {
        if (collection == null || collection.isEmpty()) {
            return "";
        }
        return JOINER.join(collection);
    }

    /**
     * 调用接口发送es日志
     */
    public static void sendPaaSCalculateLog(CalculateDataChangeMessage arg, Collection<String> objectIds,
                                            List<String> calculateField, String fieldType, String objectApiName) {
        sendPaaSCalculateLog(arg, objectIds, calculateField, fieldType, objectApiName, Modules.CALCULATE);
    }

    public static void sendPaaSCalculateLog(CalculateDataChangeMessage arg, Collection<String> objectIds,
                                            List<String> calculateField, String fieldType, String objectApiName,
                                            String module) {
        try {
            ExtraInfo extraInfo = ExtraInfo.fromCalculateDataChangeMessage(arg);
            PaaSCalculateLogDTO dto = PaaSCalculateLogDTO.builder()
                    .tenantId(arg.getTenantId())
                    .objectApiName(objectApiName)
                    .objectIds(join(objectIds))
                    .num(objectIds.size())
                    .cost(extraInfo.getCalculateCost())
                    .totalCost(extraInfo.getTotalCost())
                    .action(arg.getOp())
                    .extra(extraInfo.toJsonString())
                    .traceId(TraceContext.get().getTraceId())
                    // 模块信息，计算这里区分是计算、预处理
                    .module(module)
                    .fieldType(fieldType)
                    .queueType(getQueueType(arg))
                    // 计算所在队列，broker-a-30
                    .queueIndex(getQueueIndex(arg.getQueueIndex()))
                    .calculateFieldApiName(join(calculateField))
                    .originalObjectApiName(arg.getOriginalDescribeApiName())
                    .appName(appName)
                    .serverIp(serverIp)
                    .profile(profile)
                    .build();
            BizLogClient.send("biz-log-paas-calculate", Pojo2Protobuf.toMessage(dto, PaaSCalculateLog.class).toByteArray());
        } catch (Exception e) {
            log.warn("sendPaaSCalculateLog failed, messageId:{} ", arg.getMessageId(), e);
        }
    }

    public static void sendPaaSCalculateLog(LogInfo logInfo) {
        try {
            ExtraInfo extraInfo = logInfo.getExtraInfo();
            PaaSCalculateLogDTO dto = PaaSCalculateLogDTO.builder()
                    .tenantId(logInfo.getTenantId())
                    .objectApiName(logInfo.getObjectApiName())
                    .objectIds(join(logInfo.getDataIds()))
                    .num(logInfo.getDataIds().size())
                    .cost(extraInfo.getCalculateCost())
                    .totalCost(extraInfo.getTotalCost())
                    .action(logInfo.getAction())
                    .extra(extraInfo.toJsonString())
                    .traceId(TraceContext.get().getTraceId())
                    // 模块信息，计算这里区分是计算、预处理
                    .module(Modules.CALCULATE)
                    .fieldType(logInfo.getFieldType())
                    .queueType(logInfo.getQueueType())
                    // 计算所在队列，broker-a-30
                    .queueIndex(getQueueIndex(logInfo.getQueueIndex()))
                    .calculateFieldApiName(join(logInfo.getFieldApiNames()))
                    .originalObjectApiName(logInfo.getOriginalDescribeApiName())
                    .appName(appName)
                    .serverIp(serverIp)
                    .profile(profile)
                    .build();
            BizLogClient.send("biz-log-paas-calculate", Pojo2Protobuf.toMessage(dto, PaaSCalculateLog.class).toByteArray());
        } catch (Exception e) {
            log.warn("sendPaaSCalculateLog failed,messageId:{} ", logInfo.getExtraInfo().getMessageId(), e);
        }
    }

    private static String getQueueType(CalculateDataChangeMessage arg) {
        return arg.queueType();
    }

    public static void sendPaaSCalculateLog(CalculationJobMessage calculateJobMQMessage, Collection<String> objectIds,
                                            List<String> calculateField, String fieldType) {
        try {
            ExtraInfo extraInfo = ExtraInfo.fromCalculationJobMessage(calculateJobMQMessage);
            PaaSCalculateLogDTO dto = PaaSCalculateLogDTO.builder()
                    .tenantId(calculateJobMQMessage.getTenantId())
                    .objectApiName(calculateJobMQMessage.getObjectApiName())
                    .objectIds(join(objectIds))
                    .num(objectIds.size())
                    .cost(extraInfo.getCalculateCost())
                    .totalCost(extraInfo.getTotalCost())
                    .action(QueueTypes.CALCULATION_JOB)
                    .extra(extraInfo.toJsonString())
                    .traceId(TraceContext.get().getTraceId())
                    // 模块信息，计算这里区分是计算、预处理
                    .module(Modules.CALCULATE)
                    .fieldType(fieldType)
                    .queueType(QueueTypes.CALCULATION_JOB)
                    // 计算所在队列，broker-a-30
                    .queueIndex(getQueueIndex(calculateJobMQMessage.getQueueIndex()))
                    .calculateFieldApiName(join(calculateField))
                    .appName(appName)
                    .serverIp(serverIp)
                    .profile(profile)
                    .build();
            BizLogClient.send("biz-log-paas-calculate", Pojo2Protobuf.toMessage(dto, PaaSCalculateLog.class).toByteArray());
        } catch (Exception e) {
            log.warn("sendPaaSCalculateLog failed,messageId:{} ", calculateJobMQMessage.getMessageId(), e);
        }
    }

    public static void sendPaaSCalculateLog(Message message) {
        //聚合值计算暂不上报日志
        if (TopicConstant.isAggTopic(message.getTopic())) {
            return;
        }
        try {
            PaaSCalculateLogDTO dto = PaaSCalculateLogDTO.builder()
                    .tenantId(MessageSupport.getTenantId(message))
                    .objectApiName(MessageSupport.getDescribeApiName(message))
                    .objectIds(join(MessageSupport.getObjectIds(message)))
                    .num(MessageSupport.getObjectIds(message).size())
                    .cost(getCost(message))
                    .action(getAction(message))
                    .traceId(TraceContext.get().getTraceId())
                    .module(getModule(message))
                    .fieldType(getFieldType(message))
                    .queueType(getQueueType(message))
                    .queueIndex(getQueueIndex(MessageSupport.getBrokerName(message)))
                    .appName(appName)
                    .serverIp(serverIp)
                    .profile(profile)
                    .build();
            BizLogClient.send("biz-log-paas-calculate", Pojo2Protobuf.toMessage(dto, PaaSCalculateLog.class).toByteArray());
        } catch (Exception e) {
            log.warn("sendPaaSCalculateLog failed,topic:{} ", message.getTopic(), e);
        }
    }

    private static String getQueueIndex(String queueIndex) {
        if (!Strings.isNullOrEmpty(queueIndex) && queueIndex.startsWith("buf_")) {
            return queueIndex;
        }
        return String.format("%s-%s", Namespaces.currentNamespace(), queueIndex);
    }

    private static String getQueueType(Message message) {
        if (TopicConstant.isAggHis(message.getTopic())) {
            return QueueTypes.AGG_HIS;
        }
        if (TopicConstant.isCalculationJob(message.getTopic())) {
            return QueueTypes.CALCULATION_JOB;
        }
        if (TopicConstant.isBatch(message.getTopic())) {
            return QueueTypes.BATCH;
        }
        if (TopicConstant.isOpTool(message.getTopic())) {
            return QueueTypes.OPTOOL;
        }
        return QueueTypes.MANUAL;
    }

    private static String getAction(Message message) {
        return MessageSupport.getAction(message);
    }

    private static long getCost(Message message) {
        Long originalBornTimestamp = MessageSupport.getOriginalBornTimestamp(message);
        if (originalBornTimestamp == null) {
            return 0;
        }
        return System.currentTimeMillis() - originalBornTimestamp;
    }

    private static String getModule(Message message) {
        int reSendTimes = MessageSupport.getReSendTimes(message);
        if (reSendTimes > 0) {
            return Modules.RESEND;
        }
        return Modules.PRETREATMENT;
    }

    private static String getFieldType(Message message) {
        return MessageSupport.getType(message);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class LogInfo {
        private String tenantId;
        private String objectApiName;
        private List<String> dataIds;
        private List<String> fieldApiNames;
        private String fieldType;
        private String action;
        private String queueType;
        private String queueIndex;
        private String originalDescribeApiName;
        private ExtraInfo extraInfo;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ExtraInfo {
        private String messageId;
        private String originalMessageId;
        private List<String> originalObjectIds;
        private List<String> jobIds;

        private Long originalBornTimestamp;
        private Long bornTimestamp;
        private Long consumeTimestamp;

        /**
         * 总耗时
         */
        private long totalCost;
        /**
         * 计算耗时
         */
        private long calculateCost;
        /**
         * 预处理耗时
         */
        private long pretreatmentCost;

        public static ExtraInfo fromCalculateDataChangeMessage(CalculateDataChangeMessage message) {
            Long originalBornTimestamp = message.getOriginalBornTimestamp();
            long consumeTimestamp = message.getConsumeTimestamp();
            Long bornTimestamp = message.getBornTimestamp();
            long now = System.currentTimeMillis();
            ExtraInfo extraInfo = new ExtraInfo();
            extraInfo.setMessageId(message.getMessageId());
            extraInfo.setOriginalMessageId(message.getOriginalMessageId());
            extraInfo.setOriginalObjectIds(message.getOriginalObjectIds());

            extraInfo.setOriginalBornTimestamp(originalBornTimestamp);
            extraInfo.setBornTimestamp(bornTimestamp);
            extraInfo.setConsumeTimestamp(consumeTimestamp);

            extraInfo.setTotalCost(minus(now, originalBornTimestamp));
            extraInfo.setCalculateCost(minus(now, consumeTimestamp));
            extraInfo.setPretreatmentCost(minus(bornTimestamp, originalBornTimestamp));
            return extraInfo;
        }

        public static ExtraInfo fromCalculationJobMessage(CalculationJobMessage message) {
            Long bornTimestamp = message.getBornTimestamp();
            Long originalBornTimestamp = message.getOriginalBornTimestamp();
            long consumeTimestamp = message.getConsumeTimestamp();
            long now = System.currentTimeMillis();
            ExtraInfo extraInfo = new ExtraInfo();
            extraInfo.setMessageId(message.getMessageId());
            extraInfo.setConsumeTimestamp(consumeTimestamp);
            extraInfo.setBornTimestamp(bornTimestamp);

            extraInfo.setTotalCost(minus(now, originalBornTimestamp));
            extraInfo.setCalculateCost(minus(now, consumeTimestamp));
            extraInfo.setPretreatmentCost(minus(bornTimestamp, originalBornTimestamp));
            extraInfo.setJobIds(Lists.newArrayList(message.getJobId()));
            return extraInfo;
        }

        public String toJsonString() {
            return JacksonUtils.toJson(this);
        }

    }

    public static long minus(Long l1, Long l2) {
        if (Objects.isNull(l1) || Objects.isNull(l2)) {
            return 0L;
        }
        return l1 - l2;
    }
}
