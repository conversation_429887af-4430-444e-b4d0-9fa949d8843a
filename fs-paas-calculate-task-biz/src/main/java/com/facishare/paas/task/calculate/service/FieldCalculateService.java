package com.facishare.paas.task.calculate.service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.MetaDataService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.QuoteValueService;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.relation.FieldNode.NodeType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.Quote;
import com.facishare.paas.task.calculate.config.CalculateConfig;
import com.facishare.paas.task.calculate.model.CalculateDataGroup;
import com.facishare.paas.task.calculate.model.UpdateCalculationJob;
import com.facishare.paas.task.calculate.proxy.FormulaJobScheduleService;
import com.facishare.paas.task.calculate.util.AuditLogUtil;
import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class FieldCalculateService {

    @Autowired
    private DescribeLogicService describeLogicService;

    @Autowired
    private MetaDataService metaDataService;

    @Autowired
    private ExpressionCalculateLogicService expressionCalculateLogicService;

    @Autowired
    private QuoteValueService quoteValueService;

    @Autowired
    private DataPackageService dataPackageService;

    @Autowired
    private FormulaJobScheduleService formulaJobScheduleService;

    public void calculateByGroup(CalculateDataGroup group) {
        NodeType nodeType = NodeType.valueOf(group.getFieldType());
        switch (nodeType) {
            case QUOTE:
                calculateQuote(group);
                break;
            case COUNT:
                calculateCount(group);
                break;
            default:
                calculateFormula(group);
                break;
        }
        //更新计算任务的执行进度
        updateCalculationProgress(group);
        //上报计算日志
        AuditLogUtil.sendPaaSCalculateLog(group.toLogInfo());
    }

    private void updateCalculationProgress(CalculateDataGroup group) {
        if (CollectionUtils.empty(group.getJobIds())) {
            return;
        }
        group.getJobIds().forEach(jobId -> {
            UpdateCalculationJob.Arg arg = UpdateCalculationJob.Arg.builder()
                    .jobId(jobId)
                    .executeResult("")
                    .incrCompleteDataNum(CollectionUtils.size(group.getDataIds()))
                    .build();
            formulaJobScheduleService.updateCalculateJobProgress(group.getTenantId(), arg);
        });
    }

    private void calculateCount(CalculateDataGroup group) {
        List<String> dataIds = Lists.newArrayList(group.getDataIds());
        //更新统计字段数据
        IObjectDescribe masterDescribe = describeLogicService.findObject(group.getTenantId(), group.getObjectApiName());
        List<Count> fieldList = ObjectDescribeExt.of(masterDescribe).getFieldByApiNames(Lists.newArrayList(group.getFieldApiNames())).stream()
                .filter(IFieldDescribe::isActive)
                .filter(x -> FieldDescribeExt.of(x).isCountField())
                .filter(x -> !CalculateConfig.isFieldInBlacklist(group.getTenantId(), group.getObjectApiName(),
                        x.getApiName(), group.getQueueType(), group.getAction(), group.getOriginalObjectApiName()))
                .map(x -> (Count) x)
                .collect(Collectors.toList());
        group.setFieldApiNames(fieldList.stream().map(IFieldDescribe::getApiName).collect(Collectors.toSet()));
        if (CollectionUtils.empty(fieldList)) {
            return;
        }
        Map<String, List<Count>> countGroup = fieldList.stream().collect(Collectors.groupingBy(Count::getSubObjectDescribeApiName));
        Map<String, IObjectDescribe> detailDescribeMap = describeLogicService.findObjects(group.getTenantId(), Lists.newArrayList(countGroup.keySet()));

        IActionContext actionContext = group.buildContext();
        countGroup.forEach((detailApiName, countFields) -> {
            IObjectDescribe detailDescribe = detailDescribeMap.get(detailApiName);
            if (detailDescribe == null) {
                group.removeFields(countFields);
                log.warn("detail describe not exists:{},{},{}.{}", group.getTenantId(), detailApiName,
                        group.getObjectApiName(), countFields.get(0).getApiName());
                return;
            }
            List<IObjectData> masterDataList = dataPackageService.findObjectDataByIdsIgnoreAll(group.getTenantId(), dataIds, masterDescribe.getApiName());
            metaDataService.calculateAndUpdateMasterCountFieldsWithData(actionContext, masterDescribe, masterDataList, detailDescribe, countFields);
        });
    }

    private void calculateFormula(CalculateDataGroup group) {
        List<String> dataIds = Lists.newArrayList(group.getDataIds());
        IObjectDescribe describe = describeLogicService.findObject(group.getTenantId(), group.getObjectApiName());
        List<IFieldDescribe> fieldList = ObjectDescribeExt.of(describe).getFieldByApiNames(Lists.newArrayList(group.getFieldApiNames())).stream()
                .filter(x -> FieldDescribeExt.of(x).isCalculateFieldsNeedStoreInDB())
                .filter(x -> !FieldDescribeExt.of(x).isCountField())
                .filter(x -> !FieldDescribeExt.of(x).isQuoteField())
                .filter(x -> !CalculateConfig.isFieldInBlacklist(group.getTenantId(), group.getObjectApiName(),
                        x.getApiName(), group.getQueueType(), group.getAction(), group.getOriginalObjectApiName()))
                .collect(Collectors.toList());
        group.retainFields(fieldList);
        if (CollectionUtils.empty(fieldList)) {
            log.warn("skip no calculate field,tenantId:{},field:{}.{}", group.getTenantId(), group.getObjectApiName(), group.getFieldApiNames());
            return;
        }
        List<String> fieldNameList = fieldList.stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
        List<IObjectData> objectDataList = dataPackageService.findObjectDataByIdsIgnoreAll(group.getTenantId(), dataIds, group.getObjectApiName());

        IActionContext actionContext = group.buildContext();
        expressionCalculateLogicService.bulkCalculateWithDependentData(describe, objectDataList, fieldList, null, true);
        metaDataService.batchUpdateWithFieldsForCalculateToPG(actionContext, objectDataList, fieldNameList);
    }

    private void calculateQuote(CalculateDataGroup group) {
        List<String> dataIds = Lists.newArrayList(group.getDataIds());
        IObjectDescribe describe = describeLogicService.findObject(group.getTenantId(), group.getObjectApiName());
        List<Quote> fieldList = ObjectDescribeExt.of(describe).getFieldByApiNames(Lists.newArrayList(group.getFieldApiNames())).stream()
                .filter(x -> FieldDescribeExt.of(x).isCalculateFieldsNeedStoreInDB())
                .filter(x -> FieldDescribeExt.of(x).isQuoteField())
                .filter(x -> !CalculateConfig.isFieldInBlacklist(group.getTenantId(), group.getObjectApiName(),
                        x.getApiName(), group.getQueueType(), group.getAction(), group.getOriginalObjectApiName()))
                .map(x -> (Quote) x)
                .collect(Collectors.toList());
        group.retainFields(fieldList);
        if (CollectionUtils.empty(fieldList)) {
            log.warn("skip no calculate field,tenantId:{},field:{}.{}", group.getTenantId(), group.getObjectApiName(), group.getFieldApiNames());
            return;
        }
        List<String> fieldNameList = fieldList.stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
        List<IObjectData> objectDataList = dataPackageService.findObjectDataByIdsIgnoreAll(group.getTenantId(), dataIds, group.getObjectApiName());

        IActionContext actionContext = group.buildContext();
        quoteValueService.fillQuoteFieldValue(User.systemUser(group.getTenantId()), objectDataList, describe,
                null, true, fieldList, null, true);
        metaDataService.batchUpdateWithFieldsForCalculateToPG(actionContext, objectDataList, fieldNameList);
    }

}
