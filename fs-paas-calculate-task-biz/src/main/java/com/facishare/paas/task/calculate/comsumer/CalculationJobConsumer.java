package com.facishare.paas.task.calculate.comsumer;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.MetaDataService;
import com.facishare.paas.appframework.metadata.QuoteValueService;
import com.facishare.paas.appframework.metadata.exception.FieldNotExistException;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.relation.FieldNode;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.Quote;
import com.facishare.paas.task.calculate.config.CalculateConfig;
import com.facishare.paas.task.calculate.config.CalculateTaskApplication;
import com.facishare.paas.task.calculate.exception.FieldValidateException;
import com.facishare.paas.task.calculate.model.CalculateDataEvent;
import com.facishare.paas.task.calculate.model.CalculationJobMessage;
import com.facishare.paas.task.calculate.proxy.FormulaJobScheduleService;
import com.facishare.paas.task.calculate.service.CalculateEventService;
import com.facishare.paas.task.calculate.service.DescribePackageService;
import com.facishare.paas.task.calculate.service.SendMessageService;
import com.facishare.paas.task.calculate.util.AuditLogUtil;
import static com.facishare.paas.task.calculate.util.MessageConvert.COUNT_TYPE;
import static com.facishare.paas.task.calculate.util.MessageConvert.FORMULA_TYPE;
import com.facishare.paas.task.calculate.util.MessageSupport;
import com.facishare.paas.task.calculate.util.QueueTypes;
import com.facishare.paas.task.calculate.util.TopicConstant;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

/**
 * Created by liwei on 2018/10/9
 */
@Slf4j
@Service
public class CalculationJobConsumer implements Consumer<CalculationJobMessage> {
    @Autowired
    private DescribePackageService describePackageService;
    @Autowired
    private MetaDataService metaDataService;
    @Autowired
    private FormulaJobScheduleService formulaJobScheduleService;
    @Autowired
    private ExpressionCalculateLogicService expressionCalculateLogicService;
    @Autowired
    private QuoteValueService quoteValueService;

    @Autowired
    private SendMessageService sendMessageService;
    @Autowired
    private CalculateEventService calculateEventService;

    public void forwardMessage(CalculationJobMessage calculateJobMQMessage) {
        byte[] bytes = JSON.toJSONString(calculateJobMQMessage, SerializerFeature.WriteMapNullValue).getBytes();
        String tenantId = calculateJobMQMessage.getTenantId();
        String topic = getTopic(tenantId);
        Message msg = new Message(topic, "", bytes);
        MessageSupport.setTenantId(msg, tenantId);
        MessageSupport.setDescribeApiName(msg, calculateJobMQMessage.getObjectApiName());
        MessageSupport.setOriginalBornTimestamp(msg, calculateJobMQMessage.getBornTimestamp());
        MessageSupport.setObjectIds(msg, calculateJobMQMessage.getDataIdList());
        sendMessageService.sendMessage(msg);
    }

    private String getTopic(String tenantId) {
        return CalculateTaskApplication.getTopic(TopicConstant.BASE_CALCULATE_TASK_JOB_TOPIC, tenantId);
    }

    @Override
    public void execute(CalculationJobMessage calculateJobMQMessage) {
        String tenantId = calculateJobMQMessage.getTenantId();
        if (CollectionUtils.empty(calculateJobMQMessage.getDataIdList())) {
            if (Boolean.TRUE.equals(calculateJobMQMessage.getEndMessage())) {
                log.info("execute end jobId:{},next step whether completeBack:{}", calculateJobMQMessage.getJobId(),
                        calculateJobMQMessage.getEndMessage());
                formulaJobScheduleService.completedCalculateJob(calculateJobMQMessage);
            }
            return;
        }

        String jobParam = calculateJobMQMessage.getJobParam();
        List<String> changeFieldNames = JSONObject.parseArray(jobParam, String.class);
        if (CollectionUtils.empty(changeFieldNames)) {
            log.warn("changeFieldNames is null,do not calculate!");
            return;
        }

        String objectApiName = calculateJobMQMessage.getObjectApiName();
        User user = User.systemUser(tenantId);

        IObjectDescribe describe = describePackageService.fetchObject(tenantId, objectApiName);

        //计算字段和统计字段分类分开计算
        List<Count> countFieldList = Lists.newArrayList();
        List<String> countFieldNames = Lists.newArrayList();
        List<IFieldDescribe> formulaFieldList = Lists.newArrayList();
        List<String> formulaFieldNames = Lists.newArrayList();
        List<IFieldDescribe> quoteFieldList = Lists.newArrayList();
        List<String> quoteFieldNames = Lists.newArrayList();

        changeFieldNames.forEach(x -> {
            if (CalculateConfig.isFieldInBlacklist(tenantId, objectApiName, x, QueueTypes.CALCULATION_JOB, QueueTypes.CALCULATION_JOB, objectApiName)) {
                log.warn("skip blacklist field,tenantId:{},field:{}.{}", tenantId, objectApiName, x);
                return;
            }
            int maxReconsumeTimes = CalculateConfig.getFieldValidateRetryTimes();
            IFieldDescribe fieldDescribe = describe.getFieldDescribe(x);
            //缓存里的describe可能不是最新的，清掉缓存，抛异常让消息重试，如果最后一次重试字段还是不存在，则忽略该字段。
            if (fieldDescribe == null) {
                if(calculateJobMQMessage.shouldReconsume(maxReconsumeTimes)) {
                    throw new FieldNotExistException(tenantId + ":" + objectApiName + "." + x);
                }
                log.warn("skip field not exist,tenantId:{},field:{}.{}", tenantId, objectApiName, x);
                return;
            }
            FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
            if (!fieldDescribeExt.isCountField() && !fieldDescribeExt.isQuoteField()
                    && !fieldDescribeExt.isFormula() && !fieldDescribeExt.hasCalculateValue()) {
                log.warn("skip no calculate field,tenantId:{},field:{}.{}", tenantId, objectApiName, x);
                return;
            }
            if (!fieldDescribeExt.isActive()) {
                if(calculateJobMQMessage.shouldReconsume(maxReconsumeTimes)) {
                    throw new FieldValidateException("is_active is false:" + tenantId + ":" + objectApiName + "." + x);
                }
                log.warn("skip field not active,tenantId:{},field:{}.{}", tenantId, objectApiName, x);
                return;
            }
            if ((fieldDescribeExt.isFormula() || fieldDescribeExt.isQuoteField()) && !fieldDescribeExt.isIndex()) {
                if(calculateJobMQMessage.shouldReconsume(maxReconsumeTimes)) {
                    throw new FieldValidateException("is_index is false:" + tenantId + ":" + objectApiName + "." + x);
                }
                log.warn("skip not index field,tenantId:{},field:{}.{}", tenantId, objectApiName, x);
                return;
            }

//            if (!fieldDescribeExt.isCalculateFieldsNeedStoreInDB()) {
//                log.warn("skip not index field,tenantId:{},field:{}.{}", tenantId, objectApiName, x);
//                return;
//            }
            if (fieldDescribeExt.isCountField()) {
                countFieldList.add((Count) fieldDescribe);
                countFieldNames.add(fieldDescribe.getApiName());
            } else if (fieldDescribeExt.isQuoteField()) {
                quoteFieldList.add(fieldDescribe);
                quoteFieldNames.add(fieldDescribe.getApiName());
            } else {
                formulaFieldList.add(fieldDescribe);
                formulaFieldNames.add(fieldDescribe.getApiName());
            }
        });

        //灰度企业接入聚合框架
        if (CalculateConfig.isCountAndCalcJobGrayDispatcher(tenantId)) {
            Map<String, List<String>> fieldsMap = Maps.newHashMap();
            if (CollectionUtils.notEmpty(countFieldNames)) {
                fieldsMap.put(FieldNode.NodeType.COUNT.name(), countFieldNames);
            }
            if (CollectionUtils.notEmpty(quoteFieldNames)) {
                fieldsMap.put(FieldNode.NodeType.QUOTE.name(), quoteFieldNames);
            }
            if (CollectionUtils.notEmpty(formulaFieldNames)) {
                fieldsMap.put(FieldNode.NodeType.FORMULA.name(), formulaFieldNames);
            }
            List<CalculateDataEvent> events = calculateJobMQMessage.toEvents(fieldsMap);
            calculateEventService.upsertEvents(events);
        } else {
            //1.计算统计字段
            if (CollectionUtils.notEmpty(countFieldList)) {
                List<String> relatedApiNames = countFieldList.stream().map(Count::getSubObjectDescribeApiName).distinct().collect(Collectors.toList());
                calculateAndUpdateMasterCountFields(relatedApiNames, calculateJobMQMessage, user, describe, countFieldList, countFieldNames);
            }

            //2.计算计算字段
            if (CollectionUtils.notEmpty(formulaFieldList)) {
                calculateAndUpdateFormulaFields(user, calculateJobMQMessage, describe, formulaFieldNames, formulaFieldList);
            }

            //3.计算引用字段
            if (CollectionUtils.notEmpty(quoteFieldList)) {
                calculateAndUpdateQuoteFields(user, calculateJobMQMessage, describe, quoteFieldNames, quoteFieldList);
            }

            //更新计算任务的执行进度
            formulaJobScheduleService.updateCalculateJobProgress(calculateJobMQMessage);
        }

        //4.处理完最后一批任务更新结果
        if (Boolean.TRUE.equals(calculateJobMQMessage.getEndMessage())) {
            log.info("calculationJob complete,jobId:{},messageId:{}", calculateJobMQMessage.getJobId(),
                    calculateJobMQMessage.getMessageId());
            formulaJobScheduleService.completedCalculateJob(calculateJobMQMessage);
        }
    }

    public void calculateAndUpdateMasterCountFields(List<String> relatedApiNames,
                                                    CalculationJobMessage calculateJobMQMessage,
                                                    User user, IObjectDescribe masterDescribe,
                                                    List<Count> countFieldList, List<String> countFieldNames) {

        String tenantId = calculateJobMQMessage.getTenantId();
        log.debug("calculateAndUpdateMasterCountFields start tenantId:{},relatedApiNames:{},countFieldNames:{}",
                tenantId, relatedApiNames, countFieldNames);

        // 1. 找到这个对象下需要统计的从对象和关联对象。
        Map<String, IObjectDescribe> relatedDescribes = Maps.newHashMap();
        try {
            if (relatedApiNames.size() == 1) {
                IObjectDescribe relatedDescribe = describePackageService.fetchObject(tenantId, relatedApiNames.get(0));
                relatedDescribes.put(relatedDescribe.getApiName(), relatedDescribe);
            } else {
                relatedDescribes = describePackageService.fetchObjects(tenantId, relatedApiNames);
            }
        } catch (ObjectDefNotFoundError e) {
            log.warn("related object not exist,tenantId:{},relatedApiNames:{}", tenantId, relatedApiNames);
        }
        if (CollectionUtils.empty(relatedDescribes)) {
            return;
        }

        List<IObjectDescribe> relatedDescribeList = Lists.newArrayList(relatedDescribes.values());
        //2.根据dataIdList查询数据
        List<String> dataIdList = calculateJobMQMessage.getDataIdList();
        //3.计算
        computeCountFieldForMasterObjectData(user, masterDescribe, relatedDescribeList, countFieldList, dataIdList, calculateJobMQMessage);
    }

    public void calculateAndUpdateFormulaFields(User user, CalculationJobMessage calculateJobMQMessage, IObjectDescribe describe,
                                                List<String> formulaFieldNames, List<IFieldDescribe> formulaFieldList) {
        //1.查询对象的describe
        String tenantId = calculateJobMQMessage.getTenantId();
        String objectApiName = calculateJobMQMessage.getObjectApiName();
        log.debug("calculateAndUpdateFormulaFields start tenantId:{},objectApiName:{},formulaFieldNames:{}",
                tenantId, objectApiName, formulaFieldNames);

        //2.根据dataIdList查询数据
        List<String> dataIdList = calculateJobMQMessage.getDataIdList();
        List<IObjectData> objectDataList = metaDataService.findObjectDataByIdsIgnoreAll(tenantId, dataIdList, calculateJobMQMessage.getObjectApiName());

        //3.计算
        IActionContext actionContext = getContext(user);
        expressionCalculateLogicService.bulkCalculateWithDependentData(describe, objectDataList, formulaFieldList, null, true);
        metaDataService.batchUpdateWithFieldsForCalculateToPG(actionContext, objectDataList, formulaFieldNames);

        AuditLogUtil.sendPaaSCalculateLog(calculateJobMQMessage, dataIdList, formulaFieldNames, FORMULA_TYPE);
    }

    public void calculateAndUpdateQuoteFields(User user, CalculationJobMessage calculateJobMQMessage, IObjectDescribe describe,
                                              List<String> quoteFieldNames, List<IFieldDescribe> quoteFieldList) {
        List<Quote> quoteFields = quoteFieldList.stream().map(x -> (Quote) x).collect(Collectors.toList());
        //1.查询对象的describe
        String tenantId = calculateJobMQMessage.getTenantId();
        String objectApiName = calculateJobMQMessage.getObjectApiName();
        log.debug("calculateAndUpdateQuoteFields start tenantId:{},objectApiName:{},quoteFieldNames:{}",
                tenantId, objectApiName, quoteFieldNames);

        //2.根据dataIdList查询数据
        List<String> dataIdList = calculateJobMQMessage.getDataIdList();
        List<IObjectData> objectDataList = metaDataService.findObjectDataByIdsIgnoreAll(tenantId, dataIdList, calculateJobMQMessage.getObjectApiName());

        //3.计算更新
        IActionContext actionContext = getContext(user);
        quoteValueService.fillQuoteFieldValue(user, objectDataList, describe, null, true, quoteFields, null, true);
        metaDataService.batchUpdateWithFieldsForCalculateToPG(actionContext, objectDataList, quoteFieldNames);

        AuditLogUtil.sendPaaSCalculateLog(calculateJobMQMessage, dataIdList, quoteFieldNames, FORMULA_TYPE);
    }

    private void computeCountFieldForMasterObjectData(User user, IObjectDescribe masterDescribe, List<IObjectDescribe> relatedDescribes, List<Count> countFieldList, List<String> masterObjectDataIds, CalculationJobMessage calculateJobMQMessage) {
        IActionContext actionContext = getContext(user);
        relatedDescribes.forEach(relatedDescribe -> {
            List<Count> countFields = countFieldList.stream().filter((x) ->
                    x.getSubObjectDescribeApiName().equals(relatedDescribe.getApiName())
            ).collect(Collectors.toList());

            log.debug("computeCountFieldForMasterObjectData masterObjectDataIds:{},masterApiName:{},relatedDescribe:{},countFields:{}", masterObjectDataIds, masterDescribe.getApiName(),
                    relatedDescribe.getApiName(), countFields);
            metaDataService.calculateAndUpdateMasterCountFields(actionContext, masterObjectDataIds, masterDescribe, relatedDescribe, countFields);

            List<String> fieldNames = countFields.stream().map(Count::getApiName).collect(Collectors.toList());
            AuditLogUtil.sendPaaSCalculateLog(calculateJobMQMessage, masterObjectDataIds, fieldNames, COUNT_TYPE);
        });

    }

    private IActionContext getContext(User user) {
        return ActionContextExt.of(user, null).setBatch(true).getContext();
    }

}
