package com.facishare.paas.task.calculate.service;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.task.calculate.config.Namespaces;
import com.facishare.paas.task.calculate.model.CalculateDataEvent;
import com.fxiaoke.dispatcher.EventPorter;
import com.fxiaoke.dispatcher.common.BaseEvent;
import com.fxiaoke.dispatcher.common.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CalculateEventService {

    @Autowired
    @Lazy
    private EventPorter calculateEventPorter;

    public void resendEvents(List<CalculateDataEvent> events) {
        if (CollectionUtils.empty(events)) {
            return;
        }
        List<CalculateDataEvent> newEvents = events.stream().map(CalculateDataEvent::copy).collect(Collectors.toList());
        newEvents.forEach(x -> {
            x.setStatus(Constants.EventStatus.STATUS_READY);
            x.setTopic(Namespaces.getEventTopicByTenantIdAndObject(x.getTenantId(), x.getObjectApiName(), x.getDataId()));
        });
        upsertEvents(newEvents);
    }

    public void upsertEvents(List<CalculateDataEvent> events) {
        if (CollectionUtils.empty(events)) {
            return;
        }
        try {
            List<BaseEvent> baseEvents = events.stream()
                    .peek(x -> x.setTopic(x.topicWithNamespace()))
                    .collect(Collectors.toList());
            calculateEventPorter.upsert(baseEvents);
            log.info("upsert events success,eventNum:{}", baseEvents.size());
        } catch (Exception e) {
            log.error("upsertEvents error,events:{}", JSON.toJSONString(events), e);
            throw e;
        }
    }

}
