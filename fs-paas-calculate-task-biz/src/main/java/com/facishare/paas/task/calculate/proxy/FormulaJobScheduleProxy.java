package com.facishare.paas.task.calculate.proxy;

import com.facishare.paas.task.calculate.model.CompleteCalculationJob;
import com.facishare.paas.task.calculate.model.UpdateCalculationJob;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderParam;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

@RestResource(
        value = "PAAS-SendCalculateJob",
        desc = "计算任务服务", //ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.codec.AppDefaultCodeC"
)
public interface FormulaJobScheduleProxy {

    @POST(value = "/complete", desc = "更新计算任务状态") //ignoreI18n
    UpdateCalculationJob.Result updateCalculateJobStatus(@HeaderParam("x-fs-ei") String tenantId, @Body UpdateCalculationJob.Arg arg);

    @POST(value = "/update", desc = "更新计算任务执行进度") //ignoreI18n
    CompleteCalculationJob.Result updateCalculateJobProgress(@HeaderParam("x-fs-ei") String tenantId, @Body UpdateCalculationJob.Arg arg);

    @POST(value = "/cease", desc = "终止计算任务") //ignoreI18n
    UpdateCalculationJob.Result ceaseCalculateJob(@HeaderParam("x-fs-ei") String tenantId, @Body UpdateCalculationJob.Arg arg);
}
