package com.facishare.paas.task.calculate.listener;

import com.facishare.paas.task.calculate.model.CalculationJobMessage;
import com.facishare.paas.task.calculate.comsumer.CalculationJobConsumer;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 监听全量计算的消息
 * Created by <PERSON><PERSON><PERSON> on 2018/10/8
 */
public class CalculationJobListener extends AbstractCalculationJobListener {

    @Autowired
    private CalculationJobConsumer calculationJobConsumer;

    @Override
    protected void doConsume(CalculationJobMessage arg) {
        // 转发消息
        calculationJobConsumer.forwardMessage(arg);
    }

    @Override
    protected void customInitMessage(CalculationJobMessage calculateJobMQMessage, MessageExt message) {
        calculateJobMQMessage.setOriginalBornTimestamp(message.getBornTimestamp());
    }
}
