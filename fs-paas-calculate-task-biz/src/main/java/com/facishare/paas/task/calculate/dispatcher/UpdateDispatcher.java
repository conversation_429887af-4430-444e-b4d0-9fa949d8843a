package com.facishare.paas.task.calculate.dispatcher;

import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.task.calculate.model.CustomDataChangeMessage;
import com.facishare.paas.task.calculate.util.QueueTypes;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * Created by liwei on 2018/11/13
 */
@Slf4j
@SuperBuilder
public class UpdateDispatcher extends FieldMetadataDispatcher {

    @Override
    protected boolean skip(CustomDataChangeMessage.DataContent content) {
        return content.isLifeStatusUpdatedByInvalid();
    }

    @Override
    protected List<String> getChangeFields(IObjectDescribe objectDescribe, CustomDataChangeMessage.DataContent content) {
        List<String> changedFields = content.getChangeFields(objectDescribe);
        //灰度企业更新计算字段需要更新最后修改时间，为了防止死循环，计算服务更新数据导致最后修改时间变更时不再触发相关字段的计算
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FILTER_LAST_MODIFIED_TIME_FIELD_FOR_FORMULA, objectDescribe.getTenantId())
                && message.opByCalculate()) {
            changedFields.remove(DBRecord.LAST_MODIFIED_TIME);
        }
        return changedFields;
    }

    @Override
    protected boolean includeInvalidData() {
        return true;
    }

    @Override
    protected boolean skipFilterCount() {
        return true;
    }
}
