package com.facishare.paas.task.calculate.proxy;

import com.facishare.paas.task.async.task.AsyncTaskApi;
import com.facishare.paas.task.calculate.model.CalculationJobMessage;
import com.facishare.paas.task.calculate.model.UpdateCalculationJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * Created by liwei on 2018/10/30
 */
@Slf4j
@Service("formulaJobScheduleService")
public class FormulaJobScheduleServiceImpl implements FormulaJobScheduleService {
    @Autowired
    private FormulaJobScheduleProxy formulaJobScheduleProxy;
    @Autowired
    private AsyncTaskApi asyncTaskApi;

    @Override
    public void completedCalculateJob(CalculationJobMessage calculateJobMQMessage) {
        UpdateCalculationJob.Arg arg = UpdateCalculationJob.Arg.builder()
                .jobId(calculateJobMQMessage.getJobId())
                .executeResult("")
                .manual(calculateJobMQMessage.isManual())
                .createTime(calculateJobMQMessage.getCreateTime())
                .lastModifiedTime(calculateJobMQMessage.getLastModifiedTime())
                .build();

        long startTime = System.currentTimeMillis();
        log.info("completedCalculateJob url:/job_schedule/complete,jobId:{},arg:{}", calculateJobMQMessage.getJobId(), arg);

        String tenantId = calculateJobMQMessage.getTenantId();
        UpdateCalculationJob.Result result = formulaJobScheduleProxy.updateCalculateJobStatus(tenantId, arg);
        log.info("completedCalculateJob response:{},jobId:{},cost:{}ms", result, calculateJobMQMessage.getJobId()
                , System.currentTimeMillis() - startTime);
    }

    @Override
    public void updateCalculateJobProgress(CalculationJobMessage calculateJobMQMessage) {
        UpdateCalculationJob.Arg arg = UpdateCalculationJob.Arg.builder()
                .jobId(calculateJobMQMessage.getJobId())
                .executeResult("")
                .manual(calculateJobMQMessage.isManual())
                .createTime(calculateJobMQMessage.getCreateTime())
                .lastModifiedTime(calculateJobMQMessage.getLastModifiedTime())
                .incrCompleteDataNum(calculateJobMQMessage.dataNum())
                .build();
        updateCalculateJobProgress(calculateJobMQMessage.getTenantId(), arg);
    }

    @Override
    public void updateCalculateJobProgress(String tenantId, UpdateCalculationJob.Arg arg) {
        try {
            log.info("updateCalculateJobProgress,jobId:{},dataNum:{}", arg.getJobId(), arg.getIncrCompleteDataNum());
            asyncTaskApi.updateScheduleByTaskId(tenantId, arg.getJobId(), Long.valueOf(arg.getIncrCompleteDataNum()), arg.getLastModifiedTime());
        } catch (Exception e) {
            log.error("updateCalculateJobProgress failed,tenantId:{},arg:{}", tenantId, arg);
        }
    }

    @Override
    public void ceaseCalculateJob(CalculationJobMessage calculateJobMQMessage) {
        String lastMessageId = "";
        List<String> dataIdList = calculateJobMQMessage.getDataIdList();
        if (CollectionUtils.isNotEmpty(dataIdList)) {
            lastMessageId = dataIdList.get(dataIdList.size() - 1);
        }
        UpdateCalculationJob.Arg arg = UpdateCalculationJob.Arg.builder()
                .jobId(calculateJobMQMessage.getJobId())
                .manual(calculateJobMQMessage.isManual())
                .lastMessageId(lastMessageId)
                .build();

        long startTime = System.currentTimeMillis();
        log.info("ceaseCalculateJob url:/job_schedule/cease_job,jobId:{},arg:{}", calculateJobMQMessage.getJobId(), arg);

        String tenantId = calculateJobMQMessage.getTenantId();
        UpdateCalculationJob.Result result = formulaJobScheduleProxy.ceaseCalculateJob(tenantId, arg);
        if (Objects.nonNull(result) && result.getCode() == 0) {
            asyncTaskApi.cancelTaskByTaskId(tenantId, arg.getJobId());
        }
        log.info("ceaseCalculateJob response:{},jobId:{},cost:{}ms", result, calculateJobMQMessage.getJobId()
                , System.currentTimeMillis() - startTime);
    }

}
