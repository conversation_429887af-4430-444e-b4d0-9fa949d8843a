package com.facishare.paas.task.calculate.config;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.task.calculate.model.DelayTime;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * Created by liwei on 2019/7/6
 */
@Slf4j
public class CalculateConfig {
    private static final String ALL = "ALL";
    public static final int MAX_RETRY_TIMES = 3;
    public static final Splitter CONFIG_SPLITTER = Splitter.on(",").omitEmptyStrings().trimResults();

    public static final boolean IS_GRAY_VERSION = false;

    public static String configName = "fs-paas-calculate-task-config";

    public volatile static Config config;

    public volatile static Map<String, Integer> ipGroupIndexMap;
    private volatile static Map<String, Integer> tenantGroupIndexMap;
    private volatile static Set<String> countAndCalcJobGrayDispatcherTenantIds = Sets.newHashSet();
    private volatile static Set<String> formulaGrayDispatcherTenantIds = Sets.newHashSet();
    private volatile static Set<String> detailLogTenantIds = Sets.newHashSet();

    private static Map<String, MonitorConfig> monitorConfigItemMap = Maps.newHashMap();

    static {
        ConfigFactory.getInstance().getConfig(configName, config -> {
            reload(config);
        });
    }

    private static void reload(IConfig conf) {
        String content = new String(conf.getContent());
        if (StringUtils.isEmpty(content)) {
            log.error("{} config content is empty", configName);
        }
        config = JSON.parseObject(content, Config.class);
        if (StringUtils.isNotEmpty(config.getGroupIpIndex())) {
            ipGroupIndexMap = JSON.parseObject(config.getGroupIpIndex(), Map.class);
        }
        if (StringUtils.isNotEmpty(config.getTenantIndex())) {
            List<TenantIndex> tenantIndices = JSON.parseArray(config.getTenantIndex(), TenantIndex.class);
            tenantGroupIndexMap = tenantIndices.stream()
                    .flatMap(it -> CollectionUtils.nullToEmpty(it.getEi()).stream().map(ei -> Tuple.of(ei, it.getIndex())))
                    // 企业配置重复，最后的配置项会生效
                    .collect(Collectors.toMap(Tuple::getKey, Tuple::getValue, (x, y) -> y));
        }
        countAndCalcJobGrayDispatcherTenantIds = getSetFromConfig(config.getCountAndCalcJobGrayDispatcherTenantIds());
        formulaGrayDispatcherTenantIds = getSetFromConfig(config.getFormulaGrayDispatcherTenantIds());
        detailLogTenantIds = getSetFromConfig(config.getDetailLogTenantIds());

        Map<String, MonitorConfig> configMap = Maps.newHashMap();
        for (MonitorConfig monitorConfig : CollectionUtils.nullToEmpty(config.getMonitorConfig())) {
            monitorConfig.getTenantIds().forEach(tenantId -> configMap.putIfAbsent(tenantId, monitorConfig));
        }
        monitorConfigItemMap = configMap;
        log.info("reload config:{},content:{}", configName, config);
    }

    private static Set<String> getSetFromConfig(String value) {
        if (Strings.isNullOrEmpty(value)) {
            return Sets.newHashSet();
        }
        return Sets.newHashSet(CONFIG_SPLITTER.split(value));
    }

    @Data
    public static class Config {
        int retryTimes;
        int grayRetryTimes;
        int fieldNotExistRetryTime;
        boolean graphCache = true;
        int slowTimesThreshold = 10;
        int slowCostThreshold = 3000;
        int esReportStartTime = 1500;
        int msgDelayLevel = 1;
        int blackMsgDelayLevel = 1;
        int calculationJobMsgDelayLevel = 7;
        String groupIpIndex;
        String tenantIndex;

        String grayTenantIds;
        String stageTenantIds;
        String vipTenantIds;
        int delayTime = 60;
        List<FieldBlacklist> fieldBlackList = Lists.newArrayList();
        List<MetadataObjectBlacklist> metadataObjectBlacklistList = Lists.newArrayList();
        String countAndCalcJobGrayDispatcherTenantIds;
        String formulaGrayDispatcherTenantIds;
        List<DispatchDelayTime> dispatchDelayTimeList;
        String detailLogTenantIds;
        Map<String, String> dispatcherTopicMapping;
        Map<String, DispatcherTopicConfig> dispatcherTopicConfigs;
        List<String> optoolTraceIdPrefixes;
        List<String> optoolAppPrefixes;
        List<String> optoolDataSources;
        int queryRelateDataBatchSize;
        int relateDataCountForOptoolQueue;

        List<MonitorConfig> monitorConfig;
        String defaultSessionId;
    }

    @Data
    public static class TenantIndex {
        private int index;
        private Set<String> ei;
    }

    public static int getRetryTimes() {
        if (config != null && config.retryTimes > 0) {
            return config.retryTimes;
        }
        return MAX_RETRY_TIMES;
    }

    public static int getFieldValidateRetryTimes() {
        if (config != null && config.fieldNotExistRetryTime > 0) {
            return config.fieldNotExistRetryTime;
        }
        return 0;
    }

    public static int getDelayTime(String tenantId) {
        int delayLevel = CalculateBlackListConfig.isBlackTenantId(tenantId) ? config.getBlackMsgDelayLevel() : config.getMsgDelayLevel();
        if (delayLevel > 0) {
            int delayTime = DelayTime.getDelayTime(delayLevel);
            return delayTime;
        }
        return 0;
    }

    public static Integer getDispatchDelayTime(String queueType, String fieldType) {
        if (Strings.isNullOrEmpty(queueType) || Strings.isNullOrEmpty(fieldType)) {
            return null;
        }
        if (Objects.isNull(config) || CollectionUtils.empty(config.getDispatchDelayTimeList())) {
            return null;
        }
        return config.getDispatchDelayTimeList().stream()
                .filter(x -> Objects.equals(queueType, x.getQueueType()))
                .filter(x -> CollectionUtils.empty(x.getFieldTypes()) || x.getFieldTypes().contains(fieldType))
                .findFirst()
                .map(x -> x.getDelayTime())
                .orElse(null);
    }

    public static int getCalculationJobMsgDelayLevel() {
        if (Objects.isNull(config)) {
            return 7;
        }
        return config.getCalculationJobMsgDelayLevel();
    }

    public static int getDelayLevel(String tenantId) {
        return CalculateBlackListConfig.isBlackTenantId(tenantId) ? config.getBlackMsgDelayLevel() : config.getMsgDelayLevel();
    }

    public static int getGroupIndex(String group) {
        if (CollectionUtils.notEmpty(ipGroupIndexMap)) {
            Integer index = ipGroupIndexMap.get(group);
            if (index != null) {
                return index;
            }
        }
        return 0;
    }

    public static int getTenantGroupIndex(String tenantId) {
        if (CollectionUtils.empty(tenantGroupIndexMap)) {
            return -2;
        }
        return tenantGroupIndexMap.getOrDefault(tenantId, -1);
    }

    public static boolean isFieldInBlacklist(String tenantId, String objectApiName, String fieldName, String queueType, String op, String originalObjectApiName) {
        if (config == null) {
            return false;
        }
        if (CollectionUtils.empty(config.fieldBlackList)) {
            return false;
        }
        return config.fieldBlackList.stream().anyMatch(x -> x.match(tenantId, objectApiName, fieldName, queueType, op, originalObjectApiName));
    }

    public static boolean isInMetadataObjectBlacklist(String op, String tenantId, String objectApiName) {
        if (config == null) {
            return false;
        }
        if (CollectionUtils.empty(config.getMetadataObjectBlacklistList())) {
            return false;
        }
        return config.getMetadataObjectBlacklistList().stream().anyMatch(x -> x.match(op, tenantId, objectApiName));
    }

    public static boolean isCountAndCalcJobGrayDispatcher(String tenantId) {
        if (config == null || CollectionUtils.empty(countAndCalcJobGrayDispatcherTenantIds)) {
            return false;
        }
        return countAndCalcJobGrayDispatcherTenantIds.contains(ALL) || countAndCalcJobGrayDispatcherTenantIds.contains(tenantId);
    }

    public static boolean isFormulaGrayDispatcher(String tenantId) {
        if (config == null || CollectionUtils.empty(formulaGrayDispatcherTenantIds)) {
            return false;
        }
        return formulaGrayDispatcherTenantIds.contains(ALL) || formulaGrayDispatcherTenantIds.contains(tenantId);
    }

    public static boolean isInDetailLogTenantIds(String tenantId) {
        if (CollectionUtils.empty(detailLogTenantIds)) {
            return false;
        }
        return detailLogTenantIds.contains(tenantId);
    }

    private static String getDispatcherTopic(String tenantId) {
        if (config == null || CollectionUtils.empty(config.getDispatcherTopicMapping())) {
            return null;
        }
        return config.getDispatcherTopicMapping().get(tenantId);
    }

    public static List<String> getDispatcherTopics(String tenantId, String objectApiName) {
        if (config == null) {
            return Collections.emptyList();
        }
        Map<String, DispatcherTopicConfig> dispatcherTopicConfigs = config.getDispatcherTopicConfigs();
        if (CollectionUtils.notEmpty(dispatcherTopicConfigs) && dispatcherTopicConfigs.containsKey(tenantId)) {
            DispatcherTopicConfig dispatcherTopicConfig = dispatcherTopicConfigs.get(tenantId);
            Map<String, List<String>> objectTopicMap = dispatcherTopicConfig.getObjectTopicMap();
            if (CollectionUtils.notEmpty(objectTopicMap) && objectTopicMap.containsKey(objectApiName)) {
                return objectTopicMap.get(objectApiName);
            }
            if (CollectionUtils.notEmpty(dispatcherTopicConfig.getDefaultTopics())) {
                return dispatcherTopicConfig.getDefaultTopics();
            }
        }
        String topic = getDispatcherTopic(tenantId);
        if (!Strings.isNullOrEmpty(topic)) {
            return Lists.newArrayList(topic);
        }
        return Collections.emptyList();
    }

    public static Set<String> getAllDispatcherTopics(String tenantId) {
        if (config == null) {
            return Sets.newHashSet();
        }
        Set<String> topics = Sets.newHashSet();
        Map<String, DispatcherTopicConfig> dispatcherTopicConfigs = config.getDispatcherTopicConfigs();
        if (CollectionUtils.notEmpty(dispatcherTopicConfigs) && dispatcherTopicConfigs.containsKey(tenantId)) {
            DispatcherTopicConfig dispatcherTopicConfig = dispatcherTopicConfigs.get(tenantId);
            Map<String, List<String>> objectTopicMap = dispatcherTopicConfig.getObjectTopicMap();
            if (CollectionUtils.notEmpty(objectTopicMap)) {
                objectTopicMap.values().forEach(x -> topics.addAll(x));
            }
            if (CollectionUtils.notEmpty(dispatcherTopicConfig.getDefaultTopics())) {
                topics.addAll(dispatcherTopicConfig.getDefaultTopics());
            }
        }
        String topic = getDispatcherTopic(tenantId);
        if (!Strings.isNullOrEmpty(topic)) {
            topics.add(topic);
        }
        return topics;
    }

    /**
     * 检查给定的追踪ID是否为Optool追踪ID
     * 通过检查追踪ID是否以配置的Optool追踪ID前缀开头来判断
     *
     * @param traceId 需要检查的追踪ID
     * @return 如果追踪ID以配置的任一前缀开头则返回true，否则返回false
     */
    public static boolean isOptoolTrace(String traceId) {
        return hasPrefixMatch(traceId, config != null ? config.getOptoolTraceIdPrefixes() : null);
    }

    /**
     * 检查给定的应用ID是否为Optool应用
     * 通过检查应用ID是否以配置的Optool应用ID前缀开头来判断
     *
     * @param appId 需要检查的应用ID
     * @return 如果应用ID以配置的任一前缀开头则返回true，否则返回false
     */
    public static boolean isOptoolApp(String appId) {
        return hasPrefixMatch(appId, config != null ? config.getOptoolAppPrefixes() : null);
    }

    /**
     * 检查输入字符串是否以给定前缀列表中的任一前缀开头
     *
     * @param input    需要检查的输入字符串
     * @param prefixes 前缀列表
     * @return 如果输入字符串以前缀列表中的任一前缀开头则返回true，
     * 如果前缀列表为空或null则返回false
     */
    private static boolean hasPrefixMatch(String input, List<String> prefixes) {
        if (StringUtils.isBlank(input)) {
            return false;
        }
        return Optional.ofNullable(prefixes)
                .filter(CollectionUtils::notEmpty)
                .map(list -> list.stream().anyMatch(input::startsWith))
                .orElse(false);
    }

    /**
     * 检查数据源是否为 Optool 数据源
     *
     * @param dataSource 数据源字符串，多个数据源以"|"分隔
     * @return 如果任一数据源在配置的 Optool 数据源列表中，则返回 true
     */
    public static boolean isOptoolDataSource(String dataSource) {
        return Optional.ofNullable(dataSource)
                .filter(StringUtils::isNotEmpty)
                .filter(ds -> Objects.nonNull(config))
                .filter(ds -> CollectionUtils.notEmpty(config.getOptoolDataSources()))
                .map(ds -> StringUtils.split(ds, "|"))
                .map(Arrays::stream)
                .map(stream -> stream.anyMatch(config.getOptoolDataSources()::contains))
                .orElse(false);
    }

    public static int getQueryRelateDataBatchSize() {
        if (Objects.isNull(config) || config.getQueryRelateDataBatchSize() <= 0) {
            return 100;
        }
        return config.getQueryRelateDataBatchSize();
    }

    public static int getRelateDataCountForOptoolQueue() {
        if (Objects.isNull(config) || config.getRelateDataCountForOptoolQueue() <= 0) {
            return 1000;
        }
        return config.getRelateDataCountForOptoolQueue();
    }

    public static boolean isFindDataWithESInCalculateTask(String tenantId) {
        return UdobjGrayConfig.isAllow("findDataWithESInCalculateTask", tenantId);
    }

    @Data
    private static class FieldBlacklist {
        List<String> tenantIds = Lists.newArrayList();
        Map<String, List<String>> fields = Maps.newHashMap();
        List<String> queueTypes = Lists.newArrayList();
        List<String> ops = Lists.newArrayList();
        List<String> originalObjectApiNames = Lists.newArrayList();

        public boolean match(String tenantId, String objectApiName, String fieldApiName, String queueType, String op, String originalObjectApiName) {
            if (!this.tenantIds.contains(ALL) && !this.tenantIds.contains(tenantId)) {
                return false;
            }
            List<String> fieldMap = this.fields.getOrDefault(objectApiName, Collections.emptyList());
            if (!fieldMap.contains(ALL) && !fieldMap.contains(fieldApiName)) {
                return false;
            }
            if (!this.queueTypes.contains(ALL) && !this.queueTypes.contains(queueType)) {
                return false;
            }
            if (CollectionUtils.notEmpty(this.ops) && !this.ops.contains(ALL) && !this.ops.contains(op)) {
                return false;
            }
            return CollectionUtils.empty(this.originalObjectApiNames)
                    || this.originalObjectApiNames.contains(ALL)
                    || this.originalObjectApiNames.contains(originalObjectApiName);
        }
    }

    @Data
    private static class MetadataObjectBlacklist {
        List<String> ops = Lists.newArrayList();
        List<String> tenantIds = Lists.newArrayList();
        List<String> objectApiNames = Lists.newArrayList();

        public boolean match(String op, String tenantId, String objectApiName) {
            if (CollectionUtils.empty(ops) || CollectionUtils.empty(this.tenantIds) || CollectionUtils.empty(this.objectApiNames)) {
                return false;
            }
            if (!this.ops.contains(ALL) && !this.ops.contains(op)) {
                return false;
            }
            if (!this.tenantIds.contains(ALL) && !this.tenantIds.contains(tenantId)) {
                return false;
            }
            return this.objectApiNames.contains(ALL) || this.objectApiNames.contains(objectApiName);
        }
    }

    public static Set<String> getMonitorGrayTenantIds() {
        return ImmutableSet.copyOf(monitorConfigItemMap.keySet());
    }

    public static MonitorConfig getMonitorConfigByTenantId(String tenantId) {
        return monitorConfigItemMap.get(tenantId);
    }

    public static String getDefaultSessionId() {
        String defaultSessionId = config.getDefaultSessionId();
        if (!Strings.isNullOrEmpty(defaultSessionId)) {
            return defaultSessionId;
        }
        return "6b1de449f5484149b377e97526351c04";
    }

    @Data
    public static class DispatcherTopicConfig {
        List<String> defaultTopics;
        Map<String, List<String>> objectTopicMap;
    }

    @Data
    public static class DispatchDelayTime {
        String queueType;
        List<String> fieldTypes;
        Integer delayTime;
    }

    @Data
    public static class MonitorConfig {
        public static final int DEFAULT_VALUE = 300;
        /**
         * 包含多个id用，隔开
         */
        private String tenantId;
        private Map<String, Integer> timeThreshold;
        private Set<String> sessionIds;

        public int getTimeByMode(String mode) {
            return timeThreshold.getOrDefault(mode, DEFAULT_VALUE);
        }

        public Set<String> getSessionIds() {
            if (Objects.isNull(sessionIds)) {
                return Collections.emptySet();
            }
            return ImmutableSet.copyOf(sessionIds);
        }

        public Set<String> getTenantIds() {
            return Sets.newHashSet(CONFIG_SPLITTER.split(tenantId));
        }
    }

}
