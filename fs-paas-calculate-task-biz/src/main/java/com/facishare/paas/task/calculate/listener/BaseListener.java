package com.facishare.paas.task.calculate.listener;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.metadata.exception.FieldNotExistException;
import com.facishare.paas.task.calculate.config.CalculateConfig;
import com.facishare.paas.task.calculate.config.CalculateTaskApplication;
import com.facishare.paas.task.calculate.exception.FieldValidateException;
import com.facishare.paas.task.calculate.service.SendMessageService;
import com.facishare.paas.task.calculate.util.MessageSupport;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.autoconf.helper.ConfigHelper;
import com.github.trace.TraceContext;
import org.apache.rocketmq.common.message.MessageExt;
import org.elasticsearch.common.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.UUID;

/**
 * Created by liwei on 2018/11/27
 */
public abstract class BaseListener<A> {
    @Autowired
    private SendMessageService resendService;
    @Autowired
    protected CalculateTaskApplication calculateTaskApplication;

    private static final String APP_NAME = ConfigHelper.getProcessInfo().getName();

    protected Logger log = LoggerFactory.getLogger(getClass());

    protected abstract A parseMessage(MessageExt message);

    protected abstract String getArgDesc(A arg);

    protected abstract void doConsume(A arg);

    public void consumeMessage(List<MessageExt> messages) {
        if (CollectionUtils.empty(messages)) {
            return;
        }

        messages.forEach(message -> {
            long startTime = System.currentTimeMillis();
            initTraceContext(message);
            log.debug("receive msg:{}", message.getMsgId());
            A arg = null;
            try {
                // 预处理后的实际计算队列，处理 vip 和其他队列之间的 resend 逻辑
                if (needResend(message)) {
                    log.debug("resend msg:{}", message.getMsgId());
                    resend(message);
                    return;
                }
                if (!isMessageResended(message) && skipByMessage(message)) {
                    log.debug("skip by msg:{}", message.getMsgId());
                    return;
                }
                arg = parseMessage(message);
                if (!isMessageResended(message) && skip(arg, message)) {
                    log.debug("skip msg:{}", message.getMsgId());
                    return;
                }
                log.info("consume msgId:{},topic:{},tags:{},arg:{}", message.getMsgId(), message.getTopic(),
                        message.getTags(), getArgDesc(arg));
                initContext(arg);
                doConsume(arg);
                log.info("consume end cost:{}", System.currentTimeMillis() - startTime);
            } catch (AppBusinessException e) {
                log.warn("consume failed:{},msgId:{}", e.getMessage(), message.getMsgId(), e);
                reConsumeMsg(message, e);
            } catch (Exception e) {
                log.error("consume error:{},msgId:{},arg:{}", e.getMessage(), message.getMsgId(), arg, e);
                reConsumeMsg(message, e);
            } finally {
                TraceContext.remove();
                cleanContext();
            }
        });
    }

    protected boolean needResend(MessageExt message) {
        return false;
    }

    private void resend(MessageExt message) {
        resendService.resendMessage(message);
    }

    private boolean isMessageResended(MessageExt message) {
        return MessageSupport.getReSendTimes(message) > 0;
    }

    private boolean skipByMessage(MessageExt message) {
        String tenantId = MessageSupport.getTenantId(message);
        if (Strings.isNullOrEmpty(tenantId)) {
            return false;
        }
        return isSkipMessage(tenantId, message);
    }

    protected boolean isSkipMessage(String tenantId, MessageExt message) {
        return false;
    }

    protected boolean skip(A arg, MessageExt message) {
        return false;
    }

    protected void reConsumeMsg(MessageExt msg, Exception e) {
        //消息异常重试
        int reconsumeTimes = msg.getReconsumeTimes();
        int maxReconsumeTimes = CalculateConfig.getRetryTimes();
        if (e instanceof FieldValidateException || e instanceof FieldNotExistException) {
            maxReconsumeTimes = CalculateConfig.getFieldValidateRetryTimes();
        }
        log.info("msgId:{},reconsumeTimes:{},maxReconsumeTimes:{}", msg.getMsgId(), reconsumeTimes, maxReconsumeTimes);
        if (reconsumeTimes < maxReconsumeTimes) {
            throw new RuntimeException(e);
        }
    }

    private TraceContext initTraceContext(MessageExt message) {
        TraceContext traceContext = TraceContext.get();
        MessageHelper.fillContextFromMessage(traceContext, message);
        traceContext.setTraceId(APP_NAME + "/" + UUID.randomUUID().toString().replace("-", ""));
        return traceContext;
    }

    private void initContext(A arg) {
        fillTraceContext(arg);
        RequestContext requestContext = RequestContext.builder()
                .lang(Lang.zh_CN)
                .user(getUser(arg))
                .tenantId(getTenantId(arg))
                .build();
        requestContext.setAttribute(RequestContext.Attributes.IS_CALCULATE_CONTEXT, true);
        requestContext.setAttribute(RequestContext.IS_NOT_NEED_DEEP_COPY, true);
        RequestContextManager.setContext(requestContext);
    }

    protected User getUser(A arg) {
        return User.systemUser(getTenantId(arg));
    }

    protected abstract String getTenantId(A arg);

    protected void fillTraceContext(A arg) {

    }

    private void cleanContext() {
        RequestContextManager.removeContext();
    }

}
