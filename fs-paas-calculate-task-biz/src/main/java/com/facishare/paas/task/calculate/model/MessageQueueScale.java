package com.facishare.paas.task.calculate.model;

import com.facishare.paas.task.calculate.config.CalculateConfig;
import com.facishare.paas.task.calculate.util.MessageSupport;
import com.facishare.paas.task.calculate.util.SlowTenantCache;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.paas.task.calculate.util.MessageConvert.BATCH_INDEX;
import static com.facishare.paas.task.calculate.util.MessageConvert.COUNT_TYPE;
import static com.facishare.paas.task.calculate.util.MessageConvert.DELAY_INDEX;
import static com.facishare.paas.task.calculate.util.MessageConvert.SLOW_INDEX;

@Slf4j
public enum MessageQueueScale {
    FORMULA(0, 0.5), COUNT(0.5, 0.4375),
    SLOW(0.9375, 0.0625),

    BATCH_FORMULA(0, 0.375), BATCH_COUNT(0.375, 0.375),
    DELAY_COUNT(0.75, 0.1875),
    BATCH_SLOW(0.9375, 0.0625),

    DEFAULT_QUEUE_INDEX(0, 1);

    @Getter
    private double startProportion;
    @Getter
    private double ownRange;

    private static final int BLOCK_NUM = 8;

    private static Map<String, MessageQueueScale> queueIndexMap;

    static {
        queueIndexMap = Stream.of(values()).collect(Collectors.toMap(MessageQueueScale::name, x -> x));
    }

    MessageQueueScale(double startProportion, double ownRange) {
        this.startProportion = startProportion;
        this.ownRange = ownRange;
    }

    public static MessageQueueScale getByName(String name) {
        MessageQueueScale messageQueueIndex = queueIndexMap.get(name);
        if (messageQueueIndex != null) {
            return messageQueueIndex;
        }
        if (name.contains(BATCH_SLOW.name())) {
            return BATCH_SLOW;
        }
        if (name.contains(SLOW.name())) {
            return SLOW;
        }
        return DEFAULT_QUEUE_INDEX;
    }

    public static MessageQueueScale fromMessage(Message message, boolean isBatch) {
        String tenantId = MessageSupport.getTenantId(message);
        String type = MessageSupport.getType(message);
        boolean isSlow = SlowTenantCache.isSlowTenant(tenantId);

        if (COUNT_TYPE.equals(type) && MessageSupport.isDelayed(message)) {
            return getByName(DELAY_INDEX + "_" + type);
        }
        String queueIndexName = type;
        if (isSlow) {
            queueIndexName = SLOW_INDEX + "_" + queueIndexName;
        }
        if (isBatch) {
            queueIndexName = BATCH_INDEX + "_" + queueIndexName;
        }
        return getByName(queueIndexName);
    }

    public int getIndex( String group, int mqSize) {
        try {
            int groupIndex = CalculateConfig.getGroupIndex(group);
            int hashCode = group.replace(".", "").hashCode();
            int blockIndex = groupIndex % BLOCK_NUM;
            int blockQueueCount = mqSize / BLOCK_NUM;

            int range;
            int startIndex;
            if (name().contains(SLOW.name())) {
                startIndex = FORMULA.getOwnRange(blockQueueCount) + COUNT.getOwnRange(blockQueueCount);
                range = blockQueueCount - startIndex;
            } else if (name().contains(BATCH_SLOW.name())) {
                startIndex = BATCH_FORMULA.getOwnRange(blockQueueCount) + BATCH_COUNT.getOwnRange(blockQueueCount) + DELAY_COUNT.getOwnRange(blockQueueCount);
                range = blockQueueCount - startIndex;
            } else {
                range = getOwnRange(blockQueueCount);
                startIndex = getStartProportion(blockQueueCount);
            }

            if (startIndex >= blockQueueCount) {
                startIndex = blockQueueCount - 1;
            }
            if (range <= 0) {
                range = 1;
            }

            int index = blockIndex * blockQueueCount + startIndex + Math.abs(hashCode % range);
            log.info("messageQueueIndex:{},mqSize:{},blockIndex:{},blockQueueCount:{},startIndex:{},range:{},index:{}",
                    name(), mqSize, blockIndex, blockQueueCount, startIndex, range, index);
            return index;
        } catch (Exception e) {
            log.error("getTagIndexByTenant error:", e);
        }
        return 0;
    }

    public int getStartProportion(int mqSize) {
        return Double.valueOf(mqSize * startProportion).intValue();
    }

    public int getOwnRange(int mqSize) {
        int range = Double.valueOf(mqSize * ownRange).intValue();
        if (range > 0) {
            return range;
        }
        return 1;
    }
}
