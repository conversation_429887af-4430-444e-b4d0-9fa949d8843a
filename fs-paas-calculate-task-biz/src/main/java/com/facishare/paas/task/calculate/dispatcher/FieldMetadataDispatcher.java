package com.facishare.paas.task.calculate.dispatcher;

import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.cache.RedisDao;
import com.facishare.paas.appframework.metadata.exception.FieldNotExistException;
import com.facishare.paas.appframework.metadata.filter.ObjectDataFilter;
import com.facishare.paas.appframework.metadata.relation.*;
import com.facishare.paas.appframework.metadata.relation.RelateEdge.RelateEdgeNode;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.task.calculate.config.CalculateConfig;
import com.facishare.paas.task.calculate.model.CalculateDataChangeMessage;
import com.facishare.paas.task.calculate.model.CalculateNode;
import com.facishare.paas.task.calculate.model.CountDataInfo;
import com.facishare.paas.task.calculate.model.CustomDataChangeMessage;
import com.facishare.paas.task.calculate.service.CalculateEventService;
import com.facishare.paas.task.calculate.service.DataPackageService;
import com.facishare.paas.task.calculate.service.DescribePackageService;
import com.facishare.paas.task.calculate.service.SendMessageService;
import com.facishare.paas.task.calculate.util.MessageConvert;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by liwei on 2019/7/6
 */
@Slf4j
@SuperBuilder
public abstract class FieldMetadataDispatcher {
    private ReferenceLogicService referenceLogicService;
    private CalculateEventService calculateEventService;
    private DescribePackageService describePackageService;
    private DataPackageService dataPackageService;
    private SendMessageService sendMessageService;
    private RedisDao redisDao;
    protected ObjectAction action;
    protected CustomDataChangeMessage message;

    private static List<String> excludeList = Lists.newArrayList("version", "is_deleted");
    private static String SET_REDIS_SUCCESS = "OK";

    private FieldRelationGraph graph;

    protected boolean skipFilterCount() {
        return false;
    }

    protected IObjectDescribe getDescribe(String tenantId, String describeApiName) {
        return describePackageService.fetchObject(tenantId, describeApiName);
    }

    protected List<IObjectData> findObjectDataList(String tenantId, String describeApiName, List<String> dataIds, boolean includeInvalid) {
        List<IObjectData> dataList;
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        if (includeInvalid) {
            //作废的消息需要查询包含已删除的数据，因为作废之后直接删除或者不作废直接删除的话，数据状态是已删除的。
            dataList = dataPackageService.findObjectDataByIdsIncludeDeletedIgnoreAll(user, dataIds, describeApiName);
        } else {
            dataList = dataPackageService.findObjectDataByIdsIgnoreAll(tenantId, dataIds, describeApiName);
        }
        return dataList;
    }

    protected FieldRelationGraph getGraph(IObjectDescribe describe) {
        return describePackageService.fetchGraph(describe);
    }

    protected Map<String, Set<NodeEdgePair>> aggregatedCount(IObjectDescribe describe, String dataId, Set<NodeEdgePair> nodeEdgePairs,
                                                             Map<String, IObjectDescribe> describeMap, Map<String, IObjectData> dbDataMap,
                                                             Map<String, Object> beforeData) {
        if (Strings.isNullOrEmpty(dataId)) {
            return Collections.emptyMap();
        }
        // 获取需要计算的节点（当前对象的节点可以有多级）以及节点间的关系
        Set<NodeEdgePair> countSet = nodeEdgePairs.stream().filter(it -> it.getFieldNode().isCountField()).collect(Collectors.toSet());
        if (CollectionUtils.empty(countSet)) {
            return Collections.emptyMap();
        }
        //根据统计字段的过滤条件和从对象的数据判断是否需要计算，过滤掉不符合条件的统计字段
        if (!skipFilterCount()) {
            countSet = filterCount(describeMap, countSet, describe, dataId, dbDataMap, beforeData);
            if (CollectionUtils.empty(countSet)) {
                return Collections.emptyMap();
            }
        }
        Map<String, Set<NodeEdgePair>> result = Maps.newHashMap();
        result.put(dataId, countSet);
        return result;
    }

    protected void computeFormulaAndSendMessage(IObjectDescribe describe, String dataId, ObjectAction action, String eventId, Set<NodeEdgePair> nodeEdgePairs) {
        if (Strings.isNullOrEmpty(dataId)) {
            return;
        }
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + ".computeFormulaAndSendMessage");
        try {
            Set<NodeEdgePair> calculate = nodeEdgePairs.stream().filter(it -> !it.getFieldNode().isCountField()).collect(Collectors.toSet());

            if (CollectionUtils.notEmpty(calculate)) {
                Map<String, IObjectDescribe> objectDescribeMap = Maps.newHashMap(graph.getDescribeMap());
                objectDescribeMap.put(describe.getApiName(), describe);
                Set<String> apiNames = calculate.stream().filter(x -> !objectDescribeMap.containsKey(x.getObjectApiName())).map(x -> x.getFieldNode().getObjectApiName()).collect(Collectors.toSet());
                Map<String, IObjectDescribe> queryDescribeMap = describePackageService.fetchObjects(describe.getTenantId(), Lists.newArrayList(apiNames));
                objectDescribeMap.putAll(queryDescribeMap);
                // 不落地的引用字段不需要计算
                calculate.removeIf(x -> fieldNotNeedCalculate(objectDescribeMap, x.getFieldNode()));
                if (CollectionUtils.empty(calculate)) {
                    log.debug("calculate is empty, ei:{}, objectApiName:{}, dataId:{}, calculate:{}", describe.getTenantId(), describe.getApiName(), dataId, calculate);
                    return;
                }
                sendCalculateMessage(describe, Lists.newArrayList(dataId), action, calculate, eventId);
                stopWatch.lap("sendCalculateMessage");
            }
        } finally {
            stopWatch.logSlow(3000);
        }
    }

    private boolean fieldNotNeedCalculate(Map<String, IObjectDescribe> objectDescribeMap, FieldNode fieldNode) {
        if (!objectDescribeMap.containsKey(fieldNode.getObjectApiName())) {
            return true;
        }
        IFieldDescribe fieldDescribe = objectDescribeMap.get(fieldNode.getObjectApiName()).getFieldDescribe(fieldNode.getFieldApiName());
        return fieldDescribe == null || !FieldDescribeExt.of(fieldDescribe).isCalculateFieldsNeedStoreInDB();
    }

    private void splitCountMessage(IObjectDescribe objectDescribe, Map<String, Set<NodeEdgePair>> nodeEdgePairMap,
                                   Map<String, Map<String, Object>> beforeDataMap, Map<String, Map<String, Object>> afterDataMap,
                                   ObjectAction action, String eventId, Map<String, IObjectData> dbDataMap) {
        Set<CountDataInfo> countDataInfoSet = preProcessingCountField(objectDescribe, nodeEdgePairMap, beforeDataMap, afterDataMap, dbDataMap);
        log.debug("splitCountMessage countDataInfoSet:{}", countDataInfoSet);
        if (CollectionUtils.empty(countDataInfoSet)) {
            return;
        }

        //统计字段的消息直接写入聚合框架
//        if ((!message.isManual() && CalculateConfig.isCountAndCalcJobGrayDispatcher(objectDescribe.getTenantId())) || CalculateConfig.isFormulaGrayDispatcher(objectDescribe.getTenantId())) {
//            List<CalculateDataEvent> events = countDataInfoSet.stream().map(x -> x.toEvent(message)).collect(Collectors.toList());
//            log.info("upsertEvents:{}", events);
//            calculateEventService.upsertEvents(events);
//            return;
//        }

        if (CalculateConfig.isInDetailLogTenantIds(message.getTenantId()) && action == ObjectAction.UPDATE) {
            log.info("dispatch count by msg:{}", message);
        }

        sendCountMessage(countDataInfoSet, false, eventId, action);
    }

    private void sendCalculateMessage(IObjectDescribe describe, List<String> dataIdList, ObjectAction action, Set<NodeEdgePair> nodeEdgePairSet, String eventId) {
        if (CollectionUtils.empty(nodeEdgePairSet)) {
            return;
        }
        log.debug("sendCalculateMessage ei:{}, objectApiName:{}, action:{} nodeEdgePairSet:{}", describe.getTenantId(), describe.getApiName(), action, nodeEdgePairSet);
        List<CalculateNode> calculateNodes = nodeEdgePairSet.stream().flatMap(nodeEdgePair -> {
            FieldNode fieldNode = nodeEdgePair.getFieldNode();
            return nodeEdgePair.getRelateEdge().getRelateEdgeNodes().stream().map(it -> CalculateNode.of(fieldNode, it));
        }).collect(Collectors.toList());
        log.debug("sendCalculateMessage calculateNodes:{}", calculateNodes);

        CalculateDataChangeMessage dataChangeMessage = CalculateDataChangeMessage.builder()
                .eventId(eventId).tenantId(message.getTenantId())
                .op(action.getActionCode())
                .describeApiName(describe.getApiName())
                .dataIdList(dataIdList)
                .calculateNodes(calculateNodes)
                .originalMessageId(message.getMessageId())
                .originalBornTimestamp(message.getOriginalBornTimestamp())
                .build();

        MessageConvert.builder()
                .dataChangeMessage(dataChangeMessage)
                .isBatch(message.isBatch())
                .fromOpTool(message.isFromOpTool())
                .isDelayed(false)
                .sendMessageService(sendMessageService)
                .tenantId(message.getTenantId())
                .type(MessageConvert.FORMULA_TYPE)
                .originalObjectIds(dataIdList)
                .originalDescribeApiName(describe.getApiName())
                .build()
                .convertAndSendMessage();
    }

    private void sendCountMessage(Set<CountDataInfo> countDataInfoSet, boolean isDelayed, String eventId, ObjectAction action) {
        if (CollectionUtils.empty(countDataInfoSet)) {
            return;
        }

        for (CountDataInfo countDataInfo : countDataInfoSet) {
            CalculateNode calculateNode = CalculateNode.of(countDataInfo);
            CalculateDataChangeMessage dataChangeMessage = CalculateDataChangeMessage.builder()
                    .eventId(eventId)
                    .tenantId(message.getTenantId())
                    .describeApiName(countDataInfo.getObjectApiName())
                    .dataIdList(Lists.newArrayList(countDataInfo.getDataId()))
                    .calculateNodes(Lists.newArrayList(calculateNode))
                    .originalMessageId(message.getMessageId())
                    .originalBornTimestamp(message.getOriginalBornTimestamp())
                    .op(action.getActionCode())
                    .build();

            if (isDelayed) {
                boolean isFilter = filterCountMessage(message.getTenantId(), countDataInfo.getDataId(), calculateNode);
                if (isFilter) {
                    log.info("filterCount by redis,ei:{},di:{},mon:{},fn:{}", message.getTenantId(), countDataInfo.getDataId(), calculateNode.getObjectApiName(), calculateNode.getFieldApiName());
                    continue;
                }
            }

            String msgId = MessageConvert.builder()
                    .dataChangeMessage(dataChangeMessage)
                    .isBatch(message.isBatch())
                    .isDelayed(isDelayed)
                    .fromOpTool(message.isFromOpTool())
                    .sendMessageService(sendMessageService)
                    .tenantId(message.getTenantId())
                    .type(MessageConvert.COUNT_TYPE)
                    .originalDescribeApiName(countDataInfo.getOriginalDescribeApiName())
                    .originalObjectId(countDataInfo.getOriginalObjectId())
                    .build()
                    .convertAndSendMessage();
            if (CalculateConfig.isInDetailLogTenantIds(message.getTenantId()) && action == ObjectAction.UPDATE) {
                log.info("send count msg,id:{},body:{}", msgId, dataChangeMessage);
            }
        }
    }

    public void doDispatch() {
        List<CustomDataChangeMessage.DataContent> body = message.getBody();
        Map<String, Set<NodeEdgePair>> aggregatedCountResult = Maps.newConcurrentMap();
        String objectApiName = body.get(0).getEntityId();
        String eventId = body.get(0).getEventId();
        Map<String, Map<String, Object>> beforeDataMap = Maps.newHashMap();
        Map<String, Map<String, Object>> afterDataMap = Maps.newHashMap();

        IObjectDescribe objectDescribe = getDescribe(message.getTenantId(), objectApiName);
        Map<String, IObjectData> dbDataMap = Maps.newHashMap();
        for (CustomDataChangeMessage.DataContent content : body) {
            String dataId = content.getObjectId();
            if (Strings.isNullOrEmpty(dataId)) {
                continue;
            }
            if (skip(content)) {
                continue;
            }
            // 变更的字段
            List<String> fieldChanges = getChangeFields(objectDescribe, content);
            // 构图
            graph = getGraph(objectDescribe);
            // 拿到受影响的字段
            Set<NodeEdgePair> calculateNodeEdgePairs = getCalculateNodeEdgePairs(fieldChanges, objectDescribe, graph);
            // 特殊处理人员对象变更
            if (ObjectDescribeExt.PERSONNEL_OBJ_API_NAME.equals(objectApiName) && AppFrameworkConfig.isGrayFilterEmployeeFormula(message.getTenantId())) {
                calculateNodeEdgePairs.addAll(getCalculateNodeEdgePairsFromReference(message.getTenantId(), objectApiName, fieldChanges));
            }
            if (ObjectDescribeExt.DEPARTMENT_OBJ_API_NAME.equals(objectApiName) && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.GRAY_FILTER_BY_DEPARTMENT_FORMULA_TENANTS, message.getTenantId())) {
                calculateNodeEdgePairs.addAll(getCalculateNodeEdgePairsFromReference(message.getTenantId(), objectApiName, fieldChanges));
            }
            //过滤黑名单字段
            calculateNodeEdgePairs.removeIf(x -> CalculateConfig.isFieldInBlacklist(message.getTenantId(),
                    x.getFieldNode().getObjectApiName(), x.getFieldNode().getFieldApiName(), message.getQueueType(), message.getOp(), objectApiName));
            // 处理计算字段
            computeFormulaAndSendMessage(objectDescribe, dataId, action, eventId, calculateNodeEdgePairs);
            // 获取 beforeData
            Map<String, Object> beforeData = content.getBeforeTriggerData();
            if (CollectionUtils.notEmpty(beforeData)) {
                beforeDataMap.put(dataId, beforeData);
            }
            Map<String, Object> afterData = content.getAfterTriggerData();
            if (CollectionUtils.notEmpty(afterData)) {
                afterDataMap.put(dataId, afterData);
            }
            // 处理统计字段
            Map<String, Set<NodeEdgePair>> aggregatedCount = aggregatedCount(objectDescribe, dataId, calculateNodeEdgePairs, graph.getDescribeMap(), dbDataMap, beforeData);
            if (CollectionUtils.notEmpty(aggregatedCount)) {
                aggregatedCountResult.putAll(aggregatedCount);
            }
        }
        splitCountMessage(objectDescribe, aggregatedCountResult, beforeDataMap, afterDataMap, action, eventId, dbDataMap);
    }

    protected boolean skip(CustomDataChangeMessage.DataContent content) {
        return false;
    }

    protected List<String> getChangeFields(IObjectDescribe objectDescribe, CustomDataChangeMessage.DataContent content) {
        return ObjectDescribeExt.of(objectDescribe).getActiveFieldDescribes().stream().filter(x -> !excludeList.contains(x.getApiName())).map(IFieldDescribe::getApiName).collect(Collectors.toList());
    }

    protected Set<NodeEdgePair> getCalculateNodeEdgePairs(List<String> fieldChanges, IObjectDescribe objectDescribe, FieldRelationGraph graph) {
        Set<NodeEdgePair> resultSets = Sets.newHashSet();
        String objectApiName = objectDescribe.getApiName();
        fieldChanges.forEach(field -> {
            log.debug("describe:{},field:{}", objectApiName, field);
            graph.getNode(objectApiName, field).ifPresent(node -> {
                log.debug("node:{}", node);
                Set<FieldNode> successors = graph.successors(node);
                log.debug("successors:{}", successors);
                successors.forEach(rNode -> {
                    Optional<RelateEdge> relateEdge = graph.edgeValue(node, rNode);
                    relateEdge.ifPresent(edge -> resultSets.add(NodeEdgePair.of(rNode, edge)));
                    // 都是 S2S 的时候，在继续递归找影响的字段
                    relateEdge.filter(it -> it.allMatchRelateType(RelateType.S2S)).ifPresent(it -> fetchNodesFromGraph(resultSets, graph, rNode, objectDescribe));
                });
            });
        });
        return resultSets;
    }

    protected Set<NodeEdgePair> getCalculateNodeEdgePairsFromReference(String tenantId, String objectApiName, List<String> fieldChanges) {
        List<ReferenceData> referenceDataList = referenceLogicService.findReferenceByTarget(tenantId, TargetTypes.RELATED_DESCRIBE_FIELD, objectApiName, true);
        if (CollectionUtils.empty(referenceDataList)) {
            return Sets.newHashSet();
        }
        Set<NodeEdgePair> result = Sets.newHashSet();
        RelateType relateType;
        if (ObjectDescribeExt.PERSONNEL_OBJ_API_NAME.equals(objectApiName)) {
            relateType = RelateType.R2P;
        } else if (ObjectDescribeExt.DEPARTMENT_OBJ_API_NAME.equals(objectApiName)) {
            relateType = RelateType.R2O;
        } else {
            log.warn("getCalculateNodeEdgePairsFromReference fail, unSupport objectApiName:{}, ei:{}", objectApiName, tenantId);
            return Sets.newHashSet();
        }
        referenceDataList.forEach(referenceData -> {
            //targetValue的格式：对象的apiName.变量字段的apiName.人员字段的apiName
            String[] variableInfo = StringUtils.split(referenceData.getTargetValue(), ".");
            if (!fieldChanges.contains(variableInfo[1])) {
                return;
            }
            //sourceValue的格式：对象的apiName.计算字段的apiName
            String[] formulaInfo = StringUtils.split(referenceData.getSourceValue(), ".");
            NodeEdgePair nodeEdgePair = NodeEdgePair.of(FieldNode.of(formulaInfo[0], formulaInfo[1], FieldNode.NodeType.FORMULA), RelateEdge.of(RelateEdgeNode.of(relateType, variableInfo[2])));
            result.add(nodeEdgePair);
        });
        return result;
    }

    private void fetchNodesFromGraph(Set<NodeEdgePair> resultSets, FieldRelationGraph graph, FieldNode node, IObjectDescribe objectDescribe) {
        Set<FieldNode> successors = graph.successors(node);
        if (CollectionUtils.empty(successors)) {
            return;
        }
        successors.stream().filter(it -> Objects.equals(it.getObjectApiName(), objectDescribe.getApiName())).forEach(rNode -> {
            Optional<RelateEdge> relateEdge = graph.edgeValue(node, rNode);
            relateEdge.filter(it -> it.allMatchRelateType(RelateType.S2S)).ifPresent(it -> {
                resultSets.add(NodeEdgePair.of(rNode, relateEdge.get()));
                fetchNodesFromGraph(resultSets, graph, rNode, objectDescribe);
            });
        });
    }

    protected boolean includeInvalidData() {
        return false;
    }

    protected Set<NodeEdgePair> filterCount(Map<String, IObjectDescribe> graphDescribeMap, Set<NodeEdgePair> countNodeEdgePairs, IObjectDescribe detailDescribe, String dataId, Map<String, IObjectData> dbDataMap, Map<String, Object> beforeData) {
        Set<NodeEdgePair> result = countNodeEdgePairs.stream().filter(x -> x.allMatchRelateType(RelateType.S2S)).collect(Collectors.toSet());
        Set<NodeEdgePair> toFilterNodes = countNodeEdgePairs.stream().filter(x -> !result.contains(x)).collect(Collectors.toSet());
        if (CollectionUtils.empty(toFilterNodes)) {
            return result;
        }
        List<IObjectData> detailData = findObjectDataList(detailDescribe.getTenantId(), detailDescribe.getApiName(), Lists.newArrayList(dataId), includeInvalidData());
        if (CollectionUtils.empty(detailData)) {
            return result;
        }
        detailData.forEach(x -> dbDataMap.put(x.getId(), x));

        List<IObjectData> toFilterDataList = Lists.newArrayList();
        detailData.forEach(x -> {
            //已作废的数据需要根据作废前的生命状态来判断统计字段的过滤条件
            if (ObjectDataExt.of(x).getLifeStatus() == ObjectLifeStatus.INVALID) {
                IObjectData cpData = ObjectDataExt.of(x).copy();
                ObjectDataExt.of(cpData).setLifeStatus(ObjectLifeStatus.of(ObjectDataExt.of(x).getLifeStatusBeforeInvalid(), false));
                toFilterDataList.add(cpData);
            } else {
                toFilterDataList.add(x);
            }
        });

        log.debug("keySet:{},countNodes:{}", graphDescribeMap.keySet(), toFilterNodes);
        toFilterNodes.forEach(it -> {
            FieldNode fieldNode = it.getFieldNode();
            IObjectDescribe masterDescribe = graphDescribeMap.get(fieldNode.getObjectApiName());
            if (masterDescribe == null) {
                masterDescribe = getDescribe(detailDescribe.getTenantId(), fieldNode.getObjectApiName());
            }
            Count count = (Count) masterDescribe.getFieldDescribe(fieldNode.getFieldApiName());
            if (Objects.isNull(count)) {
                log.warn("count field not exist,tenantId:{},field:{}.{}", detailDescribe.getTenantId(), fieldNode.getObjectApiName(), fieldNode.getFieldApiName());
                return;
            }
            List<LinkedHashMap> wheres = CountExt.of(count).getWheres();
            if (CollectionUtils.empty(wheres)) {
                result.add(it);
                return;
            }

            SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(wheres);
            queryExt.handleWheresFilter(null);
            ObjectDataFilter dataFilter = ObjectDataFilter.builder()
                    .filterLabel(fieldNode.getObjectApiName() + "." + fieldNode.getFieldApiName())
                    .describeExt(ObjectDescribeExt.of(detailDescribe))
                    .queryExt(queryExt)
                    .build();
            if (CollectionUtils.notEmpty(beforeData)) {
                IObjectData oldData = ObjectDataExt.of(Maps.newHashMap(beforeData));
                ObjectDataExt.of(oldData).merge(detailData.get(0));
                toFilterDataList.add(oldData);
            }
            try {
                List<IObjectData> objectData = dataFilter.doFilter(toFilterDataList);
                if (CollectionUtils.notEmpty(objectData)) {
                    result.add(it);
                } else {
                    log.info("filterCount by filter,ei:{},di:{},mon:{},fn:{}", masterDescribe.getTenantId(),
                            dataId, masterDescribe.getApiName(), fieldNode.getFieldApiName());
                }
            } catch (FieldNotExistException e) {
                log.warn("found invalid field in count filter,msg:{},ei:{},di:{},mon:{},fn:{}",
                        e.getMessage(), masterDescribe.getTenantId(), dataId, masterDescribe.getApiName(), fieldNode.getFieldApiName());
                result.add(it);
            }
        });
        return result;
    }

    //预处理统计字段
    private Set<CountDataInfo> preProcessingCountField(IObjectDescribe objectDescribe, Map<String, Set<NodeEdgePair>> nodeEdgePairMap,
                                                       Map<String, Map<String, Object>> beforeDataMap, Map<String, Map<String, Object>> afterDataMap,
                                                       Map<String, IObjectData> dbDataMap) {
        if (CollectionUtils.empty(nodeEdgePairMap)) {
            return Collections.emptySet();
        }

        Set<CountDataInfo> result = Sets.newHashSet();
        List<String> queryDataIdList = Lists.newArrayList(nodeEdgePairMap.keySet()).stream().filter(x -> !dbDataMap.containsKey(x)).collect(Collectors.toList());
        List<IObjectData> objectDataList = findObjectDataList(objectDescribe.getTenantId(), objectDescribe.getApiName(), queryDataIdList, includeInvalidData());
        objectDataList.addAll(nodeEdgePairMap.keySet().stream().filter(x -> dbDataMap.containsKey(x)).map(x -> dbDataMap.get(x)).collect(Collectors.toList()));
        for (IObjectData objectData : objectDataList) {
            Set<NodeEdgePair> nodeEdgePairs = nodeEdgePairMap.get(objectData.getId());
            if (CollectionUtils.empty(nodeEdgePairs)) {
                continue;
            }
            nodeEdgePairs.forEach(nodeEdgePair -> {
                FieldNode countNode = nodeEdgePair.getFieldNode();
                nodeEdgePair.getRelateEdge().getRelateEdgeNodes().forEach(relateEdgeNode -> {
                    Map<String, Object> beforeData = CollectionUtils.nullToEmpty(beforeDataMap).get(objectData.getId());
                    Map<String, Object> afterData = CollectionUtils.nullToEmpty(afterDataMap).get(objectData.getId());
                    Set<String> countDataIds = getCountDataIds(beforeData, afterData, objectData, objectDescribe, relateEdgeNode, countNode);
                    Set<CountDataInfo> countList = countDataIds.stream().map(x -> CountDataInfo.of(x, countNode, relateEdgeNode, objectData)).collect(Collectors.toSet());
                    result.addAll(countList);
                    if (CalculateConfig.isInDetailLogTenantIds(message.getTenantId()) && action == ObjectAction.UPDATE) {
                        log.info("countList:{}", countList);
                    }
                });
            });
        }
        if (CalculateConfig.isInDetailLogTenantIds(message.getTenantId()) && action == ObjectAction.UPDATE) {
            log.info("countList result:{}", result);
        }
        return result;
    }

    private Set<String> getCountDataIds(Map<String, Object> beforeData, Map<String, Object> afterData, IObjectData objectData,
                                        IObjectDescribe objectDescribe, RelateEdgeNode relateEdgeNode, FieldNode countNode) {
        //从回收站恢复数据需要计算本对象的统计字段
        if (relateEdgeNode.getRelateType() == RelateType.S2S) {
            return Sets.newHashSet(objectData.getId());
        }
        Set<String> result = Sets.newHashSet();
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
        String masterApiName = countNode.getObjectApiName();

        // 变更前后，关联对象的数据id
        describeExt.getFieldDescribeSilently(relateEdgeNode.getReferenceFieldName()).ifPresent(x -> {
            if (FieldDescribeExt.of(x).isWhatListField()) {
                WhatList whatList = (WhatList) x;
                result.addAll(getRefObjectIdsFromWhatList(objectData, whatList, masterApiName));
                if (CollectionUtils.notEmpty(beforeData)) {
                    result.addAll(getRefObjectIdsFromWhatList(ObjectDataExt.of(beforeData), whatList, masterApiName));
                }
                if (CollectionUtils.notEmpty(afterData)) {
                    result.addAll(getRefObjectIdsFromWhatList(ObjectDataExt.of(afterData), whatList, masterApiName));
                }
                return;
            }

            if (FieldDescribeExt.of(x).isWhatField()) {
                What what = (What) x;
                result.addAll(getRefObjectIdsFromWhat(beforeData, afterData, objectData, what, masterApiName));
                return;
            }

            String refFieldApiName = x.getApiName();
            //更新时，会传入更新前的数据
            String beforeDataId = CollectionUtils.notEmpty(beforeData) ? (String) beforeData.get(refFieldApiName) : null;
            String afterDataId = CollectionUtils.notEmpty(afterData) ? (String) afterData.get(refFieldApiName) : null;
            String newDataId = (String) objectData.get(refFieldApiName);
            if (StringUtils.isNotEmpty(beforeDataId)) {
                result.add(beforeDataId);
            }
            if (StringUtils.isNotEmpty(afterDataId)) {
                result.add(afterDataId);
            }
            if (StringUtils.isNotEmpty(newDataId)) {
                result.add(newDataId);
            }
            if (CalculateConfig.isInDetailLogTenantIds(message.getTenantId()) && action == ObjectAction.UPDATE) {
                log.info("bid:{},aid:{},nid:{},r:{},node:{},edge:{}", beforeDataId, afterDataId, newDataId, result,
                        countNode, relateEdgeNode);
            }
        });
        return result;
    }

    private List<String> getRefObjectIdsFromWhatList(IObjectData data, WhatList whatList, String masterApiName) {
        String idFieldName = whatList.getIdFieldApiName();
        List<Map> relatedObjectData = (List<Map>) data.get(idFieldName);

        if (CollectionUtils.empty(relatedObjectData)) {
            return Lists.newArrayList();
        }
        return relatedObjectData.stream().filter(m -> Objects.equals(masterApiName, m.get("describe_api_name"))).map(m -> (String) m.get("id")).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
    }

    private Set<String> getRefObjectIdsFromWhat(Map<String, Object> beforeData, Map<String, Object> afterData,
                                                IObjectData data, What what, String masterApiName) {
        Set<String> result = Sets.newHashSet();

        String apiNameFieldName = what.getApiNameFieldApiName();
        String idFieldName = what.getIdFieldApiName();

        beforeData = CollectionUtils.nullToEmpty(beforeData);
        String beforeRefObjectApiName = (String) beforeData.get(apiNameFieldName);
        String beforeRefObjectId = (String) beforeData.get(idFieldName);

        afterData = CollectionUtils.nullToEmpty(afterData);
        String afterRefObjectApiName = (String) afterData.get(apiNameFieldName);
        String afterRefObjectId = (String) afterData.get(idFieldName);

        String refObjectApiName = (String) data.get(apiNameFieldName);
        String refObjectId = (String) data.get(idFieldName);

        if (Objects.equals(masterApiName, refObjectApiName) && StringUtils.isNotEmpty(refObjectId)) {
            result.add(refObjectId);
        }

        if (StringUtils.isNotEmpty(beforeRefObjectId)) {
            if (Objects.equals(masterApiName, beforeRefObjectApiName)) {
                result.add(beforeRefObjectId);
            } else if (StringUtils.isEmpty(beforeRefObjectApiName) && Objects.equals(masterApiName, refObjectApiName)) {
                result.add(beforeRefObjectId);
            }
        }

        if (StringUtils.isNotEmpty(afterRefObjectId)) {
            if (Objects.equals(masterApiName, afterRefObjectApiName)) {
                result.add(afterRefObjectId);
            } else if (StringUtils.isEmpty(afterRefObjectApiName) && Objects.equals(masterApiName, refObjectApiName)) {
                result.add(afterRefObjectId);
            }
        }

        return result;
    }

    private boolean filterCountMessage(String tenantId, String dataId, CalculateNode calculateNode) {
        String key = String.format("calc_%s_%s_%s_%s", tenantId, calculateNode.getObjectApiName(), calculateNode.getFieldApiName(), dataId);
        boolean isFilter = false;
        try {
            String result = redisDao.setNx(key, "0", CalculateConfig.getDelayTime(tenantId));
            if (!SET_REDIS_SUCCESS.equals(result)) {
                isFilter = true;
            }
        } catch (Exception e) {
            log.error("filterCountMessage error,key:{}", key, e);
        }
        return isFilter;
    }
}
