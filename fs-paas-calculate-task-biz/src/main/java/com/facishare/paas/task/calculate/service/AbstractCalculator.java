package com.facishare.paas.task.calculate.service;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.relation.RelateType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.task.calculate.config.CalculateConfig;
import com.facishare.paas.task.calculate.model.*;
import com.facishare.paas.task.calculate.model.CalculateNodeExt.CalculateNodeGroup;
import com.facishare.paas.task.calculate.util.AuditLogUtil;
import com.facishare.paas.task.calculate.util.MessageConvert;
import com.facishare.paas.task.calculate.util.Modules;
import com.facishare.paas.task.calculate.util.QueueTypes;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.facishare.paas.task.calculate.util.MessageConvert.COUNT_TYPE;
import static com.facishare.paas.task.calculate.util.MessageConvert.FORMULA_TYPE;

/**
 * Created by liwei on 2018/11/13
 */
@Slf4j
public abstract class AbstractCalculator implements Calculator {
    protected DescribePackageService describePackageService;
    protected ExpressionCalculateLogicService expressionCalculateLogicService;
    protected MetaDataService metaDataService;
    protected IObjectDataService objectDataService;
    protected QuoteValueService quoteValueService;
    protected DataPackageService dataPackageService;
    protected CalculateEventService calculateEventService;
    protected SendMessageService sendMessageService;

    protected CalculateDataChangeMessage dataChangeMessage;
    protected Map<String, List<IObjectData>> dependentDataMap = Maps.newConcurrentMap();
    protected Map<String, IObjectDescribe> describeMap = Maps.newConcurrentMap();
    private static final List<String> ID_FIELD = Collections.unmodifiableList(Lists.newArrayList(IObjectData.ID));

    public AbstractCalculator(DescribePackageService describePackageService,
                              ExpressionCalculateLogicService expressionCalculateLogicService,
                              MetaDataService metaDataService,
                              IObjectDataService objectDataService,
                              QuoteValueService quoteValueService,
                              DataPackageService dataPackageService,
                              CalculateEventService calculateEventService,
                              SendMessageService sendMessageService,
                              CalculateDataChangeMessage dataChangeMessage,
                              Map<String, List<IObjectData>> dependentDataMap) {
        this.describePackageService = describePackageService;
        this.expressionCalculateLogicService = expressionCalculateLogicService;
        this.metaDataService = metaDataService;
        this.objectDataService = objectDataService;
        this.quoteValueService = quoteValueService;
        this.dataPackageService = dataPackageService;
        this.calculateEventService = calculateEventService;
        this.sendMessageService = sendMessageService;
        this.dataChangeMessage = dataChangeMessage;
        this.dependentDataMap = dependentDataMap;
    }

    private void calculateCountField(User user, IObjectDescribe masterDescribe, List<IObjectData> masterDataList, List<CalculateNode> countNodeList) {
        List<IObjectData> dataList = CollectionUtils.nullToEmpty(masterDataList).stream().filter(x -> !ObjectDataExt.of(x).isInvalid()).collect(Collectors.toList());
        if (CollectionUtils.empty(dataList) || CollectionUtils.empty(countNodeList)) {
            AuditLogUtil.sendPaaSCalculateLog(dataChangeMessage, Collections.emptyList(), CalculateNodeGroup.of(countNodeList).getFieldNames(),
                    COUNT_TYPE, masterDescribe.getApiName(), Modules.TREATMENT);
            return;
        }
        //写入聚合框架
        if (CalculateConfig.isFormulaGrayDispatcher(user.getTenantId())) {
            List<String> dataIdList = dataList.stream().map(IObjectData::getId).collect(Collectors.toList());
            List<CalculateDataEvent> events = CalculateNodeGroup.of(countNodeList).toEvents(dataChangeMessage, dataIdList);
            calculateEventService.upsertEvents(events);
            AuditLogUtil.sendPaaSCalculateLog(dataChangeMessage, dataIdList, CalculateNodeGroup.of(countNodeList).getFieldNames(),
                    COUNT_TYPE, masterDescribe.getApiName(), Modules.TREATMENT);
            return;
        }

        List<String> countFieldNameList = countNodeList.stream().map(x -> x.getFieldApiName()).collect(Collectors.toList());
        //更新统计字段数据
        List<Count> countList = ObjectDescribeExt.of(masterDescribe).getFieldByApiNames(countFieldNameList).stream()
                .filter(IFieldDescribe::isActive)
                .map(x -> (Count) x)
                .collect(Collectors.toList());
        Map<String, List<Count>> countGroup = countList.stream().collect(Collectors.groupingBy(x -> x.getSubObjectDescribeApiName()));
        Map<String, IObjectDescribe> detailDescribeMap = describePackageService.fetchObjects(user.getTenantId(), Lists.newArrayList(countGroup.keySet()));

        IActionContext actionContext = buildContext(user, IFieldType.COUNT);
        countGroup.forEach((detailApiName, countFields) -> {
            List<String> fieldApiNames = countFields.stream().map(Count::getApiName).collect(Collectors.toList());
            List<String> countDataIds = dataList.stream().map(IObjectData::getId).collect(Collectors.toList());
            IObjectDescribe detailDescribe = detailDescribeMap.get(detailApiName);
            if (detailDescribe == null) {
                log.warn("detail describe not exists:{},{},{}.{}", user.getTenantId(), detailApiName,
                        masterDescribe.getApiName(), countFields.get(0).getApiName());
                AuditLogUtil.sendPaaSCalculateLog(dataChangeMessage, countDataIds, fieldApiNames, COUNT_TYPE, masterDescribe.getApiName());
                return;
            }
            metaDataService.calculateAndUpdateMasterCountFieldsWithData(actionContext, masterDescribe, dataList, detailDescribe, countFields);
            AuditLogUtil.sendPaaSCalculateLog(dataChangeMessage, countDataIds, fieldApiNames, COUNT_TYPE, masterDescribe.getApiName());
        });
    }

    //相关对象或从对象 计算字段
    private void calculateRelatedFormulaField(User user, CalculateNodeGroup nodeGroup, String dataId, List<IObjectData> dataList) {
        String filterId = dataId;
        //引用人员字段的计算字段，需要将人员数据的id转成userId再查询相关对象数据
        if (nodeGroup.getRelateType() == RelateType.R2P) {
            Optional<IObjectData> data = dataList.stream().filter(x -> dataId.equals(x.getId())).findFirst();
            if (!data.isPresent()) {
                AuditLogUtil.sendPaaSCalculateLog(dataChangeMessage, Collections.emptyList(), nodeGroup.getFieldNames(),
                        FORMULA_TYPE, nodeGroup.getObjectApiName(), Modules.TREATMENT);
                return;
            }
            filterId = String.valueOf(data.get().get(ObjectDataExt.USER_ID));
        }

        String idOffset = null;
        int batchDataNum;
        String detailApiName = nodeGroup.getObjectApiName();
        IObjectDescribe detailDescribe = getDescribeOrEmptyIfGrayDispatcher(user.getTenantId(), detailApiName);
        int defaultBatchSize = CalculateConfig.getQueryRelateDataBatchSize();
        int queryBatchSize;
        do {
            // 构建 searchQuery
            SearchTemplateQueryExt searchQuery;
            if (StringUtils.isBlank(idOffset)) {
                queryBatchSize = defaultBatchSize + 1;
                searchQuery = buildSearchQuery(filterId, nodeGroup, idOffset, queryBatchSize);
            } else {
                queryBatchSize = defaultBatchSize;
                searchQuery = buildSearchQuery(filterId, nodeGroup, idOffset, queryBatchSize);
            }
            if (Objects.isNull(searchQuery)) {
                AuditLogUtil.sendPaaSCalculateLog(dataChangeMessage, Collections.emptyList(), nodeGroup.getFieldNames(),
                        FORMULA_TYPE, detailApiName, Modules.TREATMENT);
                break;
            }

            // 查询数据
            QueryResult<IObjectData> queryResult;
            if (CalculateConfig.isFormulaGrayDispatcher(user.getTenantId())) {
                queryResult = dataPackageService.findBySearchQueryWithFieldsIgnoreAll(user, detailApiName,
                        (SearchTemplateQuery) searchQuery.getQuery(), ID_FIELD);
            } else {
                queryResult = dataPackageService.findBySearchQueryIgnoreAll(user, detailApiName,
                        (SearchTemplateQuery) searchQuery.getQuery());
            }
            List<IObjectData> queryData = queryResult.getData();
            if (CollectionUtils.notEmpty(queryData)) {
                batchDataNum = queryData.size();
                // 如果查询到的数据大于batchSize，或者有idOffset(不是第一次查询)，并且sendMessageService不为空，并且不是从optool发送的消息，
                // 并且灰度了校验相关对象数据条数，则需要重新发送消息到optool队列，否则直接计算
                if ((batchDataNum > defaultBatchSize || StringUtils.isNotBlank(idOffset))
                        && Objects.nonNull(sendMessageService)
                        && !dataChangeMessage.isFromOpTool()
                        && UdobjGrayConfig.isAllow("enableRelateDataCountCheck", user.getTenantId())) {
                    log.warn("batchDataNum:{} over limit:{},resend msg to optool queue,tenantId:{},dataId:{},nodeGroup:{}",
                            batchDataNum, defaultBatchSize, user.getTenantId(), dataId, nodeGroup);
                    CalculateDataChangeMessage newMessage = CalculateDataChangeMessage.builder()
                            .eventId(dataChangeMessage.getEventId())
                            .tenantId(user.getTenantId())
                            .op(dataChangeMessage.getOp())
                            .describeApiName(dataChangeMessage.getDescribeApiName())
                            .dataIdList(dataChangeMessage.getDataIdList())
                            .calculateNodes(nodeGroup.getCalculateNodes())
                            .originalMessageId(dataChangeMessage.getOriginalMessageId())
                            .originalBornTimestamp(dataChangeMessage.getOriginalBornTimestamp())
                            .build();
                    MessageConvert.builder()
                            .dataChangeMessage(newMessage)
                            .fromOpTool(true)
                            .isDelayed(false)
                            .sendMessageService(sendMessageService)
                            .tenantId(user.getTenantId())
                            .type(MessageConvert.FORMULA_TYPE)
                            .originalObjectIds(dataChangeMessage.getOriginalObjectIds())
                            .originalDescribeApiName(dataChangeMessage.getOriginalDescribeApiName())
                            .build()
                            .convertAndSendMessage();
                    AuditLogUtil.sendPaaSCalculateLog(dataChangeMessage, Collections.emptyList(), nodeGroup.getFieldNames(),
                            FORMULA_TYPE, detailApiName, Modules.RESEND);
                    break;
                }
                idOffset = queryData.get(batchDataNum - 1).getId();
                // 计算并更新数据
                calculateSelfFormulaField(user, queryData, detailDescribe, nodeGroup, false, false);
            } else {
                batchDataNum = 0;
                AuditLogUtil.sendPaaSCalculateLog(dataChangeMessage, Collections.emptyList(),
                        nodeGroup.getFieldNames(), FORMULA_TYPE, detailApiName, Modules.TREATMENT);
            }
        } while (StringUtils.isNotBlank(idOffset) && batchDataNum >= queryBatchSize);
    }

    private SearchTemplateQueryExt buildSearchQuery(String masterDataId, CalculateNodeGroup nodeGroup, String idOffset, int batchSize) {
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        //不需要引用字段
        queryExt.setNeedReturnQuote(false);
        //不需要数据总数
        queryExt.setNeedReturnCountNum(false);
        queryExt.setOffset(0);
        queryExt.setLimit(batchSize);
        queryExt.resetOrderBy(Lists.newArrayList(new OrderBy(IObjectData.ID, false)));

        // 通过id设置条件来分页
        if (StringUtils.isNotBlank(idOffset)) {
            queryExt.addFilter(Operator.LT, IObjectData.ID, idOffset);
        }

        List<String> lookupNames = getLookupNames(nodeGroup);
        if (CollectionUtils.empty(lookupNames)) {
            return null;
        }

        List<Wheres> wheres = lookupNames.stream()
                .map(fieldApiName -> {
                    Wheres where = new Wheres();
                    IFilter filter = FilterExt.of(Operator.EQ, fieldApiName, masterDataId).getFilter();
                    where.setFilters(Lists.newArrayList(filter));
                    return where;
                })
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(wheres)) {
            queryExt.setWheres(wheres);
        }
        return queryExt;
    }

    private SearchTemplateQueryExt buildCountQuery(String masterDataId, CalculateNodeGroup nodeGroup) {
        List<String> lookupNames = getLookupNames(nodeGroup);
        if (CollectionUtils.empty(lookupNames)) {
            return null;
        }
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        //不需要引用字段
        queryExt.setNeedReturnQuote(false);
        List<Wheres> wheres = lookupNames.stream()
                .map(fieldApiName -> {
                    Wheres where = new Wheres();
                    IFilter filter = FilterExt.of(Operator.EQ, fieldApiName, masterDataId).getFilter();
                    where.setFilters(Lists.newArrayList(filter));
                    return where;
                })
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(wheres)) {
            queryExt.setWheres(wheres);
        }
        return queryExt;
    }

    private List<String> getLookupNames(CalculateNodeGroup nodeGroup) {
        List<String> lookupNames = nodeGroup.getCalculateNodes().stream()
                .map(CalculateNode::getReferenceFieldName)
                .filter(it -> !Strings.isNullOrEmpty(it))
                .distinct()
                .collect(Collectors.toList());
        return lookupNames;
    }

    protected void calculateSelfFormulaField(User user, List<IObjectData> objectDataList, IObjectDescribe objectDescribe,
                                             CalculateNodeGroup nodeGroup, boolean isCache, boolean diffCalculateResult) {
        objectDataList = CollectionUtils.nullToEmpty(objectDataList).stream().filter(x -> !ObjectDataExt.of(x).isInvalid()).collect(Collectors.toList());
        if (CollectionUtils.empty(objectDataList) || nodeGroup.isEmpty()) {
            AuditLogUtil.sendPaaSCalculateLog(dataChangeMessage, Collections.emptyList(), nodeGroup.getFieldNames(),
                    FORMULA_TYPE, nodeGroup.getObjectApiName(), Modules.TREATMENT);
            return;
        }
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + ".bulkCalculateAndUpdate");
        try {
            //写入聚合框架
            if (CalculateConfig.isFormulaGrayDispatcher(user.getTenantId())) {
                List<String> dataIdList = objectDataList.stream().map(IObjectData::getId).collect(Collectors.toList());
                List<CalculateDataEvent> events = nodeGroup.toEvents(dataChangeMessage, dataIdList);
                calculateEventService.upsertEvents(events);
                stopWatch.lap("upsertEvents");
                AuditLogUtil.sendPaaSCalculateLog(dataChangeMessage, dataIdList, nodeGroup.getFieldNames(),
                        FORMULA_TYPE, nodeGroup.getObjectApiName(), Modules.TREATMENT);
                return;
            }

            List<String> dataIds = objectDataList.stream().map(IObjectData::getId).collect(Collectors.toList());
            List<IObjectData> beforeDataList = ObjectDataExt.copyList(objectDataList);
            // 处理调用计算接口 处理计算和引用字段
            List<String> fieldNames = calculateData(user, objectDataList, objectDescribe, nodeGroup);
            stopWatch.lap("calculateData");
            if (CollectionUtils.empty(fieldNames)) {
                AuditLogUtil.sendPaaSCalculateLog(dataChangeMessage, dataIds, fieldNames, FORMULA_TYPE, nodeGroup.getObjectApiName());
                return;
            }
            if (isCache) {
                cacheObjectData(objectDescribe.getApiName(), objectDataList);
                stopWatch.lap("cacheObjectData");
            }
            // 获取变更的字段
            List<String> changedFieldNames = getChangedFieldNames(objectDataList, objectDescribe, diffCalculateResult, fieldNames, beforeDataList);
            stopWatch.lap("changedFieldNames");
            // 更新数据
            IActionContext actionContext = buildContext(user, nodeGroup.fieldType());
            metaDataService.batchUpdateWithFieldsForCalculateToPG(actionContext, objectDataList, changedFieldNames);
            stopWatch.lap("batchUpdateWithFieldsForCalculateToPG");
            log.debug("batchUpdateWithFieldsForCalculateToPG objectDataList:{},fieldNames:{}", objectDataList, fieldNames);
            AuditLogUtil.sendPaaSCalculateLog(dataChangeMessage, dataIds, fieldNames, FORMULA_TYPE, nodeGroup.getObjectApiName());
        } finally {
            stopWatch.logSlow(500);
        }
    }

    private List<String> calculateData(User user, List<IObjectData> objectDataList, IObjectDescribe objectDescribe, CalculateNodeGroup nodeGroup) {
        // 处理引用字段
        List<String> fieldNames = nodeGroup.getFieldNames();
        if (nodeGroup.isQuoteField()) {
            List<Quote> quoteFields = getCalculateField(objectDescribe, Quote.class, fieldNames);
            if (CollectionUtils.empty(quoteFields)) {
                return Collections.emptyList();
            }
            quoteValueService.fillQuoteFieldValue(user, objectDataList, objectDescribe, null, true, quoteFields, null, true);
            return fieldNames;
        }
        // 处理计算字段
        List<IFieldDescribe> calculateField = getCalculateField(objectDescribe, IFieldDescribe.class, fieldNames);
        if (CollectionUtils.empty(calculateField)) {
            return Collections.emptyList();
        }
        expressionCalculateLogicService.bulkCalculateWithDependentDataAndDescribe(objectDescribe, objectDataList, calculateField, dependentDataMap, true, describeMap);
        return fieldNames;
    }

    private List<String> getChangedFieldNames(List<IObjectData> objectDataList, IObjectDescribe objectDescribe,
                                              boolean diffCalculateResult, List<String> fieldNames, List<IObjectData> beforeDataList) {
        if (diffCalculateResult) {
            return IntStream.range(0, objectDataList.size())
                    .mapToObj(i -> {
                        Map<String, Object> beforeData = ObjectDataExt.of(beforeDataList.get(i)).toMap();
                        Map<String, Object> afterData = ObjectDataExt.of(objectDataList.get(i)).toMap();
                        return CustomDataChangeMessage.diffDataFieldChanges(objectDescribe, Sets.newHashSet(fieldNames), beforeData, afterData);
                    })
                    .flatMap(Collection::stream)
                    .distinct()
                    .collect(Collectors.toList());
        }
        return fieldNames;
    }

    private <T extends IFieldDescribe> List<T> getCalculateField(IObjectDescribe objectDescribe,
                                                                 Class<T> clazz, List<String> fieldNames) {

        return ObjectDescribeExt.of(objectDescribe).getFieldByApiNames(fieldNames)
                .stream()
                .filter(IFieldDescribe::isActive)
                .filter(x -> BooleanUtils.isNotTrue(x.isAbstract()))
                .filter(clazz::isInstance)
                .map(x -> (T) x)
                .collect(Collectors.toList());
    }

    protected void cacheObjectData(String apiName, List<IObjectData> dataList) {
        List<IObjectData> objectDataList = dependentDataMap.get(apiName);
        if (CollectionUtils.notEmpty(objectDataList)) {
            List<IObjectData> resultDataList = Lists.newArrayList(objectDataList);
            objectDataList.forEach(x -> {
                dataList.forEach(y -> {
                    if (Objects.equals(x.getId(), y.getId())) {
                        resultDataList.remove(x);
                        resultDataList.add(y);
                    }
                });
            });
            objectDataList = resultDataList;
        } else {
            objectDataList = Lists.newArrayList();
            objectDataList.addAll(dataList);
        }
        dependentDataMap.put(apiName, objectDataList);
    }

    private IObjectDescribe getDescribeOrEmptyIfGrayDispatcher(String tenantId, String describeApiName) {
        if (CalculateConfig.isFormulaGrayDispatcher(tenantId)) {
            IObjectDescribe describe = new ObjectDescribe();
            describe.setTenantId(tenantId);
            describe.setApiName(describeApiName);
            return describe;
        }
        return getDescribe(tenantId, describeApiName);
    }

    private IObjectDescribe getDescribe(String tenantId, String describeApiName) {
        return describePackageService.fetchObject(tenantId, describeApiName);
    }

    private List<IObjectData> findObjectDataListOrEmptyIfGrayDispatcher(String tenantId, String describeApiName, List<String> dataIds,
                                                                        ObjectAction action, boolean hasR2PCalculateNode) {
        if (!hasR2PCalculateNode && CalculateConfig.isFormulaGrayDispatcher(tenantId)) {
            return dataIds.stream().map(x -> {
                IObjectData data = new ObjectData();
                data.setTenantId(tenantId);
                data.setDescribeApiName(describeApiName);
                data.setId(x);
                return data;
            }).collect(Collectors.toList());
        } else {
            return findObjectDataList(tenantId, describeApiName, dataIds, action);
        }
    }

    protected List<IObjectData> findObjectDataList(String tenantId, String describeApiName, List<String> dataIds, ObjectAction action) {
        List<IObjectData> dataList;
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        if (ObjectAction.INVALID.equals(action)) {
            //作废的消息需要查询包含已删除的数据，因为作废之后直接删除或者不作废直接删除的话，数据状态是已删除的。
            dataList = dataPackageService.findObjectDataByIdsIncludeDeletedIgnoreAll(user, dataIds, describeApiName);
        } else {
            dataList = dataPackageService.findObjectDataByIdsIgnoreAll(tenantId, dataIds, describeApiName);
        }
        return dataList;
    }

    protected void computeReferredFields(User user, List<String> dataIdList, String objectApiName,
                                         List<CalculateNode> calculateNodes, ObjectAction action) {
        if (CollectionUtils.empty(dataIdList) || CollectionUtils.empty(calculateNodes)) {
            return;
        }
        log.debug("computeReferredFields start tenantId:{}, objectApiName:{}, calculateNodes:{}, actionType:{}, dataId:{}",
                user.getTenantId(), objectApiName, calculateNodes, action.getActionCode(), dataIdList);
        List<CalculateNodeGroup> classifyFields = Lists.newArrayList();

        // 1. 本对象的计算字段
        Optional<CalculateNodeGroup> calculateNodeGroup = CalculateNodeExt.s2sFormulaNodes(calculateNodes, objectApiName);
        calculateNodeGroup.ifPresent(s2sFormulaNodes -> {
            classifyFields.add(s2sFormulaNodes);
            calculateNodes.removeAll(s2sFormulaNodes.getCalculateNodes());
            log.debug("computeReferredFields start tenantId:{},objectApiName:{},s2sFormulaNodes:{},actionType:{},dataId:{}", user.getTenantId(),
                    objectApiName, s2sFormulaNodes, action.getActionCode(), dataIdList);
        });

        // 2. 带计算字段，排序并分组
        List<CalculateNodeGroup> otherClassifyFields = CalculateNodeExt.sortAndClassifyNodeList(calculateNodes);
        if (CollectionUtils.notEmpty(otherClassifyFields)) {
            classifyFields.addAll(otherClassifyFields);
        }
        log.debug("computeReferredFields start tenantId:{},objectApiName:{},classifyFields:{},actionType:{},dataId:{}", user.getTenantId(),
                objectApiName, classifyFields, action.getActionCode(), dataIdList);

        boolean hasR2PCalculateNode = classifyFields.stream().filter(x -> RelateType.R2P.equals(x.getRelateType())).findFirst().isPresent();
        IObjectDescribe objectDescribe = getDescribeOrEmptyIfGrayDispatcher(user.getTenantId(), objectApiName);
        List<IObjectData> objectDataList = findObjectDataListOrEmptyIfGrayDispatcher(user.getTenantId(), objectApiName,
                dataIdList, action, hasR2PCalculateNode);

        classifyFields.forEach(nodeGroup -> {
            // 处理统计字段
            if (nodeGroup.isCountField()) {
                //主对象或lookup对象的统计字段;
                calculateCountField(user, objectDescribe, objectDataList, nodeGroup.getCalculateNodes());
                return;
            }
            // 处理本对象的计算字段
            if (nodeGroup.getRelateType() == RelateType.S2S) {
                //boolean diffCalculateResult = dataChangeMessage != null;
                calculateSelfFormulaField(user, objectDataList, objectDescribe, nodeGroup, true, false);
                return;
            }
            // 处理相关对象、从对象的计算字段，按关联字段分组再计算
            List<CalculateNodeGroup> groupingGroups = nodeGroup.groupingByReferenceField();
            groupingGroups.forEach(groupingGroup -> dataIdList.forEach(dataId -> calculateRelatedFormulaField(user, groupingGroup, dataId, objectDataList)));
        });
    }

    private IActionContext buildContext(User user, String fieldType) {
        String source = QueueTypes.buildSource(dataChangeMessage.queueType(), fieldType);
        return ActionContextExt.of(user)
                .setEventId(dataChangeMessage.getEventId())
                .setBatch(true)
                .disableDeepQuote()
                .skipRelevantTeam()
                .doNotDeepCopyDescribe()
                .doSkipConfig()
                .set(ActionContextKey.OBJECT_DATA_SOURCE, source)
                .getContext();
    }
}
