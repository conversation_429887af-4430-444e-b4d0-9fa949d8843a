package com.facishare.paas.task.calculate.util;

import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageQueue;

import java.util.List;
import java.util.function.Function;

/**
 * Created by liwei on 2019/12/20
 */
@Slf4j
public class AppDefaultRocketMQProducer {
    private String config;
    private AutoConfMQProducer mqProducer;
    private String topic;

    public AppDefaultRocketMQProducer(String config) {
        this.config = config;
    }

    public void init() {
        mqProducer = new AutoConfMQProducer(config);
        listenConf();
    }

    private void listenConf() {
        ConfigFactory.getConfig(config, this::reload);
    }

    private void reload(IConfig config) {
        topic = config.get("producer.default.topic");
    }

    public void close() {
        if (mqProducer != null) {
            mqProducer.close();
        }
    }

    public SendResult sendMessage(Message message, Function<List<MessageQueue>, MessageQueue> function) {
        try {
            SendResult result = mqProducer.send(message, (mqs, msg, arg) -> function.apply(mqs), "");
            log.debug("sendMessage OK,topic:{}, status:{}, messageId:{}", message.getTopic(), result.getSendStatus(), result.getMsgId());
            return result;
        } catch (Exception e) {
            log.error("sendMessage failed!, message:{}", message);
            throw e;
        }
    }
}
