package com.facishare.paas.task.calculate.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.common.mq.RocketMQException;
import com.facishare.paas.appframework.common.mq.RocketMQMessageListener;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.remoting.protocol.heartbeat.MessageModel;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * RocketMQ 消息处理器
 * <p>
 * 添加部分参数  by lw
 */
@Slf4j
public class RocketMQMessageProcessor {

    private RocketMQMessageListener rocketMQMessageListener;
    private volatile boolean shutdown = true;
    private volatile DefaultMQPushConsumer defaultMQPushConsumer;
    private volatile Config config;
    private String configName;
    private String configKey;
    private String baseKey = "base_config_value";

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public void setConfigKey(String configKey) {
        this.configKey = configKey;
    }

    public void setRocketMQMessageListener(RocketMQMessageListener rocketMQMessageListener) {
        this.rocketMQMessageListener = rocketMQMessageListener;
    }

    @PostConstruct
    public void init() {
        ConfigFactory.getInstance().getConfig(configName, config -> {
            try {
                reload(config);
            } catch (Exception e) {
                throw new RocketMQException(SystemErrorCode.MQ_INIT_ERROR, e);
            }
        });
    }

    private void reload(IConfig conf) {
        String content = new String(conf.getContent());
        if (Strings.isNullOrEmpty(content)) {
            log.error("{} config content is empty", configName);
            throw new RocketMQException("config error! key=" + configName);
        }
        if (StringUtils.isNotEmpty(this.configKey)) {
            loadConsumer(content);
        } else {
            this.config = JSON.parseObject(content, Config.class);
        }
        log.info("reload config:{}", config);

        if (defaultMQPushConsumer == null) {
            createConsumer(config);
        } else {
            shutdown();
            createConsumer(config);
        }
    }

    private void createConsumer(Config config) {
        try {
            defaultMQPushConsumer = new DefaultMQPushConsumer(config.getConsumeGroup());
            defaultMQPushConsumer.setNamesrvAddr(config.getNameServer());
            defaultMQPushConsumer.subscribe(config.getTopic(), config.getTags());
            defaultMQPushConsumer.setInstanceName(UUID.randomUUID().toString());
            defaultMQPushConsumer.setConsumeFromWhere(config.getConsumeFromWhere());
            defaultMQPushConsumer.setConsumeMessageBatchMaxSize(config.getConsumeBatchSize());
            defaultMQPushConsumer.setConsumeThreadMin(config.getConsumeThreadMin());
            defaultMQPushConsumer.setConsumeThreadMax(config.getConsumeThreadMax());
            defaultMQPushConsumer.setPullInterval(config.getPullInterval());

            if (StringUtils.isNotEmpty(config.getConsumeTimestamp())) {
                defaultMQPushConsumer.setConsumeTimestamp(config.getConsumeTimestamp());
            }

            if (config.messageModel == 1) {
                defaultMQPushConsumer.setMessageModel(MessageModel.BROADCASTING);
            }

            if (config.isOrdered()) {
                registerOrderedMessageListener();
            } else {
                registerConcurrentMessageListener();
            }
            defaultMQPushConsumer.start();
            shutdown = false;
        } catch (Exception e) {
            log.error("create RocketMQ defaultMQPushConsumer failed!", e);
        }

    }

    private void registerOrderedMessageListener() {
        defaultMQPushConsumer.registerMessageListener((MessageListenerOrderly) (messages, context) -> {

            if (shutdown) {
                log.warn("consumer is shutdown,suspend queue a moment!");
                return ConsumeOrderlyStatus.SUSPEND_CURRENT_QUEUE_A_MOMENT;
            }

            try {
                rocketMQMessageListener.consumeMessage(messages);
                return ConsumeOrderlyStatus.SUCCESS;
            } catch (Exception e) {
                log.error("consume message failed!", e);
            }
            return ConsumeOrderlyStatus.SUSPEND_CURRENT_QUEUE_A_MOMENT;
        });
    }

    private void registerConcurrentMessageListener() {
        defaultMQPushConsumer.registerMessageListener((MessageListenerConcurrently) (messages, context) -> {
            if (shutdown) {
                log.warn("consumer is shutdown,reConsume later!");
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }

            try {
                rocketMQMessageListener.consumeMessage(messages);
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("consume message failed!", e);
            }
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        });
    }

    private void shutdown() {
        shutdown = true;
        if (defaultMQPushConsumer != null) {
            try {
                this.defaultMQPushConsumer.shutdown();
                this.defaultMQPushConsumer = null;
            } catch (Exception e) {
                log.error("shutdown RocketMQ consumer failed!", e);
            }
        }
    }

    private void loadConsumer(String content){
        JSONObject jsonObject = JSON.parseObject(content);
        JSONObject baseValue = jsonObject.getJSONObject(this.baseKey);
        JSONObject value = jsonObject.getJSONObject(this.configKey);
        this.config = JSONObject.toJavaObject(baseValue, Config.class);
        if(baseValue == null){
            this.config = JSONObject.toJavaObject(value, Config.class);
            return;
        }
        Map map = JSONObject.toJavaObject(value, Map.class);
        Class configClass = config.getClass();
        Field[] declaredFields = configClass.getDeclaredFields();
        for(Field field:declaredFields){
            try {
                if (map.keySet().contains(field.getName())) {
                    field.set(config,map.get(field.getName()));
                }
            } catch (IllegalAccessException e) {
                log.error("loadConsumer error:",e);
            }
        }
    }

    @Data
    @Slf4j
    public static class Config {
        String nameServer;
        String consumeGroup;    //这个值决定了消费集群
        String topic;           //一个Processor 处理一个Topic
        List<String> tags;      //消费那些Tag
        int consumeBatchSize;   //每次消费的消息的数量
        String consumeFromWhere;
        String consumeTimestamp;
        boolean ordered = false;
        int consumeThreadMin;
        int consumeThreadMax;
        int messageModel;
        long pullInterval;

        public String getConsumeGroup() {
            return consumeGroup;
        }

        public int getConsumeBatchSize() {
            if (consumeBatchSize > 0) {
                return consumeBatchSize;
            }
            return 1;
        }

        public ConsumeFromWhere getConsumeFromWhere() {
            try {
                ConsumeFromWhere from = ConsumeFromWhere.valueOf(consumeFromWhere);
                return from;
            } catch (Exception e) {
                log.warn("Unsupported ConsumeFromWhere enum:" + consumeFromWhere);
            }
            return ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET;
        }

        public String getTags() {
            if (CollectionUtils.notEmpty(tags)) {
                return Joiner.on("||").skipNulls().join(tags);
            }
            return "*";
        }

        public int getConsumeThreadMin() {
            if (consumeThreadMin > 0) {
                return consumeThreadMin;
            }
            return 1;
        }

        public int getConsumeThreadMax() {
            if (consumeThreadMax > 0) {
                return consumeThreadMax;
            }
            return 5;
        }

        public long getPullInterval() {
            return this.pullInterval;
        }

        public String getConsumeTimestamp() {
            return consumeTimestamp;
        }

        @Override
        public String toString() {
            return "Config{" +
                    "nameServer='" + nameServer + '\'' +
                    ", consumeGroup='" + consumeGroup + '\'' +
                    ", topic='" + topic + '\'' +
                    ", tags=" + tags +
                    ", consumeBatchSize=" + consumeBatchSize +
                    ", consumeFromWhere='" + consumeFromWhere + '\'' +
                    ", ordered=" + ordered +
                    ", consumeThreadMin=" + consumeThreadMin +
                    ", consumeThreadMax=" + consumeThreadMax +
                    ", messageModel=" + messageModel +
                    ", pullInterval=" + pullInterval +
                    '}';
        }
    }

}
