package com.facishare.paas.task.calculate.util;

import lombok.Data;
import org.elasticsearch.common.Strings;

import java.util.List;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.stream.IntStream;

/**
 * create by <PERSON><PERSON><PERSON> on 2020/11/05
 */
public class ConsistentHashing {
    private final SortedMap<Long, Node> hashRing = new TreeMap<>();
    /**
     * 虚拟节点个数
     */
    private final int NUM_REPS = 100;
    /**
     * hash 算法
     */
    private final HashAlgorithm hashAlg;

    private ConsistentHashing(List<Node> nodes, HashAlgorithm hashAlg) {
        this.hashAlg = hashAlg;
        build(nodes, hashAlg);
    }

    private void build(List<Node> nodes, HashAlgorithm hashAlg) {
        nodes.forEach(node -> IntStream.range(0, NUM_REPS)
                .mapToLong(it -> {
                    String key = node.getVirtualNode(it);
                    return hashAlg.hash(key);
                })
                .forEach(key -> hashRing.put(key, node)));
    }

    public Node getNodeByKey(String key) {
        if (Strings.isNullOrEmpty(key)) {
            return null;
        }
        Long hashCode = hashAlg.hash(key);
        SortedMap<Long, Node> tailMap = hashRing.tailMap(hashCode);
        if (tailMap.isEmpty()) {
            return hashRing.get(hashRing.firstKey());
        }
        return hashRing.get(tailMap.firstKey());
    }


    @Data
    public static class Node {
        private String key;

        private String getVirtualNode(int num) {
            return String.format("%s_%s", key, num);
        }
    }
}
