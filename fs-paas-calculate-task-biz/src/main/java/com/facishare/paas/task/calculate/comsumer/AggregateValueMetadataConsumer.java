package com.facishare.paas.task.calculate.comsumer;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.LocalDateUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.task.calculate.config.CalculateTaskApplication;
import com.facishare.paas.task.calculate.model.AggregateCalculateNode;
import com.facishare.paas.task.calculate.model.AggregateValueCalculateMessage;
import com.facishare.paas.task.calculate.model.CustomDataChangeMessage;
import com.facishare.paas.task.calculate.model.CustomDataChangeMessage.DataContent;
import com.facishare.paas.task.calculate.service.AggregateValueService;
import com.facishare.paas.task.calculate.service.DataPackageService;
import com.facishare.paas.task.calculate.service.DescribePackageService;
import com.facishare.paas.task.calculate.service.SendMessageService;
import com.facishare.paas.task.calculate.util.MessageConvert;
import com.facishare.paas.task.calculate.util.MessageSupport;
import com.facishare.paas.task.calculate.util.TopicConstant;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.rocketmq.common.message.Message;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class AggregateValueMetadataConsumer implements Consumer<CustomDataChangeMessage> {

    @Autowired
    private AggregateValueService aggregateValueService;
    @Autowired
    private DescribePackageService describePackageService;
    @Autowired
    private DataPackageService dataPackageService;
    @Autowired
    private SendMessageService sendMessageService;

    @Override
    public void execute(CustomDataChangeMessage message) {
        String actionType = message.getOp();
        switch (actionType) {
            case "u":
                updateDispatch(message);
                break;
            case "i":
            case "recover":
            case "invalid":
            case "d":
                insertDispatch(message);
                break;
            default:
                break;
        }
    }

    private void updateDispatch(CustomDataChangeMessage message) {
        String tenantId = message.getTenantId();
        List<AggregateRule> aggregateRuleList = aggregateValueService.findAllAggregateRules(tenantId);
        if (CollectionUtils.empty(aggregateRuleList)) {
            return;
        }
        List<DataContent> body = message.getBody();
        String objectApiName = body.get(0).getEntityId();
        IObjectDescribe objectDescribe = describePackageService.fetchObject(tenantId, objectApiName);
        for (AggregateRule aggregateRule : aggregateRuleList) {
            IObjectDescribe aggregateObject = describePackageService.fetchObject(tenantId, aggregateRule.getAggregateObject());
            Map<String, Set<String>> relateFieldsMap = aggregateRule.getRelateFieldsMap(aggregateObject);
            if (!relateFieldsMap.containsKey(objectApiName)) {
                continue;
            }
            Map<String, Set<AggregateCalculateNode>> calculateNodeMap = Maps.newHashMap();
            Set<String> needQueryDataIds = Sets.newHashSet();
            List<DataContent> filterDataContentList = Lists.newArrayList();
            for (DataContent content : body) {
                String dataId = content.getObjectId();
                if (Strings.isNullOrEmpty(dataId)) {
                    continue;
                }
                //更新已作废的数据不需要处理
                if (content.isLifeStatusUpdatedByInvalid()) {
                    continue;
                }
                // 变更的字段
                List<String> fieldChanges = content.getChangeFields(objectDescribe);
                if (CollectionUtils.empty(fieldChanges)) {
                    continue;
                }
                if (fieldChanges.stream().noneMatch(x -> relateFieldsMap.get(objectApiName).contains(x))) {
                    continue;
                }
                if (objectApiName.equals(aggregateObject.getApiName())) {
                    if (fieldChanges.contains(aggregateRule.getDateField())) {
                        processByAggregateObject(aggregateRule, calculateNodeMap, content, dataId, fieldChanges);
                    } else {
                        needQueryDataIds.add(dataId);
                        filterDataContentList.add(content);
                    }
                } else {
                    processByLookup(message, objectDescribe, dataId, aggregateObject, aggregateRule);
                }
            }
            needQueryDataIds.removeAll(calculateNodeMap.keySet());
            Set<AggregateCalculateNode> calculateNodes = filterByDataIds(aggregateObject, needQueryDataIds, aggregateRule, filterDataContentList);
            calculateNodes.forEach(node -> {
                calculateNodeMap.putIfAbsent(node.getDataId(), Sets.newHashSet());
                calculateNodeMap.get(node.getDataId()).add(node);
            });
            sendCalculateMessages(message, aggregateObject, aggregateRule, null, calculateNodeMap);
        }
    }

    private void processByAggregateObject(AggregateRule aggregateRule, Map<String, Set<AggregateCalculateNode>> calculateNodeMap,
                                          DataContent content, String dataId, List<String> fieldChanges) {
        Tuple dates = content.getValue(aggregateRule.getDateField());

        if (aggregateRule.isDateFieldInRange(dates.getKey())) {
            AggregateCalculateNode calculateNode = AggregateCalculateNode.builder()
                    .dataId(dataId)
                    .aggregateDate(LocalDateUtils.truncateWithDay(dates.getKey()))
                    .build();
            if (fieldChanges.contains(aggregateRule.getDimension())) {
                Tuple dimensions = content.getValue(aggregateRule.getDimension());
                if (!ObjectDataExt.isValueEmpty(dimensions.getKey())) {
                    calculateNode.setAggregateDimension((String) dimensions.getKey());
                    calculateNodeMap.putIfAbsent(dataId, Sets.newHashSet());
                    calculateNodeMap.get(dataId).add(calculateNode);
                }
            } else {
                calculateNodeMap.putIfAbsent(dataId, Sets.newHashSet());
                calculateNodeMap.get(dataId).add(calculateNode);
            }
        }
        if (aggregateRule.isDateFieldInRange(dates.getValue())) {
            AggregateCalculateNode calculateNode = AggregateCalculateNode.builder()
                    .dataId(dataId)
                    .aggregateDate(LocalDateUtils.truncateWithDay(dates.getValue()))
                    .build();
            if (fieldChanges.contains(aggregateRule.getDimension())) {
                Tuple dimensions = content.getValue(aggregateRule.getDimension());
                if (!ObjectDataExt.isValueEmpty(dimensions.getValue())) {
                    calculateNode.setAggregateDimension((String) dimensions.getValue());
                    calculateNodeMap.putIfAbsent(dataId, Sets.newHashSet());
                    calculateNodeMap.get(dataId).add(calculateNode);
                }
            } else {
                calculateNodeMap.putIfAbsent(dataId, Sets.newHashSet());
                calculateNodeMap.get(dataId).add(calculateNode);
            }
        }
    }

    private void processByLookup(CustomDataChangeMessage message, IObjectDescribe lookupDescribe, String lookupDataId,
                                 IObjectDescribe detailDescribe, AggregateRule aggregateRule) {
        Set<String> lookupNames = aggregateRule.getLookupFields(detailDescribe, lookupDescribe.getApiName());
        if (CollectionUtils.empty(lookupNames)) {
            return;
        }
        String idOffset = null;
        int batchDataNum;
        // 构建 searchQuery
        SearchTemplateQueryExt searchQuery = buildSearchQuery(lookupDataId, lookupNames, aggregateRule);
        do {
            // 重置 id 筛选条件
            searchQuery.resetSearchTemplateQuery(idOffset);
            // 查询数据
            QueryResult<IObjectData> queryResult = dataPackageService.findBySearchQueryIgnoreAll(User.systemUser(lookupDescribe.getTenantId()),
                    detailDescribe.getApiName(), (SearchTemplateQuery) searchQuery.getQuery());
            List<IObjectData> queryData = queryResult.getData();
            if (CollectionUtils.notEmpty(queryData)) {
                batchDataNum = queryData.size();
                idOffset = queryData.get(batchDataNum - 1).getId();
                Set<String> ids = queryData.stream()
                        .map(IObjectData::getId)
                        .collect(Collectors.toSet());
                sendCalculateMessages(message, detailDescribe, aggregateRule, ids, null);
            } else {
                batchDataNum = 0;
            }
        } while (batchDataNum > 0 && idOffset != null);
    }

    private SearchTemplateQueryExt buildSearchQuery(String lookupDataId, Set<String> lookupNames, AggregateRule aggregateRule) {
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        //不需要引用字段
        queryExt.setNeedReturnQuote(false);
        //不需要数据总数
        queryExt.setNeedReturnCountNum(false);
        queryExt.setOffset(0);
        queryExt.setLimit(200);
        queryExt.resetOrderBy(Lists.newArrayList(new OrderBy(IObjectData.ID, false)));

        List<Long> dateRange = aggregateRule.getDateRangeWithTimeMillis();
        queryExt.addFilter(Operator.GTE, aggregateRule.getDateField(), dateRange.get(0).toString());
        queryExt.addFilter(Operator.LT, aggregateRule.getDateField(), String.valueOf(LocalDateUtils.plusOneDay(dateRange.get(dateRange.size() - 1))));

        List<Wheres> wheres = lookupNames.stream()
                .map(fieldApiName -> {
                    Wheres where = new Wheres();
                    IFilter filter = FilterExt.of(Operator.EQ, fieldApiName, Lists.newArrayList(lookupDataId)).getFilter();
                    where.setFilters(Lists.newArrayList(filter));
                    return where;
                })
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(wheres)) {
            queryExt.setWheres(wheres);
        }
        return queryExt;
    }

    private Set<AggregateCalculateNode> filterByDataIds(IObjectDescribe aggregateObject, Set<String> needQueryDataIds, AggregateRule aggregateRule,
                                                        List<DataContent> filterDataContentList) {
        if (CollectionUtils.empty(needQueryDataIds)) {
            return Sets.newHashSet();
        }
        Set<AggregateCalculateNode> calculateNodes = Sets.newHashSet();
        List<IObjectData> dataList = dataPackageService.findObjectDataByIdsIgnoreAll(aggregateObject.getTenantId(),
                Lists.newArrayList(needQueryDataIds), aggregateObject.getApiName());
        dataList.stream()
                .filter(x -> aggregateRule.isDateFieldInRange(x.get(aggregateRule.getDateField())))
                .forEach(x -> {
                    filterDataContentList.stream().filter(content -> x.getId().equals(content.getObjectId())).forEach(content -> {
                        List<String> fieldChanges = content.getChangeFields(aggregateObject);
                        if (fieldChanges.contains(aggregateRule.getDimension())) {
                            Tuple dimensions = content.getValue(aggregateRule.getDimension());
                            if (!ObjectDataExt.isValueEmpty(dimensions.getKey())) {
                                AggregateCalculateNode calculateNode = AggregateCalculateNode.builder()
                                        .dataId(x.getId())
                                        .aggregateDate(LocalDateUtils.truncateWithDay(x.get(aggregateRule.getDateField())))
                                        .aggregateDimension((String) dimensions.getKey())
                                        .build();
                                calculateNodes.add(calculateNode);
                            }
                            if (!ObjectDataExt.isValueEmpty(x.get(aggregateRule.getDimension()))) {
                                AggregateCalculateNode calculateNode = AggregateCalculateNode.builder()
                                        .dataId(x.getId())
                                        .aggregateDate(LocalDateUtils.truncateWithDay(x.get(aggregateRule.getDateField())))
                                        .aggregateDimension((String) x.get(aggregateRule.getDimension()))
                                        .build();
                                calculateNodes.add(calculateNode);
                            }
                        } else {
                            AggregateCalculateNode calculateNode = AggregateCalculateNode.builder()
                                    .dataId(x.getId())
                                    .build();
                            calculateNodes.add(calculateNode);
                        }
                    });
                });

        return calculateNodes;
    }

    private Set<String> filterByDataIds(IObjectDescribe aggregateObject, Set<String> needQueryDataIds, AggregateRule aggregateRule) {
        if (CollectionUtils.empty(needQueryDataIds)) {
            return Sets.newHashSet();
        }
        List<IObjectData> dataList = dataPackageService.findObjectDataByIdsIncludeDeletedIgnoreAll(User.systemUser(aggregateObject.getTenantId()),
                Lists.newArrayList(needQueryDataIds), aggregateObject.getApiName());
        return dataList.stream()
                .filter(x -> aggregateRule.isDateFieldInRange(x.get(aggregateRule.getDateField())))
                .filter(x -> aggregateRule.isDimensionInLookup() || !ObjectDataExt.isValueEmpty(x.get(aggregateRule.getDimension())))
                .map(x -> x.getId())
                .collect(Collectors.toSet());
    }

    private void sendCalculateMessages(CustomDataChangeMessage message, IObjectDescribe aggregateObject, AggregateRule aggregateRule,
                                       Set<String> calculateDataIds, Map<String, Set<AggregateCalculateNode>> calculateNodeMap) {
        if (CollectionUtils.empty(calculateDataIds) && CollectionUtils.empty(calculateNodeMap)) {
            return;
        }
        List<AggregateCalculateNode> nodeList = Lists.newArrayList();
        CollectionUtils.nullToEmpty(calculateNodeMap).forEach((k, v) -> nodeList.addAll(v));
        AggregateValueCalculateMessage calculateMessageBody = AggregateValueCalculateMessage.builder()
                .tenantId(aggregateObject.getTenantId())
                .aggregateRuleId(aggregateRule.getId())
                .dataIdList(Lists.newArrayList(CollectionUtils.nullToEmpty(calculateDataIds)))
                .nodeList(nodeList)
                .build();
        Message calculateMessage = buildCalcMessage(calculateMessageBody, message);
        sendMessageService.sendMessage(calculateMessage);
    }

    private Message buildCalcMessage(AggregateValueCalculateMessage calcMessageBody, CustomDataChangeMessage originalMessage) {
        byte[] body = JSON.toJSONString(calcMessageBody).getBytes(StandardCharsets.UTF_8);
        String topic = getTopic(originalMessage);
        Message calcMessage = new Message(topic, body);
        MessageSupport.setTenantId(calcMessage, originalMessage.getTenantId());
        MessageSupport.setOriginalMessageId(calcMessage, originalMessage.getMessageId());
        MessageSupport.setOriginalBornTimestamp(calcMessage, originalMessage.getOriginalBornTimestamp());
        MessageSupport.setType(calcMessage, MessageConvert.AGGREGATE_TYPE);
        return calcMessage;
    }

    private String getTopic(CustomDataChangeMessage originalMessage) {
        String baseTopic = TopicConstant.BASE_AGG_MANUAL_TOPIC;
        if (originalMessage.isFromOpTool()) {
            baseTopic = TopicConstant.BASE_AGG_OP_TOOL_TOPIC;
        } else if (originalMessage.isBatch()) {
            baseTopic = TopicConstant.BASE_AGG_BATCH_TOPIC;
        }
        return CalculateTaskApplication.getTopic(baseTopic, originalMessage.getTenantId());
    }

    private void insertDispatch(CustomDataChangeMessage message) {
        String tenantId = message.getTenantId();
        List<AggregateRule> aggregateRuleList = aggregateValueService.findAllAggregateRules(tenantId);
        if (CollectionUtils.empty(aggregateRuleList)) {
            return;
        }
        List<DataContent> body = message.getBody();
        String objectApiName = body.get(0).getEntityId();
        IObjectDescribe objectDescribe = describePackageService.fetchObject(tenantId, objectApiName);
        for (AggregateRule aggregateRule : aggregateRuleList) {
            IObjectDescribe aggregateObject = describePackageService.fetchObject(tenantId, aggregateRule.getAggregateObject());
            Map<String, Set<String>> relateFieldsMap = aggregateRule.getRelateFieldsMap(aggregateObject);
            if (!relateFieldsMap.containsKey(objectApiName)) {
                continue;
            }
            Set<String> calculateDataIds = Sets.newHashSet();
            Set<String> needQueryDataIds = Sets.newHashSet();
            for (DataContent content : body) {
                String dataId = content.getObjectId();
                if (Strings.isNullOrEmpty(dataId) || calculateDataIds.contains(dataId)) {
                    continue;
                }
                //作废或删除关联数据，无需处理
                if ((message.isInvalidOperation() || message.isDeleteOperation()) && !objectApiName.equals(aggregateObject.getApiName())) {
                    continue;
                }
                //直接删除(没有作废)，无需处理
                if (message.isDeleteOperation() && !content.isDirectDeleteData()) {
                    continue;
                }
                if (objectApiName.equals(aggregateObject.getApiName())) {
                    needQueryDataIds.add(dataId);
                } else {
                    processByLookup(message, objectDescribe, dataId, aggregateObject, aggregateRule);
                }
            }
            needQueryDataIds.removeAll(calculateDataIds);
            needQueryDataIds = filterByDataIds(aggregateObject, needQueryDataIds, aggregateRule);
            calculateDataIds.addAll(needQueryDataIds);
            sendCalculateMessages(message, aggregateObject, aggregateRule, calculateDataIds, null);
        }
    }


}
