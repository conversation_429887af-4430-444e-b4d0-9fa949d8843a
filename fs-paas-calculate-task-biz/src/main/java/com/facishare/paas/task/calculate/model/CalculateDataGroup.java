package com.facishare.paas.task.calculate.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.task.calculate.util.AuditLogUtil;
import com.facishare.paas.task.calculate.util.AuditLogUtil.LogInfo;
import com.facishare.paas.task.calculate.util.QueueTypes;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CalculateDataGroup {
    private String tenantId;
    private String objectApiName;
    private Set<String> fieldApiNames;
    private Set<String> dataIds;
    private String fieldType;
    private String eventId;
    private String action;
    private String queueType;
    private String queueIndex;
    private Set<String> jobIds;
    private Set<String> messageIds;
    private Long bornTimestamp;
    private long consumeTimestamp;
    private String originalObjectApiName;
    private String originalMessageId;
    private Long originalBornTimestamp;

    public boolean isBatch() {
        return !QueueTypes.MANUAL.equals(queueType);
    }

    public LogInfo toLogInfo() {
        long now = System.currentTimeMillis();
        return LogInfo.builder()
                .tenantId(tenantId)
                .objectApiName(objectApiName)
                .dataIds(Lists.newArrayList(dataIds))
                .fieldApiNames(Lists.newArrayList(fieldApiNames))
                .fieldType(fieldType)
                .action(action)
                .queueType(queueType)
                .queueIndex(queueIndex)
                .originalDescribeApiName(originalObjectApiName)
                .extraInfo(AuditLogUtil.ExtraInfo.builder()
                        .messageId(Objects.toString(messageIds, ""))
                        .bornTimestamp(bornTimestamp)
                        .consumeTimestamp(consumeTimestamp)
                        .originalMessageId(originalMessageId)
                        .originalBornTimestamp(originalBornTimestamp)
                        .totalCost(AuditLogUtil.minus(now, originalBornTimestamp))
                        .calculateCost(AuditLogUtil.minus(now, consumeTimestamp))
                        .pretreatmentCost(AuditLogUtil.minus(bornTimestamp, originalBornTimestamp))
                        .jobIds(Objects.isNull(this.jobIds) ? null : Lists.newArrayList(jobIds))
                        .build())
                .build();
    }

    public IActionContext buildContext() {
        ActionContextExt actionContextExt = ActionContextExt.of(User.systemUser(getTenantId()))
                .setBatch(true)
                .disableDeepQuote()
                .skipRelevantTeam()
                .doNotDeepCopyDescribe()
                .doSkipConfig();
        if (!Strings.isNullOrEmpty(getEventId())) {
            actionContextExt.setEventId(getEventId());
        }
        String source = QueueTypes.buildSource(queueType, fieldType);
        actionContextExt.set(ActionContextKey.OBJECT_DATA_SOURCE, source);
        return actionContextExt.getContext();
    }

    public <T extends IFieldDescribe> void retainFields(Collection<T> fields) {
        if (CollectionUtils.empty(fields)) {
            this.fieldApiNames = Sets.newHashSet();
        } else {
            this.fieldApiNames = fields.stream().map(IFieldDescribe::getApiName).collect(Collectors.toSet());
        }
    }

    public <T extends IFieldDescribe> void removeFields(Collection<T> fieldsToRemove) {
        if (CollectionUtils.empty(this.fieldApiNames) || CollectionUtils.empty(fieldsToRemove)) {
            return;
        }
        this.fieldApiNames.removeAll(fieldsToRemove.stream().map(IFieldDescribe::getApiName).collect(Collectors.toSet()));
    }

    public void addJobIds(Set<String> jobIds) {
        if (CollectionUtils.empty(jobIds)) {
            return;
        }
        if (Objects.isNull(this.jobIds)) {
            this.jobIds = Sets.newHashSet();
        }
        this.jobIds.addAll(jobIds);
    }

}
