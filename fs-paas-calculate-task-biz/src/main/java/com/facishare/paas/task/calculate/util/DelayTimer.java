package com.facishare.paas.task.calculate.util;

import com.facishare.paas.appframework.metadata.relation.FieldNode;
import com.facishare.paas.task.calculate.config.CalculateConfig;

import java.util.Objects;

public class DelayTimer {

    public static int getDelayTime(String queueType, String fieldType) {
        Integer delayTime = CalculateConfig.getDispatchDelayTime(queueType, fieldType);
        if (Objects.nonNull(delayTime)) {
            return delayTime;
        }
        //全量计算延迟2分钟，防止描述缓存更新有延迟
        if (QueueTypes.CALCULATION_JOB.equals(queueType)) {
            return 120000;
        }
        if (QueueTypes.MANUAL.equals(queueType)) {
            return FieldNode.NodeType.COUNT.name().equals(fieldType) ? 50 : 10;
        }
        return FieldNode.NodeType.COUNT.name().equals(fieldType) ? 5000 : 1000;
    }

}
