package com.facishare.paas.task.calculate.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.metadata.cache.RedisDao;
import com.facishare.paas.task.calculate.model.CalculationJobMessage;
import com.facishare.paas.task.calculate.proxy.FormulaJobScheduleService;
import com.facishare.paas.task.calculate.util.MessageSupport;
import com.google.common.base.Strings;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.Charset;
import java.util.List;

/**
 * create by z<PERSON><PERSON> on 2020/11/10
 */
public abstract class AbstractCalculationJobListener extends BaseListener<CalculationJobMessage> {

    @Autowired
    private RedisDao redisDao;

    @Autowired
    private FormulaJobScheduleService formulaJobScheduleService;

    private final static String CANCEL_ALL = "all";
    private final static String ALREADY_CANCEL_ALL = "canceled";

    @Override
    protected final CalculationJobMessage parseMessage(MessageExt message) {
        String body = new String(message.getBody(), Charset.forName("utf-8"));
        CalculationJobMessage calculateJobMQMessage = JSON.parseObject(body, CalculationJobMessage.class);
        calculateJobMQMessage.setMessageId(message.getMsgId());
        calculateJobMQMessage.setBornTimestamp(message.getBornTimestamp());
        String brokerName = MessageSupport.getBrokerName(message);
        calculateJobMQMessage.setQueueIndex(!Strings.isNullOrEmpty(brokerName) ? brokerName : String.valueOf(message.getQueueId()));
        calculateJobMQMessage.setReconsumeTimes(message.getReconsumeTimes());

        customInitMessage(calculateJobMQMessage, message);
        return calculateJobMQMessage;
    }

    protected abstract void customInitMessage(CalculationJobMessage calculateJobMQMessage, MessageExt message);

    @Override
    protected String getArgDesc(CalculationJobMessage arg) {
        return new ReflectionToStringBuilder(arg, ToStringStyle.NO_CLASS_NAME_STYLE)
                .setExcludeFieldNames("dataIdList", "userId", "createTime", "lastModifiedTime", "messageId")
                .toString();
    }

    private boolean isCanceled(CalculationJobMessage calculateJobMQMessage) {
        String key = "job_" + calculateJobMQMessage.getJobId();
        String value;
        try {
            value = redisDao.getStrCache(key);
        } catch (Exception e) {
            log.error("redis exception,cannot validate job whether canceled,msgId:{},jobId:{}",
                    calculateJobMQMessage.getMessageId(), calculateJobMQMessage.getJobId(), e);
            return false;
        }
        if (StringUtils.isEmpty(value)) {
            return false;
        }

        log.warn("consumeMessage job is canceled,msgId:{},jobId:{},value:{}", calculateJobMQMessage.getMessageId(),
                calculateJobMQMessage.getJobId(), value);
        if (StringUtils.equals(value, CANCEL_ALL)) {
            redisDao.set(key, ALREADY_CANCEL_ALL, 60 * 60);
            formulaJobScheduleService.ceaseCalculateJob(calculateJobMQMessage);
            return false;
        }

        if (StringUtils.equals(value, ALREADY_CANCEL_ALL)) {
            return true;
        }

        String jobParam = calculateJobMQMessage.getJobParam();
        List<String> changeFieldNames = JSONObject.parseArray(jobParam, String.class);
        List<String> canceledFieldNames = JSONObject.parseArray(value, String.class);
        changeFieldNames.removeAll(canceledFieldNames);
        calculateJobMQMessage.setJobParam(JSON.toJSONString(changeFieldNames));
        return false;
    }

    @Override
    protected boolean skip(CalculationJobMessage arg, MessageExt message) {
        if (isCanceled(arg)) {
            return true;
        }
        return false;
    }

    @Override
    protected String getTenantId(CalculationJobMessage arg) {
        return arg.getTenantId();
    }
}
