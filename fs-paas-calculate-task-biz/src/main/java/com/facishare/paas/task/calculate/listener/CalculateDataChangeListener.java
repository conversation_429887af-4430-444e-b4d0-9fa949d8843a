package com.facishare.paas.task.calculate.listener;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.task.calculate.comsumer.CalculateDataChangeConsumer;
import com.facishare.paas.task.calculate.model.CalculateDataChangeMessage;
import com.facishare.paas.task.calculate.util.MessageSupport;
import com.facishare.paas.task.calculate.util.TopicConstant;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.Charset;
import java.util.List;

/**
 * 监听新topic元数据消息
 */
public class CalculateDataChangeListener extends BaseListener<CalculateDataChangeMessage> {

    @Autowired
    private CalculateDataChangeConsumer calculateDataChangeConsumer;

    @Override
    public void consumeMessage(List<MessageExt> messages) {
        if (CollectionUtils.size(messages) <= 1) {
            super.consumeMessage(messages);
            return;
        }
        log.debug("messages size:{}", messages.size());
        List<MessageExt> validMessages = Lists.newArrayList(messages);
        messages.forEach(x -> {
            if (validMessages.stream().anyMatch(y ->
                    !y.getMsgId().equals(x.getMsgId()) && parseMessage(y).isSameMessage(parseMessage(x)))) {
                validMessages.removeIf(y -> y.getMsgId().equals(x.getMsgId()));
                log.warn("filter message:{}", x.getMsgId());
            }
        });
        super.consumeMessage(validMessages);
    }

    @Override
    protected CalculateDataChangeMessage parseMessage(MessageExt message) {
        String body = new String(message.getBody(), Charset.forName("utf-8"));
        CalculateDataChangeMessage dataChangeMessage = JSON.parseObject(body, CalculateDataChangeMessage.class);
        dataChangeMessage.setMessageId(message.getMsgId());
        dataChangeMessage.setBornTimestamp(message.getBornTimestamp());
        dataChangeMessage.setOriginalDescribeApiName(MessageSupport.getDescribeApiName(message));
        dataChangeMessage.setOriginalObjectIds(MessageSupport.getObjectIds(message));
        dataChangeMessage.setQueueIndex(MessageSupport.getBrokerName(message));

        dataChangeMessage.setBatch(TopicConstant.isBatch(message.getTopic()));
        dataChangeMessage.setFromOpTool(TopicConstant.isOpTool(message.getTopic()));
        if (message.getFlag() == 1) {
            dataChangeMessage.setFlag(true);
        }
        return dataChangeMessage;
    }

    @Override
    protected String getArgDesc(CalculateDataChangeMessage arg) {
        return JSON.toJSONString(arg);
    }

    @Override
    protected void doConsume(CalculateDataChangeMessage arg) {
        calculateDataChangeConsumer.execute(arg);
    }

    @Override
    protected boolean needResend(MessageExt message) {
        return calculateTaskApplication.isReSendMessage(message);
    }

    @Override
    protected String getTenantId(CalculateDataChangeMessage arg) {
        return arg.getTenantId();
    }

    @Override
    protected void fillTraceContext(CalculateDataChangeMessage arg) {
        String tenantId = arg.getTenantId();
        TraceContext traceContext = TraceContext.get();
        traceContext.setEi(tenantId);
    }
}
