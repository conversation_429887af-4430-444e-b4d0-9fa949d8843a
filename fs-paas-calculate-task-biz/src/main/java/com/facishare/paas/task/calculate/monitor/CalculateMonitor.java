package com.facishare.paas.task.calculate.monitor;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.support.GDSHandler;
import com.facishare.paas.task.calculate.config.CalculateConfig;
import com.facishare.paas.task.calculate.config.Namespaces;
import com.facishare.paas.task.calculate.model.CalculateDataEvent;
import com.facishare.paas.task.calculate.model.SendMsgBySession;
import com.facishare.paas.task.calculate.proxy.MessageProxy;
import com.fxiaoke.dispatcher.EventPorter;
import com.github.mongo.support.DatastoreExt;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Accumulators;
import com.mongodb.client.model.Aggregates;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Sorts;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * Created by zhaooju on 2023/2/7
 */
@Service
@Slf4j
public class CalculateMonitor {
    @Autowired
    private EventPorter calculateEventPorter;
    @Autowired
    private MessageProxy messageProxy;
    @Autowired
    private GDSHandler gdsHandler;

    private DatastoreExt calculateMongoStore;

    @PostConstruct
    public void init() {
        calculateMongoStore = calculateEventPorter.getDataSource(CalculateDataEvent.DEFAULT_NAMESPACE);
    }

    public void calculateOverflow() {
        long now = System.currentTimeMillis();
        for (String tenantId : CalculateConfig.getMonitorGrayTenantIds()) {
            try {
                String ea = gdsHandler.getEAByEI(tenantId);
                Set<MonitorData> monitorData = Sets.newHashSet();
                Set<String> topics = Namespaces.getAllEventTopicsByTenantId(tenantId);
                CalculateConfig.MonitorConfig monitorConfig = CalculateConfig.getMonitorConfigByTenantId(tenantId);
                topics.forEach(topic -> monitorData.addAll(findMonitorData(tenantId, topic, now, monitorConfig)));
                // 补充ea
                monitorData.forEach(it -> it.setEa(ea));
                // 获取需要发送的群组
                Set<String> sessionIds = Sets.newHashSet(monitorConfig.getSessionIds());
                sessionIds.add(CalculateConfig.getDefaultSessionId());
                logAndSendMsg(monitorData, sessionIds);
            } catch (Throwable e) {
                log.warn("calculateOverflow failed,tenantId:{}", tenantId, e);
            }
        }
    }

    private void logAndSendMsg(Set<MonitorData> monitorData, Set<String> sessionIds) {
        String msg = MonitorData.toFormatStr(monitorData);
        if (log.isInfoEnabled()) {
            log.info("CalculateMonitor:{}", msg);
        }
        if (Strings.isNullOrEmpty(msg)) {
            return;
        }
        sessionIds.forEach(sessionId -> logAndSendMsg(msg, sessionId));
    }

    private void logAndSendMsg(String msg, String sessionId) {
        SendMsgBySession.Arg arg = SendMsgBySession.Arg.builder()
                .content(msg)
                .senderId("E.fs.7576")
//                .sessionId("196e6480270e4915ad3b8546a4edc840")
//                .sessionId("6b1de449f5484149b377e97526351c04")
                .sessionId(sessionId)
                .build();
        Map<String, String> headers = Maps.newHashMap();
        headers.put("x-tenant-id", "1");
        headers.put("x-user-id", User.SUPPER_ADMIN_USER_ID);
        try {
            messageProxy.sendMsgBySession(arg, headers);
        } catch (Exception e) {
            log.warn("sendMsgBySession fail! msg:{}", msg, e);
        }
    }

    private MongoCollection<Document> getCollection(String topic) {
        return calculateMongoStore.getMongo()
                .getDatabase(getDatabaseName())
                .getCollection(topic);
    }

    /**
     * db.getCollection('buf_spec_obj0509_1').aggregate([{'$match':{'ei':'78057'}},{'$group':{_id:'$mode',dtime_min:{$min:'$dtime'},total:{$sum: 1}}}])
     * <p>
     * db.getCollection('buf_spec_obj0509_1').aggregate([{'$match':{'$and':[{'ei':'78057'},{'mode':'3'}]}},{'$group':{_id:'$apiName',counter:{$sum: 1}},{'$sort':{'sum':-1}},{'$limit':3}}])
     *
     * @param tenantId
     * @param topic
     * @param now
     * @param configItem
     * @return
     */
    private Set<MonitorData> findMonitorData(String tenantId, String topic, long now, CalculateConfig.MonitorConfig configItem) {
        Set<MonitorData> result = Sets.newHashSet();
        MongoCollection<Document> collection = getCollection(topic);
        // 按mode分组，查询对应队列中堆积的消息数
        Bson match = Aggregates.match(Filters.and(Filters.eq("ei", tenantId)));
        Bson group = Aggregates.group("$mode", Accumulators.min("dtime_min", "$dtime"), Accumulators.sum("total", 1));
        for (Document document : collection.aggregate(Arrays.asList(match, group))) {
            Long dispatchTime = document.getLong("dtime_min");
            if (Objects.isNull(dispatchTime)) {
                continue;
            }
            Integer mode = document.getInteger("_id");
            long delayTime = now - dispatchTime;
            int time = configItem.getTimeByMode(MonitorData.getModeStr(mode));
            // 代码判断延迟时间、不满足的不需要发消息
            long dTime = time * 1000L;
            if (delayTime < dTime) {
                continue;
            }
            Integer total = document.getInteger("total");
            // 指定mode，按apiName分组，查询top3的对象堆积的数量
            Bson filterBeforeGroup = Aggregates.match(Filters.and(Filters.eq("ei", tenantId), Filters.eq("mode", mode)));
            Bson groupByApiName = Aggregates.group("$apiName", Accumulators.sum("counter", 1));
            Bson sort = Aggregates.sort(Sorts.descending("counter"));
            Bson limit = Aggregates.limit(3);
            MonitorData.MonitorDataBuilder monitorDataBuilder = MonitorData.builder()
                    .tenantId(tenantId)
                    .topic(topic)
                    .mode(mode)
                    .total(total)
                    .dispatchTime(dispatchTime)
                    .delayTime(delayTime)
                    .tenantId(tenantId);
            for (Document doc : collection.aggregate(Arrays.asList(filterBeforeGroup, groupByApiName, sort, limit))) {
                MonitorData.ObjectInfo objectInfo = MonitorData.ObjectInfo.of(doc.getString("_id"), doc.getInteger("counter"));
                monitorDataBuilder.objectInfo(objectInfo);
            }
            result.add(monitorDataBuilder.build());
        }
        return result;
    }

    private String getDatabaseName() {
        return calculateMongoStore.getDB().getName();
    }
}
