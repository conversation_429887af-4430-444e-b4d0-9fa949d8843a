package com.facishare.paas.task.calculate.util;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * create by <PERSON><PERSON><PERSON> on 2020/11/10
 */
public class TopicConstant {

    public final static String BASE_MANUAL_TOPIC = "calculate-task-data-manual";
    public final static String MANUAL_TOPIC = "calculate-task-data-manual-default";
    public final static String MANUAL_VIP_TOPIC = "calculate-task-data-manual-vip";
    public final static String MANUAL_GRAY_TOPIC = "calculate-task-data-manual-gray";
    public final static String MANUAL_STAGE_TOPIC = "calculate-task-data-manual-stage";

    public final static String BASE_BATCH_TOPIC = "calculate-task-data-batch";
    public final static String BATCH_TOPIC = "calculate-task-data-batch-default";
    public final static String BATCH_VIP_TOPIC = "calculate-task-data-batch-vip";
    public final static String BATCH_GRAY_TOPIC = "calculate-task-data-batch-gray";
    public final static String BATCH_STAGE_TOPIC = "calculate-task-data-batch-stage";

    public final static String BASE_OP_TOOL_TOPIC = "calculate-task-data-optool";
    public final static String OP_TOOL_TOPIC = "calculate-task-data-optool-default";
    public final static String OP_TOOL_VIP_TOPIC = "calculate-task-data-optool-vip";
    public final static String OP_TOOL_GRAY_TOPIC = "calculate-task-data-optool-gray";
    public final static String OP_TOOL_STAGE_TOPIC = "calculate-task-data-optool-stage";

    public final static String BASE_CALCULATE_TASK_JOB_TOPIC = "calculate-task-job";
    public final static String CALCULATE_TASK_JOB_TOPIC = "calculate-task-job-default";
    public final static String CALCULATE_TASK_JOB_VIP_TOPIC = "calculate-task-job-vip";
    public final static String CALCULATE_TASK_JOB_GRAY_TOPIC = "calculate-task-job-gray";
    public final static String CALCULATE_TASK_JOB_STAGE_TOPIC = "calculate-task-job-stage";

    public final static String BASE_AGG_MANUAL_TOPIC = "calculate-task-agg-manual";
    public final static String AGG_MANUAL_TOPIC = "calculate-task-agg-manual-default";
    public final static String AGG_MANUAL_VIP_TOPIC = "calculate-task-agg-manual-vip";
    public final static String AGG_MANUAL_GRAY_TOPIC = "calculate-task-agg-manual-gray";
    public final static String AGG_MANUAL_STAGE_TOPIC = "calculate-task-agg-manual-stage";

    public final static String BASE_AGG_BATCH_TOPIC = "calculate-task-agg-batch";
    public final static String AGG_BATCH_TOPIC = "calculate-task-agg-batch-default";
    public final static String AGG_BATCH_VIP_TOPIC = "calculate-task-agg-batch-vip";
    public final static String AGG_BATCH_GRAY_TOPIC = "calculate-task-agg-batch-gray";
    public final static String AGG_BATCH_STAGE_TOPIC = "calculate-task-agg-batch-stage";

    public final static String BASE_AGG_OP_TOOL_TOPIC = "calculate-task-agg-optool";
    public final static String AGG_OP_TOOL_TOPIC = "calculate-task-agg-optool-default";
    public final static String AGG_OP_TOOL_VIP_TOPIC = "calculate-task-agg-optool-vip";
    public final static String AGG_OP_TOOL_GRAY_TOPIC = "calculate-task-agg-optool-gray";
    public final static String AGG_OP_TOOL_STAGE_TOPIC = "calculate-task-agg-optool-stage";

    public final static String BASE_AGG_HIS_TOPIC = "calculate-task-agg-his";
    public final static String AGG_HIS_TOPIC = "calculate-task-agg-his-default";
    public final static String AGG_HIS_VIP_TOPIC = "calculate-task-agg-his-vip";
    public final static String AGG_HIS_GRAY_TOPIC = "calculate-task-agg-his-gray";
    public final static String AGG_HIS_STAGE_TOPIC = "calculate-task-agg-his-stage";

    private static final Map<String, String> baseTopicMap = Maps.newHashMap();

    static {
        baseTopicMap.put(MANUAL_TOPIC, BASE_MANUAL_TOPIC);
        baseTopicMap.put(MANUAL_VIP_TOPIC, BASE_MANUAL_TOPIC);
        baseTopicMap.put(MANUAL_GRAY_TOPIC, BASE_MANUAL_TOPIC);
        baseTopicMap.put(MANUAL_STAGE_TOPIC, BASE_MANUAL_TOPIC);

        baseTopicMap.put(BATCH_TOPIC, BASE_BATCH_TOPIC);
        baseTopicMap.put(BATCH_VIP_TOPIC, BASE_BATCH_TOPIC);
        baseTopicMap.put(BATCH_GRAY_TOPIC, BASE_BATCH_TOPIC);
        baseTopicMap.put(BATCH_STAGE_TOPIC, BASE_BATCH_TOPIC);

        baseTopicMap.put(OP_TOOL_TOPIC, BASE_OP_TOOL_TOPIC);
        baseTopicMap.put(OP_TOOL_VIP_TOPIC, BASE_OP_TOOL_TOPIC);
        baseTopicMap.put(OP_TOOL_GRAY_TOPIC, BASE_OP_TOOL_TOPIC);
        baseTopicMap.put(OP_TOOL_STAGE_TOPIC, BASE_OP_TOOL_TOPIC);

        baseTopicMap.put(CALCULATE_TASK_JOB_TOPIC, BASE_CALCULATE_TASK_JOB_TOPIC);
        baseTopicMap.put(CALCULATE_TASK_JOB_VIP_TOPIC, BASE_CALCULATE_TASK_JOB_TOPIC);
        baseTopicMap.put(CALCULATE_TASK_JOB_GRAY_TOPIC, BASE_CALCULATE_TASK_JOB_TOPIC);
        baseTopicMap.put(CALCULATE_TASK_JOB_STAGE_TOPIC, BASE_CALCULATE_TASK_JOB_TOPIC);


        baseTopicMap.put(AGG_MANUAL_TOPIC, BASE_AGG_MANUAL_TOPIC);
        baseTopicMap.put(AGG_MANUAL_VIP_TOPIC, BASE_AGG_MANUAL_TOPIC);
        baseTopicMap.put(AGG_MANUAL_GRAY_TOPIC, BASE_AGG_MANUAL_TOPIC);
        baseTopicMap.put(AGG_MANUAL_STAGE_TOPIC, BASE_AGG_MANUAL_TOPIC);

        baseTopicMap.put(AGG_BATCH_TOPIC, BASE_AGG_BATCH_TOPIC);
        baseTopicMap.put(AGG_BATCH_VIP_TOPIC, BASE_AGG_BATCH_TOPIC);
        baseTopicMap.put(AGG_BATCH_GRAY_TOPIC, BASE_AGG_BATCH_TOPIC);
        baseTopicMap.put(AGG_BATCH_STAGE_TOPIC, BASE_AGG_BATCH_TOPIC);

        baseTopicMap.put(AGG_OP_TOOL_TOPIC, BASE_AGG_OP_TOOL_TOPIC);
        baseTopicMap.put(AGG_OP_TOOL_VIP_TOPIC, BASE_AGG_OP_TOOL_TOPIC);
        baseTopicMap.put(AGG_OP_TOOL_GRAY_TOPIC, BASE_AGG_OP_TOOL_TOPIC);
        baseTopicMap.put(AGG_OP_TOOL_STAGE_TOPIC, BASE_AGG_OP_TOOL_TOPIC);

        baseTopicMap.put(AGG_HIS_TOPIC, BASE_AGG_HIS_TOPIC);
        baseTopicMap.put(AGG_HIS_VIP_TOPIC, BASE_AGG_HIS_TOPIC);
        baseTopicMap.put(AGG_HIS_GRAY_TOPIC, BASE_AGG_HIS_TOPIC);
        baseTopicMap.put(AGG_HIS_STAGE_TOPIC, BASE_AGG_HIS_TOPIC);
    }

    public static String getBaseTopic(String topic) {
        String baseTopic = baseTopicMap.get(topic);
        if (baseTopic == null) {
            return null;
        }
        return baseTopic;
    }

    public static boolean isBaseTopic(String topic) {
        return baseTopicMap.containsValue(topic);
    }

    public static boolean isBatch(String topic) {
        return BATCH_TOPIC.equals(topic) || BATCH_VIP_TOPIC.equals(topic) || BATCH_GRAY_TOPIC.equals(topic) || BATCH_STAGE_TOPIC.equals(topic);
    }

    public static boolean isOpTool(String topic) {
        return OP_TOOL_TOPIC.equals(topic) || OP_TOOL_VIP_TOPIC.equals(topic) || OP_TOOL_GRAY_TOPIC.equals(topic) || OP_TOOL_STAGE_TOPIC.equals(topic);
    }

    public static boolean isCalculationJob(String topic) {
        return CALCULATE_TASK_JOB_TOPIC.equals(topic) || CALCULATE_TASK_JOB_VIP_TOPIC.equals(topic)
                || CALCULATE_TASK_JOB_GRAY_TOPIC.equals(topic) || CALCULATE_TASK_JOB_STAGE_TOPIC.equals(topic);
    }

    public static boolean isAggHis(String topic) {
        return AGG_HIS_TOPIC.equals(topic) || AGG_HIS_VIP_TOPIC.equals(topic) || AGG_HIS_GRAY_TOPIC.equals(topic) || AGG_HIS_STAGE_TOPIC.equals(topic);
    }

    public static boolean isAggTopic(String topic) {
        return topic.contains("-agg-");
    }
}
