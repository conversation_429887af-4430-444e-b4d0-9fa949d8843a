package com.facishare.paas.task.calculate.listener;

import com.facishare.paas.task.calculate.comsumer.AggregateValueMetadataConsumer;
import com.facishare.paas.task.calculate.model.CustomDataChangeMessage;
import com.facishare.paas.task.calculate.service.AggregateValueService;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;

public class AggregateValueMetadataListener extends MetadataObjectDataChangeListener {

    @Autowired
    private AggregateValueMetadataConsumer aggregateValueMetadataConsumer;

    @Autowired
    private AggregateValueService aggregateValueService;

    @Override
    protected void doConsume(CustomDataChangeMessage arg) {
        aggregateValueMetadataConsumer.execute(arg);
    }

    @Override
    protected boolean skip(CustomDataChangeMessage arg, MessageExt message) {
        if (super.skip(arg, message)) {
            return true;
        }
        return !aggregateValueService.isSupportAggregateValue(arg.getTenantId());
    }
}
