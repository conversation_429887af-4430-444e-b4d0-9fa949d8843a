package com.facishare.paas.task.calculate.listener;

import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.fxiaoke.dispatcher.listener.EventListener;
import org.springframework.beans.factory.annotation.Autowired;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.metadata.exception.FieldNotExistException;
import com.facishare.paas.task.calculate.config.CalculateConfig;
import com.facishare.paas.task.calculate.config.Namespaces;
import com.facishare.paas.task.calculate.exception.FieldValidateException;
import com.facishare.paas.task.calculate.model.CalculateDataEvent;
import com.facishare.paas.task.calculate.model.CalculateDataGroup;
import com.facishare.paas.task.calculate.service.CalculateEventService;
import com.facishare.paas.task.calculate.service.FieldCalculateService;
import com.github.autoconf.helper.ConfigHelper;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CalculateDataEventListener implements EventListener<CalculateDataEvent> {

    private static final String APP_NAME = ConfigHelper.getProcessInfo().getName();

    @Autowired
    private FieldCalculateService fieldCalculateService;

    @Autowired
    private CalculateEventService calculateEventService;

    @Override
    public void listen(List<CalculateDataEvent> events) {
        Tuple<String, String> traceIds = initTraceContext();
        try {
            List<CalculateDataEvent> resendEvents = events.stream().filter(this::needResend).collect(Collectors.toList());
            resend(resendEvents);
            List<CalculateDataEvent> validEvents = events.stream().filter(x -> !resendEvents.contains(x)).collect(Collectors.toList());
            if (CollectionUtils.empty(validEvents)) {
                return;
            }
            List<CalculateDataGroup> groups = CalculateDataEvent.groups(validEvents);
            AtomicInteger count = new AtomicInteger(0);
            String traceId = TraceContext.get().getTraceId();
            groups.forEach(group -> {
                try {
                    initSubContext(group, traceId, count.incrementAndGet());
                    log.info("calculate start,ei:{},group:{}", group.getTenantId(), group);
                    doConsume(group);
                    markDone(validEvents, group);
                    log.info("calculate end,ei:{},cost:{}", group.getTenantId(), System.currentTimeMillis() - group.getConsumeTimestamp());
                } catch (AppBusinessException e) {
                    log.warn("calculate failed,ei:{},objectApiName:{},fieldType:{},fieldApiNames:{}",
                            group.getTenantId(), group.getObjectApiName(), group.getFieldType(), group.getFieldApiNames(), e);
                    reConsume(validEvents, group, e);
                } catch (Exception e) {
                    log.error("calculate error,ei:{},objectApiName:{},fieldType:{},fieldApiNames:{}",
                            group.getTenantId(), group.getObjectApiName(), group.getFieldType(), group.getFieldApiNames(), e);
                    reConsume(validEvents, group, e);
                } finally {
                    cleanSubContext(traceId);
                }
            });
        } finally {
            cleanTraceContext(traceIds);
        }
    }

    private boolean needResend(CalculateDataEvent event) {
        Namespaces currentNamespace = Namespaces.currentNamespace();
        Namespaces tenantNamespace = Namespaces.getByTenantId(event.getTenantId());
        if (currentNamespace != tenantNamespace) {
            return true;
        }
        String topic = Namespaces.getEventTopicByTenantIdAndObject(event.getTenantId(), event.getObjectApiName(), event.getDataId());
        if (!Objects.equals(topic, event.topicWithoutNamespace())) {
            return true;
        }
        return false;
    }

    private void resend(List<CalculateDataEvent> events) {
        if (CollectionUtils.empty(events)) {
            return;
        }
        calculateEventService.resendEvents(events);
        markDone(events, null);
        log.info("resend events,tenantIds:{},eventNum:{}", events.stream().map(x -> x.getTenantId())
                .distinct().collect(Collectors.toList()), events.size());
    }

    private void doConsume(CalculateDataGroup group) {
        fieldCalculateService.calculateByGroup(group);
    }

    private void markDone(List<CalculateDataEvent> events, CalculateDataGroup group) {
        if (group == null) {
            events.forEach(x -> x.markDone());
        } else {
            CalculateDataEvent.filterByGroup(events, group).forEach(x -> x.markDone());
        }
    }

    private void markError(List<CalculateDataEvent> events, CalculateDataGroup group) {
        if (group == null) {
            events.forEach(x -> x.markError());
        } else {
            CalculateDataEvent.filterByGroup(events, group).forEach(x -> x.markError());
        }
    }

    private void reConsume(List<CalculateDataEvent> events, CalculateDataGroup group, Exception e) {
        int maxRetryTimes = e instanceof FieldNotExistException || e instanceof FieldValidateException ?
                CalculateConfig.getFieldValidateRetryTimes() : CalculateConfig.getRetryTimes();
        //消息异常重试
        CalculateDataEvent.filterByGroup(events, group).forEach(event -> {
            int tries = event.getTries();
            if (tries > maxRetryTimes) {
                log.warn("mark error,eventId:{},tries:{},maxRetryTimes:{}", event.getId().toHexString(), tries, maxRetryTimes);
                event.markError();
            }
        });
    }

    private Tuple<String, String> initTraceContext() {
        TraceContext traceContext = TraceContext.get();
        String oldTraceId = traceContext.getTraceId();
        String newTraceId = null;
        if (Strings.isNullOrEmpty(traceContext.getTraceId())) {
            newTraceId = APP_NAME + "/" + UUID.randomUUID().toString().replace("-", "");
            traceContext.setTraceId(newTraceId);
        }
        return Tuple.of(oldTraceId, newTraceId);
    }

    private void cleanTraceContext(Tuple<String, String> traceIds) {
        if (Strings.isNullOrEmpty(traceIds.getKey())) {
            TraceContext.remove();
        } else {
            TraceContext.get().setTraceId(traceIds.getKey());
        }
    }

    private void initSubContext(CalculateDataGroup group, String oldTraceId, int count) {
        group.setConsumeTimestamp(System.currentTimeMillis());
        //生成子trace
        TraceContext.get().setTraceId(oldTraceId + "_" + count)
                .setEi(group.getTenantId());
        RequestContext requestContext = RequestContext.builder()
                .lang(Lang.zh_CN)
                .batch(group.isBatch())
                .tenantId(group.getTenantId())
                .user(User.systemUser(group.getTenantId()))
                .build();
        requestContext.setAttribute(RequestContext.Attributes.IS_CALCULATE_CONTEXT, true);
        requestContext.setAttribute(RequestContext.IS_NOT_NEED_DEEP_COPY, true);
        RequestContextManager.setContext(requestContext);
    }

    private void cleanSubContext(String oldTraceId) {
        RequestContextManager.removeContext();
        TraceContext.get().setTraceId(oldTraceId)
                .setEi(null);
    }

}
