package com.facishare.paas.task.calculate.util;

import com.facishare.paas.task.calculate.config.CalculateConfig;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import groovy.xml.Entity;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static groovy.xml.Entity.times;

/**
 * Created by liwei on 2019/7/6
 */
@Slf4j
public class SlowTenantCache {
    private static Cache<String, AtomicInteger> localCache;

    static {
        localCache = CacheBuilder.newBuilder()
                .expireAfterWrite(10, TimeUnit.MINUTES)
                .maximumSize(10000)
                .build();
    }

    public static boolean isSlowTenant(String tenantId) {
        AtomicInteger times = localCache.getIfPresent(tenantId);
        if (times != null && times.get() > CalculateConfig.config.getSlowTimesThreshold()) {
            return true;
        }
        return false;
    }

    public static void addSlowTenantCache(String tenantId) {
        try {
            AtomicInteger atomicInteger = localCache.get(tenantId, () -> new AtomicInteger(0));
            atomicInteger.incrementAndGet();
        } catch (ExecutionException e) {
            log.error("addSlowTenantCache tenantId:{},error:",tenantId,e);
        }
    }
}
