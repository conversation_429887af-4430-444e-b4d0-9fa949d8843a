package com.facishare.paas.task.calculate.comsumer;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.AggregateRule;
import com.facishare.paas.appframework.metadata.AggregateValue;
import com.facishare.paas.task.calculate.model.AggregateCalculateNode;
import com.facishare.paas.task.calculate.model.AggregateValueCalculateMessage;
import com.facishare.paas.task.calculate.service.AggregateValueService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class AggregateValueCalculateConsumer implements Consumer<AggregateValueCalculateMessage> {

    @Autowired
    private AggregateValueService aggregateValueService;

    @Override
    public void execute(AggregateValueCalculateMessage message) {
        AggregateRule aggregateRule = aggregateValueService.findAggregateRuleById(message.getTenantId(), message.getAggregateRuleId());
        if (CollectionUtils.notEmpty(message.getDataIdList())) {
            aggregateByDataIdList(message.getTenantId(), aggregateRule, message.getDataIdList());
        }
        if (CollectionUtils.notEmpty(message.getNodeList())) {
            aggregateByNodeList(message.getTenantId(), aggregateRule, message.getNodeList());
        }
    }

    private void aggregateByNodeList(String tenantId, AggregateRule aggregateRule, List<AggregateCalculateNode> nodeList) {
        if (CollectionUtils.empty(nodeList)) {
            return;
        }
        //有聚合日期和聚合维度的直接聚合
        Map<Long, Set<String>> date2DimensionMap = Maps.newHashMap();
        nodeList.stream()
                .filter(x -> !Strings.isNullOrEmpty(x.getAggregateDimension()) && Objects.nonNull(x.getAggregateDate()))
                .forEach(x -> {
                    date2DimensionMap.putIfAbsent(x.getAggregateDate(), Sets.newHashSet());
                    date2DimensionMap.get(x.getAggregateDate()).add(x.getAggregateDimension());
                });
        date2DimensionMap.forEach((date, dimensions) ->
                aggregateByDateAndDimensions(tenantId, aggregateRule, date, dimensions));

        //没有聚合维度的需要根据数据id去查询聚合维度，按聚合日期合并之后再聚合
        List<AggregateCalculateNode> noDimensionNodes = nodeList.stream()
                .filter(x -> Strings.isNullOrEmpty(x.getAggregateDimension()) && Objects.nonNull(x.getAggregateDate()))
                .collect(Collectors.toList());
        aggregateByDataIdAndNodeList(tenantId, aggregateRule, noDimensionNodes);

        //没有聚合维度和聚合日期的需要根据数据id去查询聚合维度和聚合日期再聚合
        List<String> noDimensionAndDateIds = nodeList.stream()
                .filter(x -> Strings.isNullOrEmpty(x.getAggregateDimension()) && Objects.isNull(x.getAggregateDate()))
                .map(AggregateCalculateNode::getDataId)
                .collect(Collectors.toList());
        aggregateByDataIdList(tenantId, aggregateRule, noDimensionAndDateIds);
    }

    private void aggregateByDateAndDimensions(String tenantId, AggregateRule aggregateRule, Long date, Set<String> dimensions) {
        List<AggregateValue> aggregateValueList = aggregateValueService.aggregateByDateAndDimensions(tenantId, aggregateRule, date, Lists.newArrayList(dimensions));
        aggregateValueService.bulkUpsertAggregateValue(tenantId, aggregateValueList);
    }


    private void aggregateByDataIdList(String tenantId, AggregateRule aggregateRule, List<String> dataIdList) {
        if (CollectionUtils.empty(dataIdList)) {
            return;
        }
        List<AggregateValue> aggregateValueList = aggregateValueService.aggregateByDataIds(tenantId, aggregateRule, dataIdList, null);
        aggregateValueService.bulkUpsertAggregateValue(tenantId, aggregateValueList);
    }

    private void aggregateByDataIdAndNodeList(String tenantId, AggregateRule aggregateRule, List<AggregateCalculateNode> nodeList) {
        if (CollectionUtils.empty(nodeList)) {
            return;
        }
        List<String> dataIdList = nodeList.stream().map(AggregateCalculateNode::getDataId).distinct().collect(Collectors.toList());
        Map<String, Set<Long>> dateMap = Maps.newHashMap();
        nodeList.forEach(x -> {
            dateMap.putIfAbsent(x.getDataId(), Sets.newHashSet());
            dateMap.get(x.getDataId()).add(x.getAggregateDate());
        });
        List<AggregateValue> aggregateValueList = aggregateValueService.aggregateByDataIds(tenantId, aggregateRule, dataIdList, dateMap);
        aggregateValueService.bulkUpsertAggregateValue(tenantId, aggregateValueList);
    }
}
