package com.facishare.paas.task.calculate.model;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.facishare.paas.task.calculate.config.Namespaces;
import com.facishare.paas.task.calculate.util.DelayTimer;
import com.facishare.paas.task.calculate.util.PriorityCalculator;
import com.facishare.paas.task.calculate.util.QueueTypes;
import com.fxiaoke.dispatcher.common.Constants;
import com.fxiaoke.dispatcher.common.MessageHelper;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * Created by liwei on 2018/10/8
 */
@Slf4j
@Data
public class CalculationJobMessage {

    private List<String> dataIdList;
    private Boolean endMessage;
    private String jobId;
    private String jobParam;
    private String objectApiName;
    private String tenantId;
    private String userId;
    private Long createTime;
    private Long lastModifiedTime;
    private boolean manual;
    private int reconsumeTimes;

    private Long originalBornTimestamp;
    private transient String messageId;
    private transient Long bornTimestamp;
    private transient long consumeTimestamp = System.currentTimeMillis();
    private transient String queueIndex;

    public List<CalculateDataEvent> toEvents(Map<String, List<String>> fieldMap) {
        List<CalculateDataEvent> events = Lists.newArrayList();
        fieldMap.forEach((fieldType, fields) -> events.addAll(toEvents(fields, fieldType, dataIdList)));
        return events;
    }

    private List<CalculateDataEvent> toEvents(List<String> fieldApiNames, String fieldType, List<String> dataIds) {
        return dataIds.stream().map(x -> toEvent(fieldApiNames, fieldType, x)).collect(Collectors.toList());
    }

    private CalculateDataEvent toEvent(List<String> fieldApiNames, String fieldType, String dataId) {
        // 唯一ID(tenantId + objectDescribeApiName + dataId)
        String uniqKey = MessageHelper.md5(this.tenantId, this.objectApiName, dataId, fieldType);
        // 构建分类（tenantId + objectDescribeApiName）
        String category = Joiner.on('^').join(this.tenantId, this.objectApiName);

        // 根据tenantId查询topic名称
        String topic = Namespaces.getEventTopicByTenantIdAndObject(this.tenantId, this.objectApiName, dataId);
        long now = System.currentTimeMillis();
        long dispatchTime = now + getDelayTime(fieldType);
        int priority = getPriority(tenantId, fieldApiNames);

        return CalculateDataEvent.builder()
                .status(Constants.EventStatus.STATUS_READY)
                .key(uniqKey)
                .topic(topic)
                .category(category)
                .priority(priority)
                .dispatchTime(dispatchTime)
                .createTime(now)
                .modifiedTime(now)
                .tenantId(tenantId)
                .objectApiName(this.objectApiName)
                .dataId(dataId)
                .fieldApiNames(Sets.newHashSet(fieldApiNames))
                .fieldType(fieldType)
                .action(QueueTypes.CALCULATION_JOB)
                .queueTypes(Sets.newHashSet(QueueTypes.CALCULATION_JOB))
                .jobIds(Sets.newHashSet(this.jobId))
                .originalObjectApiName(this.objectApiName)
                .originalBornTimestamp(this.originalBornTimestamp)
                .originalMessageId(this.messageId)
                .build();
    }

    private int getPriority(String tenantId, Collection<String> fieldApiNames) {
        return PriorityCalculator.builder()
                .tenantId(tenantId)
                .objectApiName(this.objectApiName)
                .fieldApiNames(fieldApiNames)
                .queueType(QueueTypes.CALCULATION_JOB)
                .build()
                .calculate();
    }

    private int getDelayTime(String fieldType) {
        return DelayTimer.getDelayTime(QueueTypes.CALCULATION_JOB, fieldType);
    }

    public int dataNum() {
        return Objects.isNull(dataIdList) ? 0 : dataIdList.size();
    }

    /**
     * 判断消息是否应该重新消费
     * @param maxReconsumeTimes 最大重试次数
     * @return 如果当前重试次数小于最大重试次数，返回true；否则返回false
     */
    public boolean shouldReconsume(int maxReconsumeTimes) {
        return this.reconsumeTimes < maxReconsumeTimes;
    }
}
