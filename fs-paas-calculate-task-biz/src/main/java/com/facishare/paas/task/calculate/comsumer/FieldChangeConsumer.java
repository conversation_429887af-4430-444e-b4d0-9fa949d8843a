package com.facishare.paas.task.calculate.comsumer;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.task.calculate.model.FieldChangeMessage;
import com.facishare.paas.task.calculate.comsumer.AbstractDescribeChangeConsumer;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2019/1/16
 */
@Slf4j
@Service
public class FieldChangeConsumer extends AbstractDescribeChangeConsumer {

    //op:  新建  field_add  更新 field_update 删除 field_delete  field_enable   field_disable
    @Override
    public void execute(FieldChangeMessage message) {
        if (CollectionUtils.empty(message.getBody())) {
            return;
        }
        Set<String> objectApiNames = message.getBody().stream().map(x -> x.getObjectDescribeApiName()).collect(Collectors.toSet());
        describePackageService.invalidateCache(message.getTenantId(), objectApiNames);

        if (FieldChangeMessage.Ops.FIELD_DELETE.equals(message.getOp())) {
            return;
        }

        Set<String> hasCalculateObjectApiNames = Sets.newHashSet();
        message.getBody().forEach(body -> {
            if (body.getFields().stream().anyMatch(x -> isCalculateField(x.getFieldType()))) {
                hasCalculateObjectApiNames.add(body.getObjectDescribeApiName());
            }
        });
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjects(message.getTenantId(), hasCalculateObjectApiNames);
        message.getBody().stream().filter(body -> describeMap.containsKey(body.getObjectDescribeApiName())).forEach(body -> {
            IObjectDescribe describe = describeMap.get(body.getObjectDescribeApiName());
            body.getFields().stream()
                    .filter(x -> describe.containsField(x.getFieldApiName()))
                    .filter(x -> isCalculateField(x.getFieldType()))
                    .map(x -> FieldDescribeExt.of(describe.getFieldDescribe(x.getFieldApiName())))
                    .forEach(x -> invalidateRefObjectGraphs(message.getTenantId(), describe, x));
        });
    }

    private boolean isCalculateField(String fieldType) {
        return IFieldType.FORMULA.equals(fieldType) || IFieldType.COUNT.equals(fieldType) || IFieldType.QUOTE.equals(fieldType);
    }

}
