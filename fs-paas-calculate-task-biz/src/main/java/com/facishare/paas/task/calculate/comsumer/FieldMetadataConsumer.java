package com.facishare.paas.task.calculate.comsumer;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.ReferenceLogicService;
import com.facishare.paas.appframework.metadata.cache.RedisDao;
import com.facishare.paas.task.calculate.dispatcher.*;
import com.facishare.paas.task.calculate.model.CustomDataChangeMessage;
import com.facishare.paas.task.calculate.service.CalculateEventService;
import com.facishare.paas.task.calculate.service.DataPackageService;
import com.facishare.paas.task.calculate.service.DescribePackageService;
import com.facishare.paas.task.calculate.service.SendMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by liwei on 2018/10/16
 */
@Service
@Slf4j
public class FieldMetadataConsumer implements Consumer<CustomDataChangeMessage> {

    @Autowired
    private DescribePackageService describePackageService;
    @Autowired
    private SendMessageService sendMessageService;
    @Autowired
    private DataPackageService dataPackageService;
    @Autowired
    private RedisDao redisDao;
    @Autowired
    private CalculateEventService calculateEventService;
    @Autowired
    private ReferenceLogicService referenceLogicService;

    @Override
    public void execute(CustomDataChangeMessage message) {
        String actionType = message.getOp();
        switch (actionType) {
            case "u":
                UpdateDispatcher.builder()
                        .action(ObjectAction.UPDATE)
                        .describePackageService(describePackageService)
                        .message(message)
                        .sendMessageService(sendMessageService)
                        .dataPackageService(dataPackageService)
                        .redisDao(redisDao)
                        .calculateEventService(calculateEventService)
                        .referenceLogicService(referenceLogicService)
                        .build()
                        .doDispatch();
                break;
            case "i":
                InsertDispatcher.builder()
                        .action(ObjectAction.CREATE)
                        .describePackageService(describePackageService)
                        .message(message)
                        .sendMessageService(sendMessageService)
                        .dataPackageService(dataPackageService)
                        .redisDao(redisDao)
                        .calculateEventService(calculateEventService)
                        .referenceLogicService(referenceLogicService)
                        .build()
                        .doDispatch();
                break;
            case "invalid":
                InvalidDispatcher.builder()
                        .action(ObjectAction.INVALID)
                        .describePackageService(describePackageService)
                        .message(message)
                        .sendMessageService(sendMessageService)
                        .dataPackageService(dataPackageService)
                        .redisDao(redisDao)
                        .calculateEventService(calculateEventService)
                        .referenceLogicService(referenceLogicService)
                        .build()
                        .doDispatch();
                break;
            case "recover":
                RecoverDispatcher.builder()
                        .action(ObjectAction.RECOVER)
                        .describePackageService(describePackageService)
                        .message(message)
                        .sendMessageService(sendMessageService)
                        .dataPackageService(dataPackageService)
                        .redisDao(redisDao)
                        .calculateEventService(calculateEventService)
                        .referenceLogicService(referenceLogicService)
                        .build()
                        .doDispatch();
                break;
            case "d":
            case "d_r":
                DeletedDispatcher.builder()
                        .deleteDirect("d_r".equals(actionType))
                        .action(ObjectAction.INVALID)
                        .describePackageService(describePackageService)
                        .message(message)
                        .sendMessageService(sendMessageService)
                        .dataPackageService(dataPackageService)
                        .redisDao(redisDao)
                        .calculateEventService(calculateEventService)
                        .referenceLogicService(referenceLogicService)
                        .build()
                        .doDispatch();
                break;
            default:
                break;
        }
    }

}
