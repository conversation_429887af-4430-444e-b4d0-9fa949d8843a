package com.facishare.paas.task.calculate.listener;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.task.calculate.comsumer.AggregateValueHistoryCalculateConsumer;
import com.facishare.paas.task.calculate.model.AggregateValueHistoryCalculateMessage;
import com.facishare.paas.task.calculate.util.MessageSupport;
import com.github.trace.TraceContext;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;

public class AggregateValueHistoryCalculateListener extends BaseListener<AggregateValueHistoryCalculateMessage> {

    @Autowired
    private AggregateValueHistoryCalculateConsumer aggregateValueHistoryCalculateConsumer;

    @Override
    protected AggregateValueHistoryCalculateMessage parseMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        AggregateValueHistoryCalculateMessage calcMessage = JSON.parseObject(body, AggregateValueHistoryCalculateMessage.class);
        calcMessage.setMessageId(message.getMsgId());
        calcMessage.setOriginalMessageId(MessageSupport.getOriginalMessageId(message));
//        calcMessage.setBornTimestamp(message.getBornTimestamp());
//        calcMessage.setOriginalBornTimestamp(MessageSupport.getOriginalBornTimestamp(message));
        return calcMessage;
    }

    @Override
    protected String getArgDesc(AggregateValueHistoryCalculateMessage arg) {
        return JSON.toJSONString(arg);
    }

    @Override
    protected void doConsume(AggregateValueHistoryCalculateMessage arg) {
        aggregateValueHistoryCalculateConsumer.execute(arg);
    }

    @Override
    protected boolean needResend(MessageExt message) {
        return calculateTaskApplication.isReSendMessage(message);
    }

    @Override
    protected String getTenantId(AggregateValueHistoryCalculateMessage arg) {
        return arg.getTenantId();
    }

    @Override
    protected void fillTraceContext(AggregateValueHistoryCalculateMessage arg) {
        String tenantId = arg.getTenantId();
        TraceContext traceContext = TraceContext.get();
        traceContext.setEi(tenantId);
    }
}
