package com.facishare.paas.task.calculate.monitor;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/2/8
 */
@JobHander(value = "CalculateMonitorJobHandler")
@Component
@Slf4j
public class CalculateMonitorJobHandler extends IJobHandler {
    @Autowired
    private CalculateMonitor calculateOverflowMonitor;

    @Override
    public ReturnT execute(TriggerParam params) throws Exception {
        try {
            calculateOverflowMonitor.calculateOverflow();
        } catch (Exception e) {
            log.warn("execute fail!, params:{}", params, e);
            throw e;
        }
        return ReturnT.SUCCESS;
    }
}
