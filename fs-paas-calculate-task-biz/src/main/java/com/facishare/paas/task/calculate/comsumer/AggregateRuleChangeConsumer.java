package com.facishare.paas.task.calculate.comsumer;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.AggregateRule;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.task.calculate.config.CalculateTaskApplication;
import com.facishare.paas.task.calculate.model.AggregateRuleChangeMessage;
import com.facishare.paas.task.calculate.model.AggregateValueHistoryCalculateMessage;
import com.facishare.paas.task.calculate.service.AggregateValueService;
import com.facishare.paas.task.calculate.service.SendMessageService;
import com.facishare.paas.task.calculate.util.MessageConvert;
import com.facishare.paas.task.calculate.util.MessageSupport;
import com.facishare.paas.task.calculate.util.TopicConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class AggregateRuleChangeConsumer implements Consumer<AggregateRuleChangeMessage> {

    @Autowired
    private AggregateValueService aggregateValueService;
    @Autowired
    private SendMessageService sendMessageService;
    @Autowired
    private MetaDataFindService metaDataFindService;

    @Override
    public void execute(AggregateRuleChangeMessage message) {
        AggregateRule aggregateRule = aggregateValueService.findAggregateRuleById(message.getTenantId(), message.getAggregateRuleId());
        updateRuleStatus(message, AggregateRule.CalculateStatus.PROCESSING);

        //先清空该聚合规则下的所有聚合值
        aggregateValueService.deleteAllAggregateValue(message.getTenantId(), message.getAggregateRuleId());

        Long maxAggregateDate = findMaxAggregateDate(message.getTenantId(), aggregateRule);
        log.info("tenantId:{},ruleId:{},maxAggregateDate:{}", message.getTenantId(), message.getAggregateRuleId(), maxAggregateDate);
        if (Objects.isNull(maxAggregateDate)) {
            updateRuleStatus(message, AggregateRule.CalculateStatus.COMPLETED);
            return;
        }
        List<Long> dateRange = aggregateRule.getDateRangeWithTimeMillisForCalculate(maxAggregateDate);
        log.info("tenantId:{},ruleId:{},dateRangeSize:{}", message.getTenantId(), message.getAggregateRuleId(), dateRange.size());
        if (CollectionUtils.empty(dateRange)) {
            updateRuleStatus(message, AggregateRule.CalculateStatus.COMPLETED);
            return;
        }
        for (int i = 0; i < dateRange.size(); i++) {
            AggregateValueHistoryCalculateMessage calcMessageBody = AggregateValueHistoryCalculateMessage.builder()
                    .tenantId(message.getTenantId())
                    .actionCode(message.getActionCode())
                    .aggregateRuleId(message.getAggregateRuleId())
                    .aggregateDate(dateRange.get(i))
                    .last(i == dateRange.size() - 1)
                    .build();
            Message calcMessage = buildCalcMessage(calcMessageBody, message);
            sendMessageService.sendMessage(calcMessage);
            log.info("send msg,date:{}", dateRange.get(i));
        }
    }

    private void updateRuleStatus(AggregateRuleChangeMessage message, AggregateRule.CalculateStatus completed) {
        aggregateValueService.updateAggregateRuleCalculateStatus(message.getTenantId(), message.getAggregateRuleId(),
                completed);
    }

    private Message buildCalcMessage(AggregateValueHistoryCalculateMessage calcMessageBody, AggregateRuleChangeMessage originalMessage) {
        byte[] body = JSON.toJSONString(calcMessageBody).getBytes(StandardCharsets.UTF_8);
        String topic = CalculateTaskApplication.getTopic(TopicConstant.BASE_AGG_HIS_TOPIC, calcMessageBody.getTenantId());
        Message calcMessage = new Message(topic, body);
        MessageSupport.setTenantId(calcMessage, originalMessage.getTenantId());
        MessageSupport.setOriginalMessageId(calcMessage, originalMessage.getMessageId());
        MessageSupport.setOriginalBornTimestamp(calcMessage, originalMessage.getBornTimestamp());
        MessageSupport.setType(calcMessage, MessageConvert.AGGREGATE_TYPE);
        return calcMessage;
    }

    private Long findMaxAggregateDate(String tenantId, AggregateRule aggregateRule) {
        List<Map<String, Object>> condition = aggregateRule.getCondition();
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(condition)
                .addFilter(Operator.ISN, aggregateRule.getDimension(), "")
                .addFilter(Operator.ISN, aggregateRule.getDateField(), "");
        //只聚合正常状态的数据
        queryExt.addIsDeletedFalseFilter();
        List<IObjectData> dataList = metaDataFindService.aggregateFindBySearchQuery(tenantId, queryExt.toSearchTemplateQuery(),
                aggregateRule.getAggregateObject(), null, Count.TYPE_MAX, aggregateRule.getDateField());
        log.debug("findMaxAggregateDate complete,tenantId:{},ruleId:{},result:{}", tenantId, aggregateRule.getId(), dataList);
        if (CollectionUtils.empty(dataList)) {
            return null;
        }
        Object value = dataList.get(0).get(AggregateRule.buildAggFieldName(Count.TYPE_MAX, aggregateRule.getDateField()));
        return ObjectDataExt.isValueEmpty(value) ? null : new BigDecimal(value.toString()).longValue();
    }
}
