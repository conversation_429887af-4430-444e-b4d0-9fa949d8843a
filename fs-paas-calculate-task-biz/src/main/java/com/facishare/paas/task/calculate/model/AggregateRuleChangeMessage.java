package com.facishare.paas.task.calculate.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.AggregateRule;
import com.google.common.collect.Sets;
import lombok.Data;

import java.util.Map;
import java.util.Set;

@Data
public class AggregateRuleChangeMessage {
    private static final Set<String> CALCULATE_FIELDS = Sets.newHashSet("aggregate_object", "aggregate_field",
            "dimension", "date_field", "date_range", "aggregate_way", "condition");

    private String tenantId;
    private String actionCode;
    private String aggregateRuleId;
    private String aggregateType;
    private Map<String, Object> updatedFieldMap;

    private transient String messageId;
    private transient Long bornTimestamp;

    public boolean needCalculate() {
        if (!isHistoryType()) {
            return false;
        }
        ObjectAction action = ObjectAction.of(actionCode);
        if (action == ObjectAction.CREATE || action == ObjectAction.RECOVER) {
            return true;
        }
        if (action == ObjectAction.UPDATE) {
            return calculateFieldUpdated();
        }
        return false;
    }

    private boolean isHistoryType() {
        return AggregateRule.TYPE_HISTORY.equals(aggregateType);
    }

    private boolean calculateFieldUpdated() {
        if (CollectionUtils.empty(updatedFieldMap)) {
            return false;
        }
        return updatedFieldMap.keySet().stream().anyMatch(x -> CALCULATE_FIELDS.contains(x));
    }

}
