package com.facishare.paas.task.calculate.listener;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.task.calculate.comsumer.AggregateValueCalculateConsumer;
import com.facishare.paas.task.calculate.model.AggregateValueCalculateMessage;
import com.facishare.paas.task.calculate.util.MessageSupport;
import com.github.trace.TraceContext;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;

public class AggregateValueCalculateListener extends BaseListener<AggregateValueCalculateMessage> {

    @Autowired
    private AggregateValueCalculateConsumer aggregateValueCalculateConsumer;

    @Override
    protected AggregateValueCalculateMessage parseMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        AggregateValueCalculateMessage calcMessage = JSON.parseObject(body, AggregateValueCalculateMessage.class);
        calcMessage.setMessageId(message.getMsgId());
        calcMessage.setOriginalMessageId(MessageSupport.getOriginalMessageId(message));
//        calcMessage.setBornTimestamp(message.getBornTimestamp());
//        calcMessage.setOriginalBornTimestamp(MessageSupport.getOriginalBornTimestamp(message));
        return calcMessage;
    }

    @Override
    protected String getArgDesc(AggregateValueCalculateMessage arg) {
        return JSON.toJSONString(arg);
    }

    @Override
    protected void doConsume(AggregateValueCalculateMessage arg) {
        aggregateValueCalculateConsumer.execute(arg);
    }

    @Override
    protected boolean needResend(MessageExt message) {
        return calculateTaskApplication.isReSendMessage(message);
    }

    @Override
    protected String getTenantId(AggregateValueCalculateMessage arg) {
        return arg.getTenantId();
    }

    @Override
    protected void fillTraceContext(AggregateValueCalculateMessage arg) {
        String tenantId = arg.getTenantId();
        TraceContext traceContext = TraceContext.get();
        traceContext.setEi(tenantId);
    }
}
