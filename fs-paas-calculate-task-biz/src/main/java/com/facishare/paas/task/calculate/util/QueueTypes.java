package com.facishare.paas.task.calculate.util;

import com.facishare.paas.appframework.metadata.relation.FieldNode;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.task.calculate.model.CustomDataChangeMessage;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;

public class QueueTypes {
    public static final String MANUAL = "manual";
    public static final String BATCH = "batch";
    public static final String OPTOOL = "optool";
    public static final String CALCULATION_JOB = "CALCULATION_JOB";
    public static final String AGG_HIS = "agghis";

    public static String merge(Collection<String> queueTypes) {
        if (queueTypes.contains(CALCULATION_JOB)) {
            return CALCULATION_JOB;
        }
        if (queueTypes.contains(OPTOOL)) {
            return OPTOOL;
        }
        if (queueTypes.contains(BATCH)) {
            return BATCH;
        }
        if (queueTypes.contains(MA<PERSON>AL)) {
            return MANUAL;
        }
        return BATCH;
    }

    public static String buildSource(String queueType, String fieldType) {
        if (FieldNode.NodeType.COUNT.name().equalsIgnoreCase(fieldType)) {
            fieldType = IFieldType.COUNT;
        } else if (FieldNode.NodeType.QUOTE.name().equalsIgnoreCase(fieldType)) {
            fieldType = IFieldType.QUOTE;
        } else {
            fieldType = IFieldType.FORMULA;
        }
        return StringUtils.joinWith("|", CustomDataChangeMessage.CALCULATE_SOURCE, queueType, fieldType);
    }
}
