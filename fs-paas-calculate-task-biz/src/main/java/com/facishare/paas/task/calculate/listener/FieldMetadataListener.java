package com.facishare.paas.task.calculate.listener;

import com.facishare.paas.task.calculate.comsumer.FieldMetadataConsumer;
import com.facishare.paas.task.calculate.model.CustomDataChangeMessage;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by zhouwr on 2019/4/22
 */
public class FieldMetadataListener extends MetadataObjectDataChangeListener {

    @Autowired
    private FieldMetadataConsumer calculateFieldMetadataConsumer;

    @Override
    protected void doConsume(CustomDataChangeMessage arg) {
        calculateFieldMetadataConsumer.execute(arg);
    }
}
