package com.facishare.paas.task.calculate.listener;

import com.facishare.paas.task.calculate.listener.BaseListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.common.message.MessageExt;

import java.util.List;

/**
 * create by z<PERSON><PERSON> on 2020/07/01
 */
@Slf4j
public class OrderlyListener implements MessageListenerOrderly {
    private BaseListener listener;

    public void setListener(BaseListener listener) {
        this.listener = listener;
    }

    @Override
    public ConsumeOrderlyStatus consumeMessage(List<MessageExt> msgs, ConsumeOrderlyContext context) {
        try {
            listener.consumeMessage(msgs);
            return ConsumeOrderlyStatus.SUCCESS;
        } catch (Exception e) {
            log.warn("consume message failed!", e);
        }
        return ConsumeOrderlyStatus.SUSPEND_CURRENT_QUEUE_A_MOMENT;
    }
}
