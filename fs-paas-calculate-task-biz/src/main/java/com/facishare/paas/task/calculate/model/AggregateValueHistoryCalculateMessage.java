package com.facishare.paas.task.calculate.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AggregateValueHistoryCalculateMessage {
    private String tenantId;
    private String actionCode;
    private String aggregateRuleId;
    private Long aggregateDate;
    private boolean last;

    private transient String messageId;
    private String originalMessageId;
//    private transient Long originalBornTimestamp;
//    private transient Long bornTimestamp;
//    private transient Long consumeTimestamp;
//    private transient String queueIndex;
}
