package com.facishare.paas.task.calculate.listener;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.task.calculate.config.CalculateConfig;
import com.facishare.paas.task.calculate.config.Namespaces;
import com.facishare.paas.task.calculate.model.CustomDataChangeMessage;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.rocketmq.common.message.MessageExt;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 监听自定义对象db变化
 * Created by <PERSON><PERSON><PERSON> on 2018/10/15
 */
public abstract class MetadataObjectDataChangeListener extends BaseListener<CustomDataChangeMessage> {

    private static final String OBJECT_DATA_BATCH_TOPIC = "object-data-batch";
    private static final String SOURCE_APP_PROPERTY = "sourceApp";

    @Override
    protected CustomDataChangeMessage parseMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        CustomDataChangeMessage customDataChangeMessage = JSON.parseObject(body, CustomDataChangeMessage.class);
        customDataChangeMessage.setMessageId(message.getMsgId());
        customDataChangeMessage.setOriginalBornTimestamp(message.getBornTimestamp());
        customDataChangeMessage.setBatch("batch-object-data-tag".equals(message.getTags()));
        boolean fromOpTool = isFromOpTool(message, customDataChangeMessage);
        customDataChangeMessage.setFromOpTool(fromOpTool);
        return customDataChangeMessage;
    }

    /**
     * 判断消息是否来自实施工具
     * 
     * @param message                 RocketMQ消息
     * @param customDataChangeMessage 自定义数据变更消息
     * @return true表示来自实施工具，false表示不是
     */
    private static boolean isFromOpTool(MessageExt message, CustomDataChangeMessage customDataChangeMessage) {
        // 1. 检查消息主题
        if (OBJECT_DATA_BATCH_TOPIC.equals(message.getTopic())) {
            return true;
        }

        // 2. 检查消息属性
        if (customDataChangeMessage.isDirectDelete() || customDataChangeMessage.fromCalculationJobOrOpTool()) {
            return true;
        }

        if (UdobjGrayConfig.isAllow("enhanceOptoolJudge", customDataChangeMessage.getTenantId())) {
            // 3. 检查数据源
            if (CalculateConfig.isOptoolDataSource(customDataChangeMessage.getDataSource())) {
                return true;
            }

            // 4. 检查应用来源
            String sourceApp = MessageHelper.getProperty(message, SOURCE_APP_PROPERTY);
            if (CalculateConfig.isOptoolApp(sourceApp)) {
                return true;
            }

            // 5. 检查TraceId
            String traceId = MessageHelper.getTraceId(message);
            return CalculateConfig.isOptoolTrace(traceId);
        }

        return false;
    }

    @Override
    protected String getArgDesc(CustomDataChangeMessage arg) {
        List<String> dataIds = arg.getBody().stream().map(CustomDataChangeMessage.DataContent::getObjectId)
                .collect(Collectors.toList());
        return new ReflectionToStringBuilder(arg, ToStringStyle.NO_CLASS_NAME_STYLE)
                .setExcludeFieldNames("body")
                .append(dataIds)
                .toString();
    }

    @Override
    protected boolean isSkipMessage(String tenantId, MessageExt message) {
        return !Namespaces.currentNamespace().isNamespaceTenant(tenantId, message);
    }

    @Override
    protected boolean skip(CustomDataChangeMessage arg, MessageExt message) {
        if (arg.bodyEmpty()) {
            return true;
        }
        String objectApiName = arg.getObjectApiName();
        // 忽略聚合规则和聚合值的变更消息
        if (Utils.AGGREGATE_RULE_API_NAME.equals(objectApiName)
                || Utils.AGGREGATE_VALUE_OBJ_API_NAME.equals(objectApiName)) {
            return true;
        }
        // 过滤黑名单对象
        if (CalculateConfig.isInMetadataObjectBlacklist(arg.getOp(), arg.getTenantId(), objectApiName)) {
            return true;
        }
        return isSkipMessage(arg.getTenantId(), message);
    }

    @Override
    protected String getTenantId(CustomDataChangeMessage arg) {
        return arg.getTenantId();
    }

    @Override
    protected void fillTraceContext(CustomDataChangeMessage arg) {
        String tenantId = arg.getTenantId();
        TraceContext traceContext = TraceContext.get();
        traceContext.setEi(tenantId);
    }
}
