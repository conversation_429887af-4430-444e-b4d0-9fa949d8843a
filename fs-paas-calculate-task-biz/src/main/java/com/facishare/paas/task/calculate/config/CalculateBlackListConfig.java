package com.facishare.paas.task.calculate.config;

import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;

import java.util.Set;

/**
 * Created by zhouwr on 2019/7/3
 */
@Slf4j
public class CalculateBlackListConfig {

    public static Set<String> batchObjectDataBlackList = Sets.newHashSet();

    static {
        ConfigFactory.getConfig("fs-paas-calculate-task-black-list", config -> {
            log.warn("reload config {},content:{}", config.getName(), config.getString());
            batchObjectDataBlackList = parseSetConfig(config, "batch-object-data.blackList");
        });
    }

    private static Set<String> parseSetConfig(IConfig config, String key) {
        String value = config.get(key);
        if (Strings.isNullOrEmpty(value)) {
            return Sets.newHashSet();
        }
        return Sets.newHashSet(Splitter.on(",").omitEmptyStrings().trimResults().split(value));
    }

    public static boolean isBlackTenantId(String tenantId) {
        return batchObjectDataBlackList.contains(tenantId);
    }
}
