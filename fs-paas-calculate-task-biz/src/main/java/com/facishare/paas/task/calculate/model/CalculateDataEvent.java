package com.facishare.paas.task.calculate.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.task.calculate.util.QueueTypes;
import com.fxiaoke.dispatcher.common.BaseEvent;
import com.fxiaoke.dispatcher.common.Constants;
import com.fxiaoke.dispatcher.common.MessageField;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CalculateDataEvent extends BaseEvent {

    public static final String DEFAULT_NAMESPACE = "default";

    @MessageField(name = "dataId")
    private String dataId;

    @MessageField(name = "apiName")
    private String objectApiName;

    @MessageField(name = "fieldType")
    private String fieldType;

    @MessageField(name = "fieldNames", addToSet = true)
    private Set<String> fieldApiNames;

    @MessageField(name = "eventId")
    private String eventId;

    /**
     * 数据操作类型
     */
    @MessageField(name = "action")
    private String action;

    /**
     * 消息来源（manual-手工；batch-批量；optool-实施工具；CALCULATION_JOB-全量计算）
     */
    @MessageField(name = "qts", addToSet = true)
    private Set<String> queueTypes;

    /**
     * 全量计算任务的id，需要聚合到一起
     */
    @MessageField(name = "jobIds", addToSet = true)
    private Set<String> jobIds;

    @MessageField(name = "oon")
    private String originalObjectApiName;

    @MessageField(name = "omid")
    private String originalMessageId;

    @MessageField(name = "ombt")
    private Long originalBornTimestamp;

    public String topicWithoutNamespace() {
        if (StringUtils.endsWith(getTopic(), "@" + DEFAULT_NAMESPACE)) {
            return StringUtils.substringBeforeLast(getTopic(), "@");
        }
        return getTopic();
    }

    public String topicWithNamespace() {
        if (StringUtils.endsWith(getTopic(), "@" + DEFAULT_NAMESPACE)) {
            return getTopic();
        }
        return StringUtils.joinWith("@", getTopic(), DEFAULT_NAMESPACE);
    }

    @Override
    public BaseEvent merge(BaseEvent other) {
        CalculateDataEvent event = (CalculateDataEvent) other;
        this.fieldApiNames.addAll(event.getFieldApiNames());
        this.queueTypes.addAll(event.getQueueTypes());
        if (CollectionUtils.notEmpty(event.getJobIds())) {
            if (Objects.isNull(this.jobIds)) {
                this.jobIds = Sets.newHashSet();
            }
            this.jobIds.addAll(event.getJobIds());
        }
        if (other.getPriority() < this.getPriority()) {
            this.setPriority(other.getPriority());
        }
        if (other.getDispatchTime() < this.getDispatchTime()) {
            this.setDispatchTime(other.getDispatchTime());
        }
        return this;
    }

    @Override
    public BaseEvent parse(Document doc) {
        super.parse(doc);
        this.setDataId(doc.getString("dataId"));
        this.setObjectApiName(doc.getString("apiName"));
        this.setFieldType(doc.getString("fieldType"));
        this.setFieldApiNames(Sets.newHashSet(doc.getList("fieldNames", String.class)));
        this.setEventId(doc.getString("eventId"));
        this.setQueueTypes(Sets.newHashSet(doc.getList("qts", String.class)));
        this.setAction(doc.getString("action"));
        List<String> jobIds = doc.getList("jobIds", String.class);
        if (CollectionUtils.notEmpty(jobIds)) {
            this.setJobIds(Sets.newHashSet(jobIds));
        }
        this.setOriginalMessageId(doc.getString("omid"));
        this.setOriginalObjectApiName(doc.getString("oon"));
        this.setOriginalBornTimestamp(doc.getLong("ombt"));
        return this;
    }

    public void markDone() {
        this.setStatus(Constants.EventStatus.STATUS_DONE);
    }

    public void markError() {
        this.setStatus(Constants.EventStatus.STATUS_ERROR);
    }

    public CalculateDataEvent copy() {
        return JacksonUtils.fromJson(JacksonUtils.toJson(this), CalculateDataEvent.class);
    }

    public boolean sameGroup(CalculateDataEvent event) {
        if (!getTenantId().equals(event.getTenantId())) {
            return false;
        }
        if (!getObjectApiName().equals(event.getObjectApiName())) {
            return false;
        }
        if (!CollectionUtils.isEqual(getFieldApiNames(), event.getFieldApiNames())) {
            return false;
        }
        return CollectionUtils.isEqual(getJobIds(), event.getJobIds());
    }

    public static List<CalculateDataGroup> groups(List<CalculateDataEvent> events) {
        List<List<CalculateDataEvent>> eventGroups = Lists.newArrayList();
        events.forEach(event -> {
            Optional<List<CalculateDataEvent>> groupOpt = eventGroups.stream().filter(x -> x.get(0).sameGroup(event)).findFirst();
            if (groupOpt.isPresent()) {
                groupOpt.get().add(event);
            } else {
                eventGroups.add(Lists.newArrayList(event));
            }
        });
        return eventGroups.stream().map(CalculateDataEvent::group).collect(Collectors.toList());
    }

    public static CalculateDataGroup group(List<CalculateDataEvent> events) {
        CalculateDataGroup group = events.get(0).toGroup();
        for (int i = 1; i < events.size(); i++) {
            events.get(i).merge(group);
        }
        return group;
    }

    private CalculateDataGroup toGroup() {
        return CalculateDataGroup.builder()
                .tenantId(getTenantId())
                .objectApiName(getObjectApiName())
                .dataIds(Sets.newHashSet(getDataId()))
                .fieldApiNames(Sets.newHashSet(getFieldApiNames()))
                .fieldType(getFieldType())
                .eventId(getEventId())
                .action(getAction())
                .queueType(QueueTypes.merge(getQueueTypes()))
                .queueIndex(topicWithoutNamespace())
                .jobIds(getJobIds())
                .messageIds(Sets.newHashSet(getId().toHexString()))
                .bornTimestamp(getCreateTime())
                .originalObjectApiName(getOriginalObjectApiName())
                .originalMessageId(getOriginalMessageId())
                .originalBornTimestamp(getOriginalBornTimestamp())
                .build();
    }

    private void merge(CalculateDataGroup group) {
        group.getDataIds().add(getDataId());
        group.getMessageIds().add(getId().toHexString());
        Set<String> queueTypes = Sets.newHashSet(group.getQueueType());
        queueTypes.addAll(this.getQueueTypes());
        group.setQueueType(QueueTypes.merge(queueTypes));
        group.addJobIds(this.getJobIds());
    }

    public static List<CalculateDataEvent> filterByGroup(List<CalculateDataEvent> events, CalculateDataGroup group) {
        return events.stream()
                .filter(x -> group.getMessageIds().contains(x.getId().toHexString()))
                .collect(Collectors.toList());
    }
}
