package com.facishare.paas.task.calculate.listener;

import com.facishare.paas.task.calculate.comsumer.CalculationJobConsumer;
import com.facishare.paas.task.calculate.model.CalculationJobMessage;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 消费预处理之后的全量计算消息
 * <p>
 * create by <PERSON><PERSON><PERSON> on 2020/11/10
 */
public class CalculateTaskJobListener extends AbstractCalculationJobListener {
    @Autowired
    private CalculationJobConsumer calculationJobConsumer;

    @Override
    protected boolean needResend(MessageExt message) {
        return calculateTaskApplication.isReSendMessage(message);
    }

    @Override
    protected void doConsume(CalculationJobMessage arg) {
        calculationJobConsumer.execute(arg);
    }

    @Override
    protected void customInitMessage(CalculationJobMessage calculateJobMQMessage, MessageExt message) {

    }
}
