package com.facishare.paas.task.calculate.comsumer;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.AggregateRule;
import com.facishare.paas.appframework.metadata.AggregateValue;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.task.calculate.model.AggregateValueHistoryCalculateMessage;
import com.facishare.paas.task.calculate.service.AggregateValueService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class AggregateValueHistoryCalculateConsumer implements Consumer<AggregateValueHistoryCalculateMessage> {

    @Autowired
    private AggregateValueService aggregateValueService;

    @Override
    public void execute(AggregateValueHistoryCalculateMessage message) {
        AggregateRule aggregateRule = aggregateValueService.findAggregateRuleById(message.getTenantId(), message.getAggregateRuleId());
        List<AggregateValue> aggregateValueList = aggregateValueService.aggregateByDate(message.getTenantId(), aggregateRule,
                message.getAggregateDate());
        //将历史聚合值更新为0
        updateHistoryAggregateValues(message, aggregateRule, aggregateValueList);
        aggregateValueService.bulkUpsertAggregateValueById(message.getTenantId(), aggregateValueList);
        //最后一条消息消费完之后更新聚合规则的计算状态为已完成
        if (message.isLast()) {
            aggregateValueService.updateAggregateRuleCalculateStatus(message.getTenantId(), message.getAggregateRuleId(),
                    AggregateRule.CalculateStatus.COMPLETED);
        }
    }

    private void updateHistoryAggregateValues(AggregateValueHistoryCalculateMessage message, AggregateRule aggregateRule, List<AggregateValue> aggregateValueList) {
        String idOffset = null;
        int batchDataNum;
        // 构建 searchQuery
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery())
                .addFilter(Operator.EQ, AggregateValue.AGGREGATE_RULE_ID, aggregateRule.getId())
                .addFilter(Operator.EQ, AggregateValue.AGGREGATE_DATE, String.valueOf(message.getAggregateDate()));
        //不需要引用字段
        queryExt.setNeedReturnQuote(false);
        //不需要数据总数
        queryExt.setNeedReturnCountNum(false);
        queryExt.setOffset(0);
        queryExt.setLimit(200);
        queryExt.resetOrderBy(Lists.newArrayList(new OrderBy(IObjectData.ID, false)));
        do {
            // 重置 id 筛选条件
            queryExt.resetSearchTemplateQuery(idOffset);
            // 查询数据
            QueryResult<AggregateValue> queryResult = aggregateValueService.findBySearchQuery(message.getTenantId(), queryExt.toSearchTemplateQuery());
            List<AggregateValue> queryData = queryResult.getData();
            if (CollectionUtils.notEmpty(queryData)) {
                batchDataNum = queryData.size();
                idOffset = queryData.get(batchDataNum - 1).getId();
                //根据唯一索引匹配数据id
                aggregateValueList.stream()
                        .filter(x -> Strings.isNullOrEmpty(x.getId()))
                        .forEach(x -> queryResult.getData().stream().filter(y -> y.keyEqual(x)).findFirst().ifPresent(y -> x.setId(y.getId())));
                queryData = queryData.stream()
                        .filter(x -> aggregateValueList.stream().noneMatch(y -> y.keyEqual(x)))
                        .peek(x -> {
                            if (aggregateRule.isMaxOrMinWay()) {
                                x.setAggregateValue(null);
                            } else {
                                x.setAggregateValue(0);
                            }
                            x.setAggregateCountValue(0);
                        })
                        .collect(Collectors.toList());
                aggregateValueService.bulkUpdateAggregateValue(message.getTenantId(), queryData);
            } else {
                batchDataNum = 0;
            }
        } while (batchDataNum > 0 && idOffset != null);
    }
}
