package com.facishare.paas.task.calculate.comsumer;

import com.facishare.paas.appframework.metadata.MetaDataService;
import com.facishare.paas.appframework.metadata.QuoteValueService;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.task.calculate.model.CalculateDataChangeMessage;
import com.facishare.paas.task.calculate.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 接收新topic 不同tag下的消息
 * Created by liwei on 2018/10/16
 */
@Service
@Slf4j
public class CalculateDataChangeConsumer implements Consumer<CalculateDataChangeMessage> {

    @Autowired
    private DescribePackageService describePackageService;
    @Autowired
    private ExpressionCalculateLogicService expressionCalculateLogicService;
    @Autowired
    private MetaDataService metaDataService;
    @Autowired
    private IObjectDataService objectDataService;
    @Autowired
    private QuoteValueService quoteValueService;
    @Autowired
    private DataPackageService dataPackageService;
    @Autowired
    private CalculateEventService calculateEventService;
    @Autowired
    private SendMessageService sendMessageService;

    @Override
    public void execute(CalculateDataChangeMessage message) {
        CalculateDataChangeCalculator.builder()
                .describePackageService(describePackageService)
                .expressionCalculateLogicService(expressionCalculateLogicService)
                .metaDataService(metaDataService)
                .objectDataService(objectDataService)
                .quoteValueService(quoteValueService)
                .dataPackageService(dataPackageService)
                .calculateEventService(calculateEventService)
                .sendMessageService(sendMessageService)
                .dataChangeMessage(message)
                .build()
                .doCalculate();
    }

}
