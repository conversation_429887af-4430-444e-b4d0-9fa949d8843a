package com.facishare.paas.task.calculate.monitor;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.timezone.DateTimeFormat;
import com.facishare.paas.timezone.DateTimeFormatUtils;
import com.facishare.paas.timezone.TimeZoneContext;
import com.google.common.base.Joiner;
import lombok.*;
import org.jetbrains.annotations.NotNull;

import java.text.MessageFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2023/2/7
 */
@Data
@Builder
public class MonitorData implements Comparable<MonitorData> {
    private String tenantId;
    private String ea;
    private String topic;
    private Integer mode;
    private Integer total;
    /**
     * top10的对象
     */
    @Singular
    private List<ObjectInfo> objectInfos;
    private Long dispatchTime;
    private Long delayTime;

    private static final Joiner joiner = Joiner.on(",");

    public static String toFormatStr(Collection<MonitorData> monitorDataList) {
        if (CollectionUtils.empty(monitorDataList)) {
            return "";
        }
        String nowTime = String.format("告警时间：%s\n", formatTime(System.currentTimeMillis(), TimeZoneContext.DEFAULT_TIME_ZONE)); //ignoreI18n
        return monitorDataList.stream()
                .sorted()
                .map(MonitorData::toFormatStr)
                .collect(Collectors.joining("\n-------------\n", nowTime, ""));
    }

    public String toFormatStr() {
        return MessageFormat.format("企业EA：{0}\n" + //ignoreI18n
                        "企业EI：{1}\n" + //ignoreI18n
                        "所属队列：{2}\n" + //ignoreI18n
                        "队列模式：{3}\n" + //ignoreI18n
                        "堆积的消息数：{4}\n" + //ignoreI18n
                        "top对象：{5}\n" + //ignoreI18n
                        "原计划消费时间：{6}\n" + //ignoreI18n
                        "延迟时间:{7}\n", ea, tenantId, topic, getModeStr(mode), total, //ignoreI18n
                getObjectInfoStr(), formatDateTime(dispatchTime), formatDuration(delayTime));
    }

    private String getObjectInfoStr() {
        if (CollectionUtils.empty(objectInfos)) {
            return "";
        }
        return objectInfos.stream()
                .map(it -> MessageFormat.format("{0}({1})", it.getDescribeApiName(), it.getTotal()))
                .collect(Collectors.joining(","));
    }

    public static String getModeStr(Integer mode) {
        if (Objects.isNull(mode)) {
            return "";
        }
        switch (mode) {
            case 1:
                return "fast";
            case 2:
                return "slow";
            case 3:
                return "batch";
            default:
                return "";
        }
    }

    private static String formatDuration(Long delayTime) {
        if (Objects.isNull(delayTime) || delayTime <= 0) {
            return "";
        }
        LocalDateTime dateTime = Instant.ofEpochMilli(delayTime).atZone(ZoneId.of("GMT+0")).toLocalDateTime();
        StringBuilder result = new StringBuilder();
        if (dateTime.getYear() > 1970) {
            result.append(dateTime.getYear() - 1970).append("年"); //ignoreI18n
        }
        if (dateTime.getDayOfYear() > 1) {
            result.append(dateTime.getDayOfYear()).append("天"); //ignoreI18n
        }
        if (dateTime.getHour() > 0) {
            result.append(dateTime.getHour()).append("小时"); //ignoreI18n
        }
        if (dateTime.getMinute() > 0) {
            result.append(dateTime.getMinute()).append("分"); //ignoreI18n
        }
        if (dateTime.getSecond() > 0) {
            result.append(dateTime.getSecond()).append("秒"); //ignoreI18n
        }
        return result.toString();
    }

    private static String formatTime(Long delayTime, ZoneId timeZone) {
        if (Objects.isNull(delayTime)) {
            return "";
        }
        return DateTimeFormat.TIME.convertFromTimestamp(delayTime, timeZone);
    }

    private String formatDateTime(Long dispatchTime) {
        if (Objects.isNull(dispatchTime)) {
            return "";
        }
        return DateTimeFormatUtils.format(dispatchTime, TimeZoneContext.DEFAULT_TIME_ZONE, DateTimeFormat.DATE_TIME.getType());
    }

    @Override
    public int compareTo(@NotNull MonitorData o) {
        if (Objects.isNull(tenantId) || Objects.isNull(o.tenantId)) {
            return 0;
        }
        if (Objects.equals(tenantId, o.tenantId)) {
            if (Objects.isNull(mode) || Objects.isNull(o.mode)) {
                return 0;
            }
            return mode.compareTo(o.mode);
        }
        return tenantId.compareTo(o.tenantId);
    }

    @Getter
    @AllArgsConstructor(staticName = "of")
    @ToString
    public static class ObjectInfo {
        private String describeApiName;
        private Integer total;
    }

}
