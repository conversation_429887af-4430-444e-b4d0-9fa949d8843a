package com.facishare.paas.task.calculate.util;

import lombok.Builder;

import java.util.Collection;
import java.util.Objects;
import java.util.Set;

import static com.facishare.paas.task.calculate.util.QueueTypes.BATCH;
import static com.facishare.paas.task.calculate.util.QueueTypes.MANUAL;

@Builder
public class PriorityCalculator {
    private String tenantId;
    private String objectApiName;
    private Collection<String> fieldApiNames;
    private String queueType;

    public int calculate() {
        int basePriority = getBasePriority();
        return fieldApiNames.stream().map(x -> {
            int offset = getOffset(x);
            return basePriority + offset;
        }).min(Integer::compareTo).get();
    }

    /**
     * 计算基准优先级。默认的优先级区间划分如下：<p>
     * [0,100) Fast队列<p>
     * [100,1000) Slow队列<p>
     * [1000,1000+) Batch队列<p>
     * 其中数字越小表示优先级越高，为了保证消息的分区被降级时相对优先级不变，Slow的基准优先级设为200，Batch的基准优先级设为2000，这样消息从
     * Fast分区转到Slow分区时优先级仍然排在前100名，消息从Slow分区转到Batch分区时优先级仍然排在前1000名。
     *
     * @return
     */
    private int getBasePriority() {
        switch (this.queueType) {
            case MANUAL:
                return 0;
            case BATCH:
                return 200;
            default:
                return 2000;
        }
    }

    /**
     * 计算优先级偏移量，计算规则如下：<p>
     * [0,20) 订单相关对象<p>
     * [20,50) 其他预设对象<p>
     * [50,100) 自定义对象
     *
     * @return
     */
    private int getOffset(String fieldApiName) {
        int hash = Math.abs(Objects.hash(this.tenantId, this.objectApiName, fieldApiName));
        if (this.objectApiName.contains("Order")) {
            return hash % 20;
        }
        if (!this.objectApiName.contains("__c")) {
            return 20 + hash % 30;
        }
        return 50 + hash % 50;
    }
}
