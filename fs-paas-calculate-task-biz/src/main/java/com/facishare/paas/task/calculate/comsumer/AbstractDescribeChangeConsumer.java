package com.facishare.paas.task.calculate.comsumer;

import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.QuoteExt;
import com.facishare.paas.appframework.metadata.expression.Expression;
import com.facishare.paas.appframework.metadata.expression.ExpressionFactory;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.Quote;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.task.calculate.model.FieldChangeMessage;
import com.facishare.paas.task.calculate.service.DescribePackageService;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Set;

/**
 * Created by zhouwr on 2019/6/3
 */
public abstract class AbstractDescribeChangeConsumer implements Consumer<FieldChangeMessage> {

    @Autowired
    protected DescribeLogicService describeLogicService;

    @Autowired
    protected DescribePackageService describePackageService;

    protected void invalidateRefObjectGraphs(String tenantId, IObjectDescribe describe, FieldDescribeExt fieldExt) {
        Set<String> refObjectApiNames = Sets.newHashSet();
        if (fieldExt.isFormula()) {
            Expression expression = ExpressionFactory.createExpression(describe, fieldExt.getFieldDescribe(), true);
            refObjectApiNames.addAll(expression.getDependentObjectAPINames());
        } else if (fieldExt.isCountField()) {
            Count count = fieldExt.getFieldDescribe();
            refObjectApiNames.add(count.getSubObjectDescribeApiName());
        } else if (fieldExt.isQuoteField()) {
            Quote quote = fieldExt.getFieldDescribe();
            String refFieldName = QuoteExt.of(quote).parseQuoteField().getKey();
            ObjectDescribeExt.of(describe).getFieldDescribeSilently(refFieldName).ifPresent(refField -> {
                String targetApiName = (String) refField.get(ObjectReferenceFieldDescribe.TARGET_API_NAME);
                if (!Strings.isNullOrEmpty(targetApiName)) {
                    refObjectApiNames.add(targetApiName);
                }
            });
        }
        describePackageService.invalidateCache(tenantId, refObjectApiNames);
    }

}
