package com.facishare.paas.task.calculate.comsumer;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.task.calculate.model.FieldChangeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2019/6/3
 */
@Slf4j
@Service
public class ObjectChangeConsumer extends AbstractDescribeChangeConsumer {

    @Override
    public void execute(FieldChangeMessage message) {
        if (CollectionUtils.empty(message.getBody())) {
            return;
        }
        Set<String> objectApiNames = message.getBody().stream().map(x -> x.getDescribeApiName()).collect(Collectors.toSet());
        describePackageService.invalidateCache(message.getTenantId(), objectApiNames);
        if (FieldChangeMessage.Ops.DESCRIBE_DELETE.equals(message.getOp())) {
            return;
        }
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjects(message.getTenantId(), objectApiNames);
        log.info("object change ei:{},messageId:{},objApi-Version:{}",
                message.getTenantId(), message.getMessageId(),
                describeMap.entrySet().stream().map(x -> x.getKey() + "-" + x.getValue().getVersion()).collect(Collectors.toList()));
        describeMap.forEach((apiName, describe) -> ObjectDescribeExt.of(describe).getActiveFieldDescribes().stream()
                .map(x -> FieldDescribeExt.of(x))
                .forEach(x -> invalidateRefObjectGraphs(message.getTenantId(), describe, x)));


    }

}
