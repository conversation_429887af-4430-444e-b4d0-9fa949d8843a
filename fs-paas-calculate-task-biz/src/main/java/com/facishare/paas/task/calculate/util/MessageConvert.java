package com.facishare.paas.task.calculate.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.facishare.paas.appframework.metadata.MetaDataService;
import com.facishare.paas.task.calculate.config.CalculateConfig;
import com.facishare.paas.task.calculate.config.CalculateTaskApplication;
import com.facishare.paas.task.calculate.model.CalculateDataChangeMessage;
import com.facishare.paas.task.calculate.service.SendMessageService;
import lombok.Builder;
import lombok.Data;
import lombok.Singular;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.common.message.Message;

import java.util.List;

import static com.facishare.paas.task.calculate.util.TopicConstant.BASE_BATCH_TOPIC;
import static com.facishare.paas.task.calculate.util.TopicConstant.BASE_MANUAL_TOPIC;
import static com.facishare.paas.task.calculate.util.TopicConstant.BASE_OP_TOOL_TOPIC;

/**
 * Created by liwei on 2019/12/23
 */
@Slf4j
@Builder
@Data
public class MessageConvert {
    private String tenantId;
    private String type;
    private boolean isBatch;
    private boolean fromOpTool;
    private boolean isDelayed;
    private CalculateDataChangeMessage dataChangeMessage;
    private SendMessageService sendMessageService;

    /**
     * 原始的对象 ApiName
     */
    private String originalDescribeApiName;
    /**
     * 原始的数据 Id
     */
    @Singular
    private List<String> originalObjectIds;

    public static String COUNT_TYPE = "COUNT";
    public static String FORMULA_TYPE = "FORMULA";
    public static String AGGREGATE_TYPE = "AGGREGATE";

    public static String BATCH_INDEX = "BATCH";
    public static String SLOW_INDEX = "SLOW";
    public static String DELAY_INDEX = "DELAY";

    public String convertAndSendMessage() {
        if (StringUtils.equals(type, FORMULA_TYPE) && StringUtils.equals(type, COUNT_TYPE)) {
            throw new RuntimeException("no match calculate type error.");
        }
        byte[] bytes = JSON.toJSONString(dataChangeMessage, SerializerFeature.WriteMapNullValue).getBytes();

        String topic = getTopic(tenantId);
        Message msg = new Message(topic, "", bytes);

        MessageSupport.setTenantId(msg, tenantId);
        MessageSupport.setDescribeApiName(msg, originalDescribeApiName);
        MessageSupport.setObjectIds(msg, originalObjectIds);
        MessageSupport.setOriginalBornTimestamp(msg, dataChangeMessage.getOriginalBornTimestamp());
        MessageSupport.setAction(msg, dataChangeMessage.getOp());

        if (COUNT_TYPE.equals(type)) {
            msg.setFlag(1);
        }

        //设置延迟时间
        if (isDelayed) {
            msg.setDelayTimeLevel(CalculateConfig.getDelayLevel(tenantId));
        }

        return sendMessageService.sendMessage(msg);
    }

    private String getTopic(String tenantId) {
        return CalculateTaskApplication.getTopic(getBaseTopic(), tenantId);
    }

    private String getBaseTopic() {
        if (fromOpTool) {
            return BASE_OP_TOOL_TOPIC;
        }
        return isBatch ? BASE_BATCH_TOPIC : BASE_MANUAL_TOPIC;
    }
}
