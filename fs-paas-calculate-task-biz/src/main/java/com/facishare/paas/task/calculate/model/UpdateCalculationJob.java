package com.facishare.paas.task.calculate.model;

import lombok.Builder;
import lombok.Data;

public interface UpdateCalculationJob {
    @Data
    @Builder
    class Arg{
        private String jobId;
        private String executeResult;
        private Long createTime;
        private Long lastModifiedTime;
        private boolean manual;
        private String lastMessageId;
        private Integer incrCompleteDataNum;
    }

    @Data
    class Result{
        private Integer code;
        private String message;
    }
}
