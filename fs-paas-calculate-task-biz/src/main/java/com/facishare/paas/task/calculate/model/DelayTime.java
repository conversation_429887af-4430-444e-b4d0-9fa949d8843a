package com.facishare.paas.task.calculate.model;

import lombok.Getter;

import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum DelayTime {
    LEVEL_1S(1, 1), LEVEL_5S(2, 5), LEVEL_10S(3, 10), LEVEL_30S(4, 30),
    LEVEL_1M(5, 60), LEVEL_2M(6, 120), LEVEL_3M(7, 180), LEVEL_4M(8, 240), LEVEL_5M(9, 300),
    LEVEL_6M(10, 360), LEVEL_7M(11, 420), LEVEL_8M(12, 480), LEVEL_9M(13, 540), LEVEL_10M(14, 600),
    LEVEL_20M(15, 1200), LEVEL_30M(16, 1800), LEVEL_1H(17, 3600), LEVEL_2H(18, 7200);

    @Getter
    private int level;
    @Getter
    private int time;

    private static Map<Integer, DelayTime> delayTimeMap;

    static {
        delayTimeMap = Stream.of(values()).collect(Collectors.toMap(DelayTime::getLevel, x -> x));
    }

    DelayTime(int level, int time) {
        this.level = level;
        this.time = time;
    }

    public static int getDelayTime(int level) {
        DelayTime delayTime = delayTimeMap.get(level);
        int delaySecond = 0;
        if (Objects.isNull(delayTime)) {
            return delaySecond;
        }
        if(delayTime.name().contains("S")){
            delaySecond = delayTime.getTime() - 5;
        }else{
            delaySecond = delayTime.getTime() - 60;
        }
        delaySecond = delaySecond > 0 ? delaySecond : 0;
        return delaySecond;
    }

    public static void main(String[] args) {
        int delayTime = getDelayTime(9);
        System.out.println(delayTime);
    }
}
