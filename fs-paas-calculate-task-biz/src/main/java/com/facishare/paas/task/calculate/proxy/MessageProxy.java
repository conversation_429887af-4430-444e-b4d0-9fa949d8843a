package com.facishare.paas.task.calculate.proxy;

import com.facishare.paas.task.calculate.model.SendMsgBySession;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * Created by zhaooju on 2023/2/10
 */
@RestResource(
        value = "FlowSession",
        desc = "企信消息服务", //ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.codec.AppDefaultCodeC"
)
public interface MessageProxy {

    @POST(value = "/session/v2/sendToSession", desc = "向指定的企信群组发送消息") //ignoreI18n
    String sendMsgBySession(@Body SendMsgBySession.Arg arg, @HeaderMap Map<String, String> header);
}
