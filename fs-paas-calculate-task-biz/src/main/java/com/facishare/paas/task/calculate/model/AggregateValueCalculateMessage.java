package com.facishare.paas.task.calculate.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AggregateValueCalculateMessage {
    private String tenantId;
    private String aggregateRuleId;
    private List<String> dataIdList;
    private List<AggregateCalculateNode> nodeList;

    private transient String messageId;
    private String originalMessageId;
}
