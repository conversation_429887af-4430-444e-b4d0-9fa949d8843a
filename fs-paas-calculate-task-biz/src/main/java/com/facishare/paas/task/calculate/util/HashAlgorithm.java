package com.facishare.paas.task.calculate.util;

import com.google.common.hash.Hashing;

import java.nio.charset.Charset;

/**
 * create by <PERSON><PERSON><PERSON> on 2020/11/05
 */
public enum HashAlgorithm {
    KETAMA_HASH() {
        @Override
        public Long getHash(String key) {
            byte[] bKey = computeMd5(key);
            return (long) (bKey[3] & 0xFF) << 24
                    | (long) (bKey[2] & 0xFF) << 16
                    | (long) (bKey[1] & 0xFF) << 8
                    | bKey[0] & 0xFF;
        }
    },

    FNV1_32_HASH() {
        @Override
        public Long getHash(String key) {
            long hash = FNV_32_INIT;
            int len = key.length();
            for (int i = 0; i < len; i++) {
                hash *= FNV_32_PRIME;
                hash ^= key.charAt(i);
            }
            return hash;
        }
    },

    FNV1A_32_HASH() {
        @Override
        public Long getHash(String key) {
            long hash = FNV_32_INIT;
            for (int i = 0; i < key.length(); i++) {
                hash ^= key.charAt(i);
                hash *= FNV_32_PRIME;
            }
            hash += hash << 13;
            hash ^= hash >> 7;
            hash += hash << 3;
            hash ^= hash >> 17;
            hash += hash << 5;
            return Math.abs(hash);
        }
    },

    NATIVE_HASH() {
        @Override
        public Long getHash(String key) {
            return (long) key.hashCode();
        }
    },
    ;

    private static final long FNV_32_INIT = 2166136261L;
    private static final long FNV_32_PRIME = 16777619;

    protected abstract Long getHash(String key);

    public final Long hash(String key) {
        long hash = getHash(key);
        // Convert to unsigned 32-bits
        return hash & 0xffffffffL;
    }

    public static byte[] computeMd5(String key) {
        return Hashing.md5().hashString(key, Charset.forName("UTF-8")).asBytes();
    }
}
