package com.facishare.paas.task.calculate.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.relation.FieldNode;
import com.facishare.paas.appframework.metadata.relation.RelateType;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * create by zhaoju on 2020/08/07
 */
public class CalculateNodeExt {
    @Getter
    private CalculateNode calculateNode;
    @Getter
    private boolean calculateAllData;

    private CalculateNodeExt(CalculateNode calculateNode, boolean calculateAllData) {
        this.calculateNode = calculateNode;
        this.calculateAllData = calculateAllData;
    }

    public static CalculateNodeExt of(CalculateNode calculateNode) {
        return new CalculateNodeExt(calculateNode, true);
    }

    public static Optional<CalculateNodeGroup> s2sFormulaNodes(List<CalculateNode> calculateNodes, String objectApiName) {
        List<CalculateNode> s2sFormulaNodes = calculateNodes.stream()
                .filter(it -> Objects.equals(it.getObjectApiName(), objectApiName))
                .filter(it -> it.getRelateType() == RelateType.S2S)
                .filter(it -> it.getNodeType() == FieldNode.NodeType.FORMULA)
                .sorted()
                .collect(Collectors.toList());

        CalculateNodeGroup result = null;
        for (CalculateNode node : s2sFormulaNodes) {
            if (result == null) {
                result = CalculateNodeGroup.of(node);
                continue;
            }
            result = result.merge(CalculateNodeExt.of(node));
        }
        return Optional.ofNullable(result);

    }

    /**
     * 将计算型字段排序并分组。
     * <P>排序规则：order小的排前面，order一样的按objectApiName排，objectApiName一样的需要计算所有数据的排在前面，其他字段排在后面。
     * <P>分组规则：先按objectApiName分组，相邻的统计字段分在一组，其他相邻的字段继续分组：calculateAllData相同的分在一组。
     *
     * @param calculateNodes
     * @return
     */
    public static List<CalculateNodeGroup> sortAndClassifyNodeList(List<CalculateNode> calculateNodes) {
        if (CollectionUtils.empty(calculateNodes)) {
            return Collections.emptyList();
        }

        List<CalculateNodeExt> sortedList = calculateNodes.stream().sorted().map(CalculateNodeExt::of).collect(Collectors.toList());
        // 对 sortedList 进行分组
        CalculateNodeGroup tmpGroup = null;
        List<CalculateNodeGroup> groups = Lists.newArrayList();
        for (CalculateNodeExt calculateNodeExt : sortedList) {
            if (tmpGroup == null) {
                tmpGroup = CalculateNodeGroup.of(calculateNodeExt);
                continue;
            }
            if (tmpGroup.isSameGroup(calculateNodeExt)) {
                tmpGroup.add(calculateNodeExt);
            } else {
                groups.add(tmpGroup);
                tmpGroup = CalculateNodeGroup.of(calculateNodeExt);
            }
        }
        // tmpGroup 不为空，则把 tmpGroup 追加到 groups 中
        if (tmpGroup != null) {
            groups.add(tmpGroup);
        }
        return groups;

    }

    public boolean isCountField() {
        return calculateNode.getNodeType() == FieldNode.NodeType.COUNT;
    }

    public boolean isCalculateValue() {
        return FieldNode.NodeType.CALCULATE_VALUE == calculateNode.getNodeType();
    }

    public CalculateDataEvent toEvent(CalculateDataChangeMessage message, String dataId) {
        return this.calculateNode.toEvent(message, dataId);
    }

    @ToString
    @EqualsAndHashCode
    public static class CalculateNodeGroup {
        private List<CalculateNodeExt> calculateNodes;
        @Getter
        private FieldNode.NodeType nodeType;
        @Getter
        private RelateType relateType;
        @Getter
        private String objectApiName;
        @Getter
        private boolean calculateAllData;

        private CalculateNodeGroup(List<CalculateNodeExt> calculateNodes, FieldNode.NodeType nodeType,
                                   RelateType relateType, String objectApiName, boolean calculateAllData) {
            this.calculateNodes = calculateNodes;
            this.nodeType = nodeType;
            this.relateType = relateType;
            this.objectApiName = objectApiName;
            this.calculateAllData = calculateAllData;
        }

        public static CalculateNodeGroup of(List<CalculateNode> calculateNodes) {
            CalculateNode calculateNode = calculateNodes.get(0);
            List<CalculateNodeExt> calculateNodeExtList = calculateNodes.stream().map(CalculateNodeExt::of).collect(Collectors.toList());
            return new CalculateNodeGroup(calculateNodeExtList, calculateNode.getNodeType(), calculateNode.getRelateType(),
                    calculateNode.getObjectApiName(), true);
        }

        public static CalculateNodeGroup of(CalculateNode calculateNode) {
            return of(CalculateNodeExt.of(calculateNode));
        }

        public static CalculateNodeGroup of(CalculateNodeExt calculateNodeExt) {
            return new CalculateNodeGroup(Lists.newArrayList(calculateNodeExt),
                    calculateNodeExt.getCalculateNode().getNodeType(),
                    calculateNodeExt.getCalculateNode().getRelateType(),
                    calculateNodeExt.getCalculateNode().getObjectApiName(),
                    calculateNodeExt.isCalculateAllData());
        }

        public List<CalculateNode> getCalculateNodes() {
            return calculateNodes.stream().map(CalculateNodeExt::getCalculateNode).collect(Collectors.toList());
        }

        public boolean isSameGroup(CalculateNodeExt other) {
            if (!Objects.equals(getObjectApiName(), other.getCalculateNode().getObjectApiName())) {
                return false;
            }
            if (getNodeType() != other.getCalculateNode().getNodeType()) {
                return false;
            }
            if (getRelateType() != other.getCalculateNode().getRelateType()) {
                return false;
            }
            return isCalculateAllData() == other.isCalculateAllData();
        }

        public String fieldType() {
            if (nodeType == FieldNode.NodeType.COUNT) {
                return IFieldType.COUNT;
            }
            if (nodeType == FieldNode.NodeType.QUOTE) {
                return IFieldType.QUOTE;
            }
            return IFieldType.FORMULA;
        }

        public boolean isCountField() {
            return getNodeType() == FieldNode.NodeType.COUNT;
        }

        public boolean add(CalculateNodeExt calculateNodeExt) {
            return calculateNodes.add(calculateNodeExt);
        }

        public CalculateNodeGroup merge(CalculateNodeExt calculateNodeExt) {
            if (isEmpty()) {
                return CalculateNodeGroup.of(calculateNodeExt);
            }
            if (isSameGroup(calculateNodeExt)) {
                add(calculateNodeExt);
            }
            return this;
        }

        public List<CalculateNodeGroup> groupingByReferenceField() {
            CalculateNodeGroup nodesWithoutReferenceField = filter(x -> Strings.isNullOrEmpty(x.getCalculateNode().getReferenceFieldName()));
            Map<String, List<CalculateNodeExt>> nodeGroups = this.calculateNodes.stream()
                    .filter(x -> !Strings.isNullOrEmpty(x.getCalculateNode().getReferenceFieldName()))
                    .collect(Collectors.groupingBy(x -> x.getCalculateNode().getReferenceFieldName()));
            List<CalculateNodeGroup> result = nodeGroups.values().stream()
                    .map(x -> new CalculateNodeGroup(x, nodeType, relateType, objectApiName, calculateAllData))
                    .collect(Collectors.toList());
            if(!nodesWithoutReferenceField.isEmpty()) {
                result.add(nodesWithoutReferenceField);
            }
            return result;
        }

        public boolean isEmpty() {
            return CollectionUtils.empty(calculateNodes);
        }

        public boolean isQuoteField() {
            return FieldNode.NodeType.QUOTE == nodeType;
        }

        public List<String> getFieldNames() {
            return getCalculateNodes().stream()
                    .map(CalculateNode::getFieldApiName)
                    .collect(Collectors.toList());
        }

        public boolean removeIf(Predicate<CalculateNodeExt> filter) {
            return calculateNodes.removeIf(filter);
        }

        public CalculateNodeGroup filter(Predicate<CalculateNodeExt> filter) {
            List<CalculateNodeExt> calculateNodeExts = calculateNodes.stream().filter(filter).collect(Collectors.toList());
            return new CalculateNodeGroup(calculateNodeExts, nodeType, relateType, objectApiName, calculateAllData);
        }

        public List<CalculateDataEvent> toEvents(CalculateDataChangeMessage message, List<String> dataIdList) {
            if (CollectionUtils.empty(calculateNodes) || CollectionUtils.empty(dataIdList)) {
                return Lists.newArrayList();
            }
            List<CalculateDataEvent> events = Lists.newArrayList();
            dataIdList.forEach(dataId -> events.addAll(calculateNodes.stream().map(x -> x.toEvent(message, dataId)).collect(Collectors.toList())));
            return events;
        }
    }

}
