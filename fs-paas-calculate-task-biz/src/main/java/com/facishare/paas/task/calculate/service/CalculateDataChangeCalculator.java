package com.facishare.paas.task.calculate.service;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.task.calculate.config.CalculateConfig;
import com.facishare.paas.task.calculate.model.CalculateDataChangeMessage;
import com.facishare.paas.task.calculate.model.CalculateNodeExt;
import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by liwei on 2018/11/13
 */
@Slf4j
public class CalculateDataChangeCalculator extends AbstractCalculator {
    private static String userId = "-10000";

    @Builder
    public CalculateDataChangeCalculator(DescribePackageService describePackageService,
                                         ExpressionCalculateLogicService expressionCalculateLogicService,
                                         MetaDataService metaDataService,
                                         IObjectDataService objectDataService,
                                         QuoteValueService quoteValueService,
                                         DataPackageService dataPackageService,
                                         CalculateEventService calculateEventService,
                                         SendMessageService sendMessageService,
                                         CalculateDataChangeMessage dataChangeMessage) {
        super(describePackageService, expressionCalculateLogicService, metaDataService, objectDataService, quoteValueService,
                dataPackageService, calculateEventService, sendMessageService, dataChangeMessage, Maps.newConcurrentMap());
    }

    @Override
    public void doCalculate() {
        //过滤黑名单中的字段
        dataChangeMessage.getCalculateNodes().removeIf(x -> CalculateConfig.isFieldInBlacklist(dataChangeMessage.getTenantId(),
                x.getObjectApiName(), x.getFieldApiName(), dataChangeMessage.queueType(), dataChangeMessage.getOp(), dataChangeMessage.getOriginalDescribeApiName()));
        if (CollectionUtils.empty(dataChangeMessage.getCalculateNodes())) {
            return;
        }

        String op = dataChangeMessage.getOp();
        ObjectAction action = ObjectAction.of(op);
        User user = User.builder().tenantId(dataChangeMessage.getTenantId()).userId(userId).build();
        computeReferredFields(user, dataChangeMessage.getDataIdList(), dataChangeMessage.getDescribeApiName(), dataChangeMessage.getCalculateNodes(), action);
    }

    /**
     * 老对象新建数据，生命状态为 normal 时，需要计算 状态字段（CALCULATE_VALUE）
     * ineffective 时不需要计算 状态字段（CALCULATE_VALUE）
     *
     * @param user
     * @param objectDataList
     * @param objectDescribe
     * @param nodeGroup
     * @param isCache
     * @param diffCalculateResult
     */
    @Override
    protected void calculateSelfFormulaField(User user, List<IObjectData> objectDataList, IObjectDescribe objectDescribe,
                                             CalculateNodeExt.CalculateNodeGroup nodeGroup, boolean isCache, boolean diffCalculateResult) {
        if (ObjectDescribeExt.isSFAObject(objectDescribe.getApiName())
                && ObjectAction.CREATE.getActionCode().equals(dataChangeMessage.getOp())) {
            CalculateNodeExt.CalculateNodeGroup calculateNodeGroup = nodeGroup.filter(CalculateNodeExt::isCalculateValue);
            if (!calculateNodeGroup.isEmpty()) {
                List<IObjectData> normalStatusDataList = objectDataList.stream()
                        .filter(x -> ObjectLifeStatus.NORMAL.equals(ObjectDataExt.of(x).getLifeStatus()))
                        .collect(Collectors.toList());
                super.calculateSelfFormulaField(user, normalStatusDataList, objectDescribe, calculateNodeGroup, isCache, diffCalculateResult);
                nodeGroup.removeIf(it -> calculateNodeGroup.getCalculateNodes().contains(it.getCalculateNode()));
            }
        }
        super.calculateSelfFormulaField(user, objectDataList, objectDescribe, nodeGroup, isCache, diffCalculateResult);
    }
}
