package com.facishare.paas.task.calculate.dispatcher;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.relation.*;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.task.calculate.model.CustomDataChangeMessage;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * create by zhao<PERSON> on 2020/03/26
 */
@Slf4j
@SuperBuilder
public class DeletedDispatcher extends FieldMetadataDispatcher {

    private boolean deleteDirect;

    @Override
    protected Set<NodeEdgePair> getCalculateNodeEdgePairs(List<String> fieldChanges, IObjectDescribe objectDescribe, FieldRelationGraph graph) {
        Set<NodeEdgePair> calculateNodeEdgePairs = super.getCalculateNodeEdgePairs(fieldChanges, objectDescribe, graph);
        // 作废不计算本对象字段
        Set<NodeEdgePair> result = calculateNodeEdgePairs.stream()
                .map(it -> {
                    RelateEdge relateEdge = it.getRelateEdge().exclude(relateEdgeNode -> relateEdgeNode.getRelateType() == RelateType.S2S && !isSpecialField(it.getFieldNode()));
                    return NodeEdgePair.of(it.getFieldNode(), relateEdge);
                })
                .filter(it -> CollectionUtils.notEmpty(it.getRelateEdgeNodes()))
                .collect(Collectors.toSet());
        return result;
    }

    /**
     * 订单的 order_status 需要计算
     *
     * @param fieldNode
     * @return
     */
    private boolean isSpecialField(FieldNode fieldNode) {
        return Utils.SALES_ORDER_API_NAME.equals(fieldNode.getObjectApiName()) && "order_status".equals(fieldNode.getFieldApiName());
    }

    @Override
    protected boolean includeInvalidData() {
        return true;
    }
}
