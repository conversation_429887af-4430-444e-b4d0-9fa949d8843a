package com.facishare.paas.task.calculate.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * Created by li<PERSON> on 2019/1/16
 */
@Data
public class FieldChangeMessage {
    private String name;
    private String op;
    private String tenantId;
    private List<Body> body;
    private transient String messageId;

    public boolean isFieldMessage() {
        return Names.OBJECT_FIELD.equals(name);
    }

    @Data
    public class Body {
        @JSONField(name = "describe_api_name")
        private String describeApiName;

        @J<PERSON><PERSON>ield(name = "object_describe_api_name")
        private String objectDescribeApiName;
        private List<Field> fields;

        @Data
        public class Field {
            @J<PERSON>NField(name = "field_api_name")
            private String fieldApiName;
            @JSONField(name = "field_type")
            private String fieldType;
            @JSONField(name = "target_describe_api_name")
            private String targetDescribeApiName;
        }
    }

    public interface Names {
        String OBJECT_DESCRIBE = "object_describe";
        String OBJECT_FIELD = "object_field";
    }

    public interface Ops {
        String DESCRIBE_DELETE = "describe_delete";
        String FIELD_DELETE = "field_delete";
    }

}
