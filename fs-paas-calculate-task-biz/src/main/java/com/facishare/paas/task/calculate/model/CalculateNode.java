package com.facishare.paas.task.calculate.model;

import com.facishare.paas.appframework.metadata.relation.FieldNode;
import com.facishare.paas.appframework.metadata.relation.RelateEdge;
import com.facishare.paas.appframework.metadata.relation.RelateType;
import com.facishare.paas.task.calculate.config.Namespaces;
import com.facishare.paas.task.calculate.util.DelayTimer;
import com.facishare.paas.task.calculate.util.PriorityCalculator;
import com.fxiaoke.dispatcher.common.Constants;
import com.fxiaoke.dispatcher.common.MessageHelper;
import com.google.common.base.Joiner;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.NonNull;

import java.util.Objects;

/**
 * Created by liwei on 2019/7/10
 */
@Data
public class CalculateNode implements Comparable<CalculateNode> {
    private String objectApiName;
    private String fieldApiName;
    private FieldNode.NodeType nodeType;
    private int order;
    private RelateType relateType;
    private String referenceFieldName;

    public CalculateNode() {
    }

    private CalculateNode(String objectApiName, String fieldApiName, FieldNode.NodeType nodeType, int order,
                          RelateType relateType, String referenceFieldName) {
        this.objectApiName = objectApiName;
        this.fieldApiName = fieldApiName;
        this.nodeType = nodeType;
        this.order = order;
        this.relateType = relateType;
        this.referenceFieldName = referenceFieldName;
    }

    public static CalculateNode of(FieldNode fieldNode, RelateEdge.RelateEdgeNode relateEdgeNode) {
        return new CalculateNode(fieldNode.getObjectApiName(), fieldNode.getFieldApiName(), fieldNode.getNodeType(),
                fieldNode.getOrder(), relateEdgeNode.getRelateType(), relateEdgeNode.getReferenceFieldName());
    }

    public static CalculateNode of(FieldNode fieldNode, RelateType relateType) {
        return new CalculateNode(fieldNode.getObjectApiName(), fieldNode.getFieldApiName(), fieldNode.getNodeType(),
                fieldNode.getOrder(), relateType, null);
    }

    public static CalculateNode of(CountDataInfo countDataInfo) {
        return new CalculateNode(countDataInfo.getObjectApiName(), countDataInfo.getFieldApiName(), FieldNode.NodeType.COUNT,
                0, countDataInfo.getRelateType(), countDataInfo.getReferenceFieldName());
    }

    public FieldNode toFieldNode() {
        FieldNode fieldNode = FieldNode.of(getObjectApiName(), getFieldApiName(), getOrder());
        fieldNode.setNodeType(getNodeType());
        return fieldNode;
    }

    @Override
    public int compareTo(@NonNull CalculateNode o) {
        if (this.order != o.order) {
            return this.order - o.order;
        }
        if (!Objects.equals(this.objectApiName, o.objectApiName)) {
            return this.objectApiName.compareTo(o.objectApiName);
        }
        return 0;
    }

    public CalculateDataEvent toEvent(CalculateDataChangeMessage message, String dataId) {
        String tenantId = message.getTenantId();
        String fieldType = this.nodeType.name();
        // 唯一ID(tenantId + objectDescribeApiName + dataId)
        String uniqKey = MessageHelper.md5(tenantId, this.objectApiName, dataId, fieldType);
        // 构建分类（tenantId + objectDescribeApiName）
        String category = Joiner.on('^').join(tenantId, this.objectApiName);

        // 根据tenantId查询topic名称
        String topic = Namespaces.getEventTopicByTenantIdAndObject(tenantId, this.objectApiName, dataId);
        long now = System.currentTimeMillis();
        String queueType = message.queueType();
        long dispatchTime = now + DelayTimer.getDelayTime(queueType, fieldType);
        int priority = PriorityCalculator.builder()
                .tenantId(tenantId)
                .objectApiName(this.objectApiName)
                .fieldApiNames(Sets.newHashSet(this.fieldApiName))
                .queueType(queueType)
                .build()
                .calculate();

        return CalculateDataEvent.builder()
                .status(Constants.EventStatus.STATUS_READY)
                .key(uniqKey)
                .topic(topic)
                .category(category)
                .priority(priority)
                .dispatchTime(dispatchTime)
                .createTime(now)
                .modifiedTime(now)
                .tenantId(tenantId)
                .objectApiName(this.objectApiName)
                .dataId(dataId)
                .fieldApiNames(Sets.newHashSet(this.fieldApiName))
                .fieldType(fieldType)
                .action(message.getOp())
                .queueTypes(Sets.newHashSet(queueType))
                .eventId(message.getEventId())
                .originalObjectApiName(message.getOriginalDescribeApiName())
                .originalBornTimestamp(message.getOriginalBornTimestamp())
                .originalMessageId(message.getOriginalMessageId())
                .build();
    }
}
