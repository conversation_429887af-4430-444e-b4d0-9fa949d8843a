package com.facishare.paas.task.calculate.config;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.task.calculate.config.CalculateConfig.Config;
import com.facishare.paas.task.calculate.util.MessageSupport;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;

import java.util.Collection;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Stream;

/**
 * Created by zhouwr on 2018/10/30
 */
@Slf4j
public abstract class CalculateGrayConfig {

    private volatile static int DELAY_TIME = 60;
    public static final Splitter CONFIG_SPLITTER = Splitter.on(",").omitEmptyStrings().trimResults();

    private static volatile Set<String> GRAY_TENANT_IDS = Sets.newHashSet();
    private static volatile Set<String> LAST_GRAY_TENANT_IDS = Sets.newHashSet();
    private static volatile Set<String> STAGE_TENANT_IDS = Sets.newHashSet();
    private static volatile Set<String> LAST_STAGE_TENANT_IDS = Sets.newHashSet();
    private static volatile Set<String> VIP_TENANT_IDS = Sets.newHashSet();
    private static volatile Set<String> LAST_VIP_TENANT_IDS = Sets.newHashSet();

    private static final AtomicLong lastModifiedTime = new AtomicLong();

    public static final long FIRST_LOAD_TIME_TAMP = 1L;

    static {
        ConfigFactory.getConfig(CalculateConfig.configName, conf -> {
            log.warn("reload config {},content:{}", CalculateConfig.configName, conf.getString());

            String content = new String(conf.getContent());
            Config config = JSON.parseObject(content, Config.class);

            Set<String> grayTmpTenantIds = getSetFromConfig(config.getGrayTenantIds());
            Set<String> stageTmpTenantIds = getSetFromConfig(config.getStageTenantIds());
//            Set<String> vipTmpTenantIds = getSetFromConfig(config.getVipTenantIds());

            DELAY_TIME = config.getDelayTime();

            LAST_GRAY_TENANT_IDS = GRAY_TENANT_IDS;
            GRAY_TENANT_IDS = grayTmpTenantIds;

            LAST_STAGE_TENANT_IDS = STAGE_TENANT_IDS;
            STAGE_TENANT_IDS = stageTmpTenantIds;

            LAST_VIP_TENANT_IDS = VIP_TENANT_IDS;
//            VIP_TENANT_IDS = vipTmpTenantIds;

            // 第一次加载的时候 timestamp 为 0L，需要将最后更新时间指定为 FIRST_LOAD_TIME_TAMP
            lastModifiedTime.getAndUpdate((timestamp -> {
                if (timestamp < FIRST_LOAD_TIME_TAMP) {
                    return FIRST_LOAD_TIME_TAMP;
                }
                return System.currentTimeMillis();
            }));
        });
    }

    private static long getLastModifiedTime() {
        return lastModifiedTime.get();
    }

    /**
     * @return 延迟生效的时间，默认 lastModifiedTime + 60s
     */
    private static long getDelayTime() {
        return lastModifiedTime.get() + DELAY_TIME * 1000;
    }

    private static Set<String> getSetFromConfig(String value) {
        if (Strings.isNullOrEmpty(value)) {
            return Sets.newHashSet();
        }
        return Sets.newHashSet(CONFIG_SPLITTER.split(value));
    }

    /**
     * 判断是否是计算灰度企业
     * <p>
     * bornTimestamp 大于 delayTime 直接使用 GRAY_TENANT_IDS 判断
     * bornTimestamp 介于 LastModifiedTime 和 delayTime 使用 GRAY_TENANT_IDS 和 lastGrayTenantIds判断
     * bornTimestamp 小于 LastModifiedTime 使用 LAST_GRAY_TENANT_IDS 判断
     *
     * @param tenantId
     * @param message
     * @return
     */
    public static boolean isGrayTenantId(String tenantId, MessageExt message) {
        long bornTimestamp = message.getBornTimestamp();
        if (bornTimestamp > getDelayTime()) {
            return isGrayTenantId(tenantId);
        }
        if (bornTimestamp > getLastModifiedTime()) {
            return isDelayGrayTenantId(tenantId);
        }
        return isLastGrayTenantIds(tenantId);
    }

    public static boolean isGrayTenantId(String tenantId) {
        return GRAY_TENANT_IDS.contains(tenantId);
    }

    /**
     * 配置文件生效有延迟，在配置文件生效后的一段时间内，新旧配置文件中的企业都需要处理
     *
     * @param tenantId
     * @return
     */
    private static boolean isDelayGrayTenantId(String tenantId) {
        return Stream.of(GRAY_TENANT_IDS, LAST_GRAY_TENANT_IDS).flatMap(Collection::stream).anyMatch(it -> Objects.equals(it, tenantId));
    }

    private static boolean isLastGrayTenantIds(String tenantId) {
        return LAST_GRAY_TENANT_IDS.contains(tenantId);
    }

    public static boolean isStageTenantId(String tenantId, MessageExt message) {
        long bornTimestamp = message.getBornTimestamp();
        if (bornTimestamp > getDelayTime()) {
            return isStageTenantId(tenantId);
        }
        if (bornTimestamp > getLastModifiedTime()) {
            return isDelayStageTenantId(tenantId);
        }
        return isLastStageTenantIds(tenantId);
    }

    private static boolean isLastStageTenantIds(String tenantId) {
        return LAST_STAGE_TENANT_IDS.contains(tenantId);
    }

    private static boolean isDelayStageTenantId(String tenantId) {
        return Stream.of(STAGE_TENANT_IDS, LAST_STAGE_TENANT_IDS).flatMap(Collection::stream).anyMatch(it -> Objects.equals(it, tenantId));
    }

    public static boolean isStageTenantId(String tenantId) {
        return STAGE_TENANT_IDS.contains(tenantId);
    }

    public static boolean isVipTenantId(String tenantId, MessageExt message) {
        long bornTimestamp = message.getBornTimestamp();
        if (bornTimestamp > getDelayTime()) {
            return isVipTenantId(tenantId);
        }
        if (bornTimestamp > getLastModifiedTime()) {
            return isDelayVipTenantId(tenantId);
        }
        return isLastVipTenantIds(tenantId);
    }

    private static boolean isLastVipTenantIds(String tenantId) {
        return LAST_VIP_TENANT_IDS.contains(tenantId);
    }

    private static boolean isDelayVipTenantId(String tenantId) {
        return Stream.of(VIP_TENANT_IDS, LAST_VIP_TENANT_IDS).flatMap(Collection::stream).anyMatch(it -> Objects.equals(it, tenantId));
    }

    public static boolean isVipTenantId(String tenantId) {
        return VIP_TENANT_IDS.contains(tenantId);
    }

    public static boolean isNormalTenantId(String tenantId, MessageExt message) {
        long bornTimestamp = message.getBornTimestamp();
        if (bornTimestamp > getDelayTime()) {
            return isNormalTenantId(tenantId);
        }
        if (bornTimestamp > getLastModifiedTime()) {
            return isDelayNormalTenantId(tenantId);
        }
        return isLastNormalTenantId(tenantId);
    }

    /**
     * gary、stage、vip 灰度名单中未发生改变的企业
     * 移入、移除 normal 的企业需要延迟消费
     *
     * @param tenantId
     * @return
     */
    private static boolean isDelayNormalTenantId(String tenantId) {
        return isNormalTenantId(tenantId) || isLastNormalTenantId(tenantId);
    }

    public static boolean isNeedReSendMessage(MessageExt message) {
        String tenantId = MessageSupport.getTenantId(message);
        return isVipTenantId(tenantId);
    }

    public static boolean isNormalTenantId(String tenantId) {
        return !isGrayTenantId(tenantId) && !isVipTenantId(tenantId) && !isStageTenantId(tenantId);
    }

    public static boolean isLastNormalTenantId(String tenantId) {
        return !isLastGrayTenantIds(tenantId) && !isLastStageTenantIds(tenantId) && !isLastVipTenantIds(tenantId);
    }
}
