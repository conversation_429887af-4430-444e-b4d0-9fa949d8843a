package com.facishare.paas.task.calculate.service;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.relation.FieldRelationGraph;
import com.facishare.paas.appframework.metadata.relation.FieldRelationGraphService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.cache.DescribeCache;
import com.facishare.paas.task.calculate.config.CalculateConfig;
import com.google.common.base.Joiner;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2019/1/15
 */
@Service
@Slf4j
public class DescribePackageService {

    private static final Joiner JOINER = Joiner.on("-");

    private Cache<String, FieldRelationGraph> localCache;

    @Autowired
    private DescribeLogicService describeLogicService;

    @Autowired
    private FieldRelationGraphService fieldRelationGraphService;

    @Autowired
    private DescribeCache describeCache;

    public DescribePackageService() {
        localCache = CacheBuilder.newBuilder()
                .expireAfterWrite(10, TimeUnit.MINUTES)
                .maximumSize(5000)
                .build();
    }

    public void invalidateCache(String tenantId, Collection<String> objectApiNames) {
        if (CollectionUtils.empty(objectApiNames)) {
            return;
        }
        objectApiNames.forEach(objectApiName -> {
            describeCache.purgeLocalCache(tenantId, objectApiName);
            log.warn("purge local cache tenantId:{},objectApiName:{}", tenantId, objectApiName);
            String key = buildKey(tenantId, objectApiName);
            localCache.invalidate(key);
            log.warn("invalidate graph cache tenantId:{},objectApiName:{}", tenantId, objectApiName);
        });
    }

    public Map<String, IObjectDescribe> fetchObjects(String tenantId, List<String> objectApiNames) {
        return describeLogicService.findObjects(tenantId, objectApiNames);
    }

    public IObjectDescribe fetchObject(String tenantId, String objectApiName) {
        return describeLogicService.findObjectUseThreadLocalCache(tenantId, objectApiName);
    }

    public FieldRelationGraph fetchGraph(IObjectDescribe describe) {
        if (!CalculateConfig.config.isGraphCache()) {
            return getGraphByRPC(describe);
        }
        String key = buildKey(describe.getTenantId(), describe.getApiName());
        try {
            return localCache.get(key, () -> {
                log.debug("fetch graph from rpc,tenantId:{},objectApiName:{}", describe.getTenantId(), describe.getApiName());
                FieldRelationGraph graph = getGraphByRPC(describe);
                graph.getDescribeMap().clear();
                return graph;
            });
        } catch (ExecutionException e) {
            log.error("fetchGraph error,tenantId:{},objectApiName:{}", describe.getTenantId(), describe.getApiName(), e);
            throw new RuntimeException(e);
        }
    }

    private FieldRelationGraph getGraphByRPC(IObjectDescribe describe) {
        return fieldRelationGraphService.buildReverseFullDependencyGraph(
                describe, null, true, false, true,
                true, true);
    }

    private String buildKey(String tenantId, String objectApiName) {
        return JOINER.join(tenantId, objectApiName);
    }
}
