package com.facishare.paas.task.calculate.util;

import com.facishare.paas.task.calculate.config.CalculateConfig;
import lombok.NonNull;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.Message;
import org.elasticsearch.common.Strings;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static com.facishare.paas.task.calculate.config.CalculateGrayConfig.CONFIG_SPLITTER;
import static com.facishare.paas.task.calculate.util.MessageConvert.*;

/**
 * create by z<PERSON><PERSON> on 2020/11/06
 */
@UtilityClass
public class MessageSupport {
    public static final String X_FS_EI = "x-fs-ei";
    public static final String X_FS_DESCRIBE_API_NAME = "x-fs-describe-api-name";
    public static final String X_FS_OBJECT_ID = "x-fs-object-id";
    public static final String X_FS_ORIGINAL_MESSAGE_ID = "x-fs-resend-original-message-id";
    public static final String X_FS_RESEND_TIMES = "x-fs-resend-times";
    public static final String X_FS_BROKER_NAME = "x-fs-broker-name";
    public static final String X_FS_ACTION_OP = "x-fs-action-op";
    public static final String X_FS_ORIGINAL_BORN_TIMESTAMP = "x-fs-original-born-timestamp";

    public Message setTenantId(@NonNull Message message, String tenantId) {
        putMessageProperty(message, X_FS_EI, tenantId);
        return message;
    }

    public String getTenantId(@NonNull Message message) {
        return message.getUserProperty(X_FS_EI);
    }

    public Message setDescribeApiName(@NonNull Message message, String describeApiName) {
        putMessageProperty(message, X_FS_DESCRIBE_API_NAME, describeApiName);
        return message;
    }

    public String getDescribeApiName(@NonNull Message message) {
        return message.getUserProperty(X_FS_DESCRIBE_API_NAME);
    }

    public Message setObjectIds(@NonNull Message message, Collection<String> objectIds) {
        putMessageProperty(message, X_FS_OBJECT_ID, String.join(",", objectIds));
        return message;
    }

    public List<String> getObjectIds(@NonNull Message message) {
        String objectIds = message.getUserProperty(X_FS_OBJECT_ID);
        if (Strings.isNullOrEmpty(objectIds)) {
            return Collections.emptyList();
        }
        return CONFIG_SPLITTER.splitToList(message.getUserProperty(X_FS_OBJECT_ID));
    }

    public String getType(@NonNull Message message) {
        if (message.getFlag() == 2) {
            return AGGREGATE_TYPE;
        }
        return message.getFlag() == 1 ? COUNT_TYPE : FORMULA_TYPE;
    }

    public Message setType(@NonNull Message message, String type) {
        if (AGGREGATE_TYPE.equals(type)) {
            message.setFlag(2);
        } else if (COUNT_TYPE.equals(type)) {
            message.setFlag(1);
        }
        return message;
    }

    public boolean isDelayed(@NonNull Message message) {
        int delayTimeLevel = message.getDelayTimeLevel();
        return delayTimeLevel > 0;
    }

    public Message setDelayTimeLevel(@NonNull Message message, String tenantId) {
        message.setDelayTimeLevel(CalculateConfig.getDelayLevel(tenantId));
        return message;
    }

    public Message setOriginalMessageId(@NonNull Message message, String originalMessageId) {
        putMessageProperty(message, X_FS_ORIGINAL_MESSAGE_ID, originalMessageId);
        return message;
    }

    public String getOriginalMessageId(@NonNull Message message) {
        return message.getUserProperty(X_FS_ORIGINAL_MESSAGE_ID);
    }

    public int getReSendTimes(@NonNull Message message) {
        String resendTimes = message.getUserProperty(X_FS_RESEND_TIMES);
        if (Strings.isNullOrEmpty(resendTimes)) {
            return 0;
        }
        return Integer.valueOf(resendTimes);
    }

    public Message setReSendTimes(@NonNull Message message, int reConsumeTimes) {
        putMessageProperty(message, X_FS_RESEND_TIMES, String.valueOf(reConsumeTimes));
        return message;
    }

    public Message setBrokerName(@NonNull Message message, String brokerName) {
        putMessageProperty(message, X_FS_BROKER_NAME, brokerName);
        return message;
    }

    public String getBrokerName(@NonNull Message message) {
        return message.getUserProperty(X_FS_BROKER_NAME);
    }

    public Message setAction(@NonNull Message message, String op) {
        putMessageProperty(message, X_FS_ACTION_OP, op);
        return message;
    }

    public String getAction(@NonNull Message message) {
        return message.getUserProperty(X_FS_ACTION_OP);
    }

    public Message setOriginalBornTimestamp(@NonNull Message message, Long originalBornTimestamp) {
        putMessageProperty(message, X_FS_ORIGINAL_BORN_TIMESTAMP, originalBornTimestamp == null ? null :
                String.valueOf(originalBornTimestamp));
        return message;
    }

    public Long getOriginalBornTimestamp(@NonNull Message message) {
        String originalBornTimestamp = message.getUserProperty(X_FS_ORIGINAL_BORN_TIMESTAMP);
        if (originalBornTimestamp == null) {
            return null;
        }
        return Long.valueOf(originalBornTimestamp);
    }

    private static void putMessageProperty(@NonNull Message message, String key, String value) {
        if (StringUtils.isAnyBlank(key, value)) {
            return;
        }
        message.putUserProperty(key, value);
    }

}
