package com.facishare.paas.task.calculate.config;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.fxiaoke.dispatcher.route.CollectionRouter;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum Namespaces {
    DEFAULT("default") {
        @Override
        public String getConfigName() {
            return BASE_CONFIG_NAME;
        }

        @Override
        public boolean isNamespaceTenant(String tenantId) {
            return CalculateGrayConfig.isNormalTenantId(tenantId);
        }

        @Override
        public boolean isNamespaceTenant(String tenantId, MessageExt message) {
            return CalculateGrayConfig.isNormalTenantId(tenantId, message);
        }
    },

    GRAY("gray") {
        @Override
        public String getDefaultEventTopic(String tenantId) {
            return EVENT_TOPIC_PREFIX + getName() + "_" + Integer.parseInt(tenantId) % 20;
        }

        @Override
        public String getConfigName() {
            return GRAY_CONFIG_NAME;
        }

        @Override
        public boolean isNamespaceTenant(String tenantId) {
            return CalculateGrayConfig.isGrayTenantId(tenantId);
        }

        @Override
        public boolean isNamespaceTenant(String tenantId, MessageExt message) {
            return CalculateGrayConfig.isGrayTenantId(tenantId, message);
        }
    },

    STAGE("stage") {
        @Override
        public String getEventTopic(String tenantId, String objectApiName, String dataId) {
            return EVENT_TOPIC_PREFIX + getName() + "_" + Integer.parseInt(tenantId) % 40;
        }

        @Override
        public Set<String> getAllEventTopics(String tenantId) {
            return Sets.newHashSet(getEventTopic(tenantId, null, null));
        }

        @Override
        public boolean isNamespaceTenant(String tenantId) {
            return CalculateGrayConfig.isStageTenantId(tenantId);
        }

        @Override
        public boolean isNamespaceTenant(String tenantId, MessageExt message) {
            return CalculateGrayConfig.isStageTenantId(tenantId, message);
        }
    },

    VIP("vip") {
        @Override
        public boolean isNamespaceTenant(String tenantId) {
            return CalculateGrayConfig.isVipTenantId(tenantId);
        }

        @Override
        public boolean isNamespaceTenant(String tenantId, MessageExt message) {
            return CalculateGrayConfig.isVipTenantId(tenantId, message);
        }
    };

    @Getter
    private String name;

    Namespaces(String name) {
        this.name = name;
    }

    public static Namespaces of(String name) {
        if (Strings.isNullOrEmpty(name)) {
            return DEFAULT;
        }
        return namespacesMap.getOrDefault(name, DEFAULT);
    }

    public static Namespaces getByTenantId(String tenantId) {
        if (CalculateGrayConfig.isStageTenantId(tenantId)) {
            return STAGE;
        }
        if (CalculateGrayConfig.isGrayTenantId(tenantId)) {
            return GRAY;
        }
//        if (CalculateGrayConfig.isVipTenantId(tenantId)) {
//            return VIP;
//        }
        return DEFAULT;
    }

    public String getConfigName() {
        return String.format("%s-%s", BASE_CONFIG_NAME, name);
    }

    public String getTopicByBaseTopic(final String baseTopic) {
        return String.format("%s-%s", baseTopic, name);
    }

    public String getEventTopic(String tenantId, String objectApiName, String dataId) {
        //优先使用配置文件中配置的topic
        List<String> topics = CalculateConfig.getDispatcherTopics(tenantId, objectApiName);
        //过滤掉名称不规范的topics
        topics = CollectionUtils.nullToEmpty(topics).stream().filter(x -> StringUtils.startsWith(x, "buf_")).collect(Collectors.toList());
        if (CollectionUtils.notEmpty(topics)) {
            if (topics.size() == 1) {
                return topics.get(0);
            }
            int index = Math.abs(Objects.hash(dataId) % topics.size());
            return topics.get(index);
        }
        //默认使用数据库地址构造topic
        return getDefaultEventTopic(tenantId);
    }

    public Set<String> getAllEventTopics(String tenantId) {
        //获取配置文件中配置的topic
        Set<String> topics = CalculateConfig.getAllDispatcherTopics(tenantId);
        //过滤掉名称不规范的topics
        topics = topics.stream().filter(x -> StringUtils.startsWith(x, "buf_")).collect(Collectors.toSet());
        //添加默认topic
        topics.add(getDefaultEventTopic(tenantId));
        return topics;
    }

    protected String getDefaultEventTopic(String tenantId) {
        return CollectionRouter.getTopicName(tenantId);
    }

    public abstract boolean isNamespaceTenant(String tenantId);

    public abstract boolean isNamespaceTenant(String tenantId, MessageExt message);

    public static Namespaces currentNamespace() {
        return namespaces;
    }

    public static boolean notInCurrentNamespace(String tenantId) {
        return !inCurrentNamespace(tenantId);
    }

    public static boolean inCurrentNamespace(String tenantId) {
        return currentNamespace().isNamespaceTenant(tenantId);
    }

    public static String getEventTopicByTenantIdAndObject(String tenantId, String objectApiName, String dataId) {
        return getByTenantId(tenantId).getEventTopic(tenantId, objectApiName, dataId);
    }

    public static Set<String> getAllEventTopicsByTenantId(String tenantId) {
        return getByTenantId(tenantId).getAllEventTopics(tenantId);
    }

    public static boolean isMasterNamespace(Namespaces namespaces) {
        return namespaces == DEFAULT || namespaces == VIP;
    }

    public static boolean isFeatureNamespace(Namespaces namespaces) {
        return namespaces == GRAY || namespaces == STAGE;
    }

    private final static Namespaces namespaces;
    private static final Map<String, Namespaces> namespacesMap;
    private static final Logger log = LoggerFactory.getLogger(Namespaces.class);
    private static final String BASE_CONFIG_NAME = "fs-paas-calculate-task-data-processing-consumer";
    private static final String GRAY_CONFIG_NAME = "fs-paas-calculate-task-data-processing-gray-consumer";
    private static final String EVENT_TOPIC_PREFIX = "buf_";

    static {
        namespacesMap = Stream.of(values()).collect(Collectors.toMap(Enum::toString, it -> it, (x, y) -> y,
                () -> new TreeMap<>(String.CASE_INSENSITIVE_ORDER)));

        String NAMESPACE = System.getProperty("namespace");
        namespaces = Namespaces.of(NAMESPACE);
        log.warn("application namespaces:{}", namespaces);
    }
}
