package com.facishare.paas.task.calculate.service;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.dispatcher.ObjectDataProxy;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.task.calculate.config.CalculateConfig;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class DataPackageService {

    @Autowired
    private ObjectDataProxy dataProxy;

    @Autowired
    private ObjectDataServiceImpl objectDataService;

    public IObjectData findObjectDataIgnoreAll(String tenantId, String id, String objectApiName) {
        List<IObjectData> dataList = findObjectDataByIdsIgnoreAll(tenantId, Lists.newArrayList(id), objectApiName);
        if (CollectionUtils.empty(dataList)) {
            throw new ObjectDataNotFoundException(I18N.text(I18NKey.DATA_NOT_USED));
        }
        return dataList.get(0);
    }

    public List<IObjectData> findObjectDataByIdsIgnoreAll(String tenantId, List<String> ids, String objectApiName) {
        return getObjectDataByIdsIgnoreAll(User.systemUser(tenantId), ids, objectApiName, false);
    }

    public List<IObjectData> findObjectDataByIdsIncludeDeletedIgnoreAll(User user, List<String> ids, String objectApiName) {
        return getObjectDataByIdsIgnoreAll(user, ids, objectApiName, true);
    }

    private List<IObjectData> getObjectDataByIdsIgnoreAll(User user, List<String> ids, String objectApiName, boolean includeDeleted) {
        if (CollectionUtils.empty(ids)) {
            return Lists.newArrayList();
        }
        IActionContext context = getContext(user);
        context.setDoCalculate(false);
        context.setPrivilegeCheck(false);

        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        queryExt.searchInDB();
        queryExt.setNeedReturnCountNum(false);
        queryExt.setNeedReturnQuote(false);
        queryExt.setLimit(ids.size());
        queryExt.addFilter(Operator.EQ, IObjectData.DESCRIBE_API_NAME, objectApiName);
        queryExt.addFilter(Operator.IN, IObjectData.ID, ids);
        if (!includeDeleted) {
            queryExt.addIsDeletedFalseFilter();
        }
        try {
            QueryResult<IObjectData> queryResult = dataProxy.findBySearchQuery(context.getEnterpriseId(), objectApiName,
                    (SearchTemplateQuery) queryExt.getQuery(), context);
            return CollectionUtils.nullToEmpty(queryResult.getData());
        } catch (MetadataServiceException e) {
            log.warn("findObjectDataByIdsIncludeDeleted error,tenantId:{},objectApiName:{},ids:{}", context.getEnterpriseId(),
                    objectApiName, ids, e);
            throw new MetaDataBusinessException(e);
        }
    }

    public QueryResult<IObjectData> findBySearchQueryIgnoreAll(User user, String apiName, SearchTemplateQuery searchTemplateQuery) {
        //元数据不处理引用字段、不获取相关团队
        IActionContext context = getContext(user);
        //不处理计算字段
        context.setDoCalculate(false);
        //跳过数据权限
        searchTemplateQuery.setPermissionType(0);
        //不返回总记录数
        searchTemplateQuery.setNeedReturnCountNum(false);
        //不返回引用字段
        searchTemplateQuery.setNeedReturnQuote(false);

        if (!Utils.AGGREGATE_VALUE_OBJ_API_NAME.equals(apiName)) {
            SearchTemplateQueryExt.of(searchTemplateQuery).addDeletedFilterIfNoDeletedFilter();
        }

        try {
            return dataProxy.findBySearchQuery(context.getEnterpriseId(), apiName, searchTemplateQuery, context);
        } catch (MetadataServiceException e) {
            log.warn("findBySearchQueryIgnoreAll error,tenantId:{},apiName:{},searchTemplateQuery:{}", user.getTenantId(),
                    apiName, searchTemplateQuery.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    public QueryResult<IObjectData> findBySearchQueryWithFieldsIgnoreAll(User user, String apiName, SearchTemplateQuery searchTemplateQuery, List<String> fieldApiNames) {
        //元数据不处理引用字段、不获取相关团队
        IActionContext context = getContext(user);
        //灰度企业走es查询
        if (CalculateConfig.isFindDataWithESInCalculateTask(user.getTenantId())) {
            context.put(ActionContextKey.ES_SEARCH_SKIP_APP_CHECK, true);
            context.put(ActionContextKey.ONLY_RETURN_IDS_FROM_ES, true);
        }
        //不处理计算字段
        context.setDoCalculate(false);
        //跳过数据权限
        searchTemplateQuery.setPermissionType(0);
        //不返回总记录数
        searchTemplateQuery.setNeedReturnCountNum(false);
        //不返回引用字段
        searchTemplateQuery.setNeedReturnQuote(false);

        if (!Utils.AGGREGATE_VALUE_OBJ_API_NAME.equals(apiName)) {
            SearchTemplateQueryExt.of(searchTemplateQuery).addDeletedFilterIfNoDeletedFilter();
        }

        try {
            //拷贝一份防止底层修改这个List
            fieldApiNames = Lists.newArrayList(CollectionUtils.nullToEmpty(fieldApiNames));
            return objectDataService.findBySearchQuery(user.getTenantId(), apiName, searchTemplateQuery, fieldApiNames, context);
        } catch (MetadataServiceException e) {
            log.warn("findBySearchQueryWithFieldsIgnoreAll error,tenantId:{},apiName:{},searchTemplateQuery:{},fieldApiNames:{}", user.getTenantId(),
                    apiName, searchTemplateQuery.toJsonString(), fieldApiNames, e);
            throw new MetaDataBusinessException(e);
        }
    }

    public int countDataNum(User user, String apiName, SearchTemplateQuery searchTemplateQuery) {
        IActionContext context = getContext(user);
        //走es查询
        context.put(ActionContextKey.ES_SEARCH_SKIP_APP_CHECK, true);
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(searchTemplateQuery);
        queryExt.onlyQueryTotalNumIgnorePermission();
        queryExt.addDeletedFilterIfNoDeletedFilter();
        try {
            return dataProxy.findBySearchQuery(user.getTenantId(), apiName, searchTemplateQuery, context).getTotalNumber();
        } catch (MetadataServiceException e) {
            log.warn("countDataNum error,tenantId:{},apiName:{},searchTemplateQuery:{}", user.getTenantId(),
                    apiName, searchTemplateQuery.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    private IActionContext getContext(User user) {
        return ActionContextExt.of(user).disableDeepQuote().skipRelevantTeam().doNotDeepCopyDescribe().getContext();
    }

}
