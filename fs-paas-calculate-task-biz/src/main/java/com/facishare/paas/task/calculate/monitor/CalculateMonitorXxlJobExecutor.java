package com.facishare.paas.task.calculate.monitor;

import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.xxl.job.core.executor.XxlJobExecutor;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/2/8
 */
public class CalculateMonitorXxlJobExecutor extends XxlJobExecutor {

    public CalculateMonitorXxlJobExecutor(String configName) {
        ConfigFactory.getConfig(configName, (IConfig config) -> {
            setIp(config.get("xxl.job.executor.ip"));
            setPort(config.getInt("xxl.job.executor.port"));
            setAdminAddresses(config.get("xxl.job.admin.addresses"));
            setAppName(config.get("xxl.job.app.name"));
            setAccessToken(config.get("xxl.job.accessToken"));
            setLogPath(config.get("xxl.job.executor.logpath"));
        });
    }
}
