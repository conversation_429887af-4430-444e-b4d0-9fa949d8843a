package com.facishare.paas.task.calculate.model;

import com.facishare.paas.appframework.metadata.relation.FieldNode;
import com.facishare.paas.appframework.metadata.relation.RelateEdge;
import com.facishare.paas.appframework.metadata.relation.RelateType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.task.calculate.config.Namespaces;
import com.facishare.paas.task.calculate.util.DelayTimer;
import com.facishare.paas.task.calculate.util.PriorityCalculator;
import com.fxiaoke.dispatcher.common.Constants;
import com.fxiaoke.dispatcher.common.MessageHelper;
import com.google.common.base.Joiner;
import com.google.common.collect.Sets;
import lombok.Data;

/**
 * Created by liwei on 2019/8/16
 */
@Data
public class CountDataInfo {
    private String dataId;
    private String objectApiName;
    private String fieldApiName;
    private RelateType relateType;
    private String referenceFieldName;

    /**
     * 原始的对象 ApiName
     */
    private String originalDescribeApiName;
    /**
     * 原始的数据 Id
     */
    private String originalObjectId;

    public static CountDataInfo of(String dataId, FieldNode node, RelateEdge.RelateEdgeNode relateEdgeNode,
                                   IObjectData originalObject) {
        CountDataInfo countDataInfo = new CountDataInfo();
        countDataInfo.setDataId(dataId);
        countDataInfo.setObjectApiName(node.getObjectApiName());
        countDataInfo.setFieldApiName(node.getFieldApiName());
        countDataInfo.setRelateType(relateEdgeNode.getRelateType());
        countDataInfo.setReferenceFieldName(relateEdgeNode.getReferenceFieldName());
        countDataInfo.setOriginalDescribeApiName(originalObject.getDescribeApiName());
        countDataInfo.setOriginalObjectId(originalObject.getId());
        return countDataInfo;
    }

    public CalculateDataEvent toEvent(CustomDataChangeMessage message) {
        String tenantId = message.getTenantId();
        // 唯一ID(tenantId + objectDescribeApiName + dataId)
        String uniqKey = MessageHelper.md5(tenantId, this.objectApiName, this.dataId, FieldNode.NodeType.COUNT.name());
        // 构建分类（tenantId + objectDescribeApiName）
        String category = Joiner.on('^').join(tenantId, this.objectApiName);

        // 根据tenantId查询topic名称
        String topic = Namespaces.getEventTopicByTenantIdAndObject(tenantId, this.objectApiName, dataId);
        long now = System.currentTimeMillis();
        long dispatchTime = now + getDelayTime(message.getQueueType());
        String queueType = message.getQueueType();
        int priority = getPriority(tenantId, queueType);

        return CalculateDataEvent.builder()
                .status(Constants.EventStatus.STATUS_READY)
                .key(uniqKey)
                .topic(topic)
                .category(category)
                .priority(priority)
                .dispatchTime(dispatchTime)
                .createTime(now)
                .modifiedTime(now)
                .tenantId(tenantId)
                .objectApiName(this.objectApiName)
                .dataId(this.dataId)
                .fieldApiNames(Sets.newHashSet(this.fieldApiName))
                .fieldType(FieldNode.NodeType.COUNT.name())
                .action(message.getActionCode())
                .queueTypes(Sets.newHashSet(queueType))
                .eventId(message.getEventId())
                .originalObjectApiName(this.originalDescribeApiName)
                .originalBornTimestamp(message.getOriginalBornTimestamp())
                .originalMessageId(message.getMessageId())
                .build();
    }

    private int getPriority(String tenantId, String queueType) {
        return PriorityCalculator.builder()
                .tenantId(tenantId)
                .objectApiName(this.objectApiName)
                .fieldApiNames(Sets.newHashSet(this.fieldApiName))
                .queueType(queueType)
                .build()
                .calculate();
    }

    private int getDelayTime(String queueType) {
        return DelayTimer.getDelayTime(queueType, FieldNode.NodeType.COUNT.name());
    }
}
