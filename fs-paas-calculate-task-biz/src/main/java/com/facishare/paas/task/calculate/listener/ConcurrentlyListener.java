package com.facishare.paas.task.calculate.listener;

import com.facishare.paas.task.calculate.listener.BaseListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;

import java.util.List;

/**
 * create by z<PERSON><PERSON> on 2020/07/01
 */
@Slf4j
public class ConcurrentlyListener implements MessageListenerConcurrently {
    private BaseListener listener;

    public void setListener(BaseListener listener) {
        this.listener = listener;
    }

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        try {
            listener.consumeMessage(msgs);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.warn("consume message failed!", e);
        }
        return ConsumeConcurrentlyStatus.RECONSUME_LATER;
    }
}
