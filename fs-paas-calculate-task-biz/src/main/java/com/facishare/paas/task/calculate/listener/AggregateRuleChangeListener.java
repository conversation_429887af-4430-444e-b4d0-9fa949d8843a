package com.facishare.paas.task.calculate.listener;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.task.calculate.comsumer.AggregateRuleChangeConsumer;
import com.facishare.paas.task.calculate.config.Namespaces;
import com.facishare.paas.task.calculate.model.AggregateRuleChangeMessage;
import com.github.trace.TraceContext;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;

public class AggregateRuleChangeListener extends BaseListener<AggregateRuleChangeMessage> {

    @Autowired
    private AggregateRuleChangeConsumer consumer;

    @Override
    protected AggregateRuleChangeMessage parseMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        AggregateRuleChangeMessage aggregateRuleChangeMessage = JSON.parseObject(body, AggregateRuleChangeMessage.class);
        aggregateRuleChangeMessage.setMessageId(message.getMsgId());
        aggregateRuleChangeMessage.setBornTimestamp(message.getBornTimestamp());
        return aggregateRuleChangeMessage;
    }

    @Override
    protected String getArgDesc(AggregateRuleChangeMessage arg) {
        return JSON.toJSONString(arg);
    }

    @Override
    protected void doConsume(AggregateRuleChangeMessage arg) {
        consumer.execute(arg);
    }

    @Override
    protected boolean skip(AggregateRuleChangeMessage arg, MessageExt message) {
        if (!arg.needCalculate()) {
            return true;
        }
        return isSkipMessage(arg.getTenantId(), message);
    }

    @Override
    protected String getTenantId(AggregateRuleChangeMessage arg) {
        return arg.getTenantId();
    }

    @Override
    protected boolean isSkipMessage(String tenantId, MessageExt message) {
        return !Namespaces.currentNamespace().isNamespaceTenant(tenantId, message);
    }

    @Override
    protected void fillTraceContext(AggregateRuleChangeMessage arg) {
        String tenantId = arg.getTenantId();
        TraceContext traceContext = TraceContext.get();
        traceContext.setEi(tenantId);
    }
}
