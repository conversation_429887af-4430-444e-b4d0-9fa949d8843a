package com.facishare.paas.task.calculate.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.task.calculate.util.QueueTypes;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.ObjectLifeStatus.*;

/**
 * Created by liwei on 2018/10/16
 */
@Slf4j
@Data
public class CustomDataChangeMessage {
    public static final String CALCULATE_SOURCE = "calculate";
    public static final String USER_ID = "userId";
    private static final Set<String> EXCLUDE_LIST = Sets.newHashSet("version", "is_deleted");

    private String tenantId;
    private String op;
    private String name;
    private List<DataContent> body;
    private String dataSource;
    private boolean batch;
    private boolean fromOpTool;
    private String messageId;
    private Long originalBornTimestamp;

    public static List<String> diffDataFieldChanges(IObjectDescribe objectDescribe, Set<String> fieldNames, Map<String, Object> beforeData,
                                                    Map<String, Object> afterData) {
        List<String> changeFields = Lists.newArrayList();
        fieldNames.forEach(field -> {
            if (EXCLUDE_LIST.contains(field)) {
                return;
            }
            IFieldDescribe fieldDescribe = ObjectDescribeExt.of(objectDescribe).getActiveFieldDescribeSilently(field).orElse(null);
            if (fieldDescribe == null) {
                return;
            }
            Object before = beforeData.get(field);
            Object after = afterData.get(field);
            try{
                boolean isEqual = ObjectDataExt.isValueEqual(before, after, fieldDescribe);
                if (!isEqual) {
                    changeFields.add(field);
                }
            } catch(Exception e) {
                log.error("diffDataFieldChanges failed, field: {}, before: {}, after: {}", field, before, after, e);
                changeFields.add(field);
            }
        });
        return changeFields;
    }

    public String getObjectApiName() {
        if (CollectionUtils.empty(body)) {
            return null;
        }
        return body.get(0).getEntityId();
    }

    public boolean isInvalidOperation() {
        return "invalid".equals(op);
    }

    public boolean isDeleteOperation() {
        return "d".equals(op) || "d_r".equals(op);
    }

    public boolean isDirectDelete() {
        if (CollectionUtils.empty(body)) {
            return false;
        }
        return body.stream().anyMatch(DataContent::isDirectDeleteData);
    }

    public boolean fromCalculationJobOrOpTool() {
        if (Strings.isNullOrEmpty(dataSource)) {
            return false;
        }
        return dataSource.contains(QueueTypes.CALCULATION_JOB)
                || dataSource.contains(QueueTypes.OPTOOL);
    }

    public String getQueueType() {
        if (isBatch()) {
            return QueueTypes.BATCH;
        }
        if (isFromOpTool()) {
            return QueueTypes.OPTOOL;
        }
        return QueueTypes.MANUAL;
    }

    public boolean isManual() {
        return QueueTypes.MANUAL.equals(getQueueType());
    }

    public String getActionCode() {
        switch (op) {
            case "u":
                return ObjectAction.UPDATE.getActionCode();
            case "i":
                return ObjectAction.CREATE.getActionCode();
            case "invalid":
            case "d":
            case "d_r":
                return ObjectAction.INVALID.getActionCode();
            case "recover":
                return ObjectAction.RECOVER.getActionCode();
            default:
                return op;
        }
    }

    public String getEventId() {
        if (CollectionUtils.empty(body)) {
            return null;
        }
        return body.get(0).getEventId();
    }

    public boolean bodyEmpty() {
        return body == null || body.isEmpty();
    }

    public boolean opByCalculate() {
        return StringUtils.startsWith(dataSource, CALCULATE_SOURCE);
    }

    @Data
    public static class DataContent {
        @JSONField(serialize = false)
        private String eventId;
        @JSONField(serialize = false)
        private Map<String, Object> beforeTriggerData;
        @JSONField(serialize = false)
        private String context;
        private String entityId;
        @JSONField(serialize = false)
        private String triggerType;
        private String objectId;
        @JSONField(serialize = false)
        private Map<String, Object> afterTriggerData;

        public boolean isLifeStatusUpdatedByInvalid() {
            if (CollectionUtils.empty(getAfterTriggerData())) {
                return false;
            }
            return getAfterTriggerData().containsKey(LIFE_STATUS_BEFORE_INVALID_API_NAME)
                    && INVALID.getCode().equals(getAfterTriggerData().get(LIFE_STATUS_API_NAME));
        }

        public boolean isDirectDeleteData() {
            if (CollectionUtils.empty(beforeTriggerData)) {
                return false;
            }
            return String.valueOf(DELETE_STATUS.NORMAL.getValue()).equals(beforeTriggerData.get(DBRecord.IS_DELETED));
        }

        public List<String> getChangeFields(IObjectDescribe objectDescribe) {
            List<String> fieldChanges = Lists.newArrayList();
            Map<String, Object> beforeData = getBeforeTriggerData();
            Map<String, Object> afterData = getAfterTriggerData();

            //3.1 判断值变更得字段
            Set<String> afterKeys = afterData.keySet();
            Set<String> beforeKeys = beforeData.keySet();
            //需要确认filter逻辑！！！
            List<String> specialKeys = beforeKeys.stream()
                    .filter(x -> !afterKeys.contains(x))
                    .filter(x -> !EXCLUDE_LIST.contains(x))
                    .collect(Collectors.toList());
            fieldChanges.addAll(specialKeys);

            List<String> changes = diffDataFieldChanges(objectDescribe, afterData.keySet(), beforeData, afterData);
            fieldChanges.addAll(changes);

            return fieldChanges;
        }

        public Tuple<Object, Object> getValue(String fieldName) {
            return Tuple.of(beforeTriggerData.get(fieldName), afterTriggerData.get(fieldName));
        }
    }

}
