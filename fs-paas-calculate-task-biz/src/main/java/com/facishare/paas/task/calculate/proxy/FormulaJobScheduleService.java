package com.facishare.paas.task.calculate.proxy;

import com.facishare.paas.task.calculate.model.CalculationJobMessage;
import com.facishare.paas.task.calculate.model.UpdateCalculationJob;

public interface FormulaJobScheduleService {

    void completedCalculateJob(CalculationJobMessage calculateJobMQMessage);

    void updateCalculateJobProgress(CalculationJobMessage calculateJobMQMessage);

    void updateCalculateJobProgress(String tenantId, UpdateCalculationJob.Arg arg);

    void ceaseCalculateJob(CalculationJobMessage calculateJobMQMessage);
}
