package com.facishare.paas.task.calculate.dispatcher;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.relation.*;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Sets;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by liwei on 2018/11/13
 */
@Slf4j
@SuperBuilder
public class RecoverDispatcher extends FieldMetadataDispatcher {

    @Override
    protected Set<NodeEdgePair> getCalculateNodeEdgePairs(List<String> fieldChanges, IObjectDescribe objectDescribe, FieldRelationGraph graph) {
        Set<NodeEdgePair> calculateNodeEdgePairs = super.getCalculateNodeEdgePairs(fieldChanges, objectDescribe, graph);
        //补充本对象不依赖其他字段的孤立节点
        Set<NodeEdgePair> calculateNodeEdgePairSets = getIsolatedS2SCalculateNodeEdgePairs(graph, objectDescribe);
        if (CollectionUtils.notEmpty(calculateNodeEdgePairSets)) {
            calculateNodeEdgePairs.addAll(calculateNodeEdgePairSets);
        }
        return calculateNodeEdgePairs;
    }

    /**
     * 拿到本对象所有统计字段和属于孤立节点的计算字段
     *
     * @param graph
     * @param describe
     * @return 需要计算的节点和边
     */
    private Set<NodeEdgePair> getIsolatedS2SCalculateNodeEdgePairs(FieldRelationGraph graph, IObjectDescribe describe) {
        String objectApiName = describe.getApiName();
        return ObjectDescribeExt.of(describe).getCountAndFormulaFields().stream()
                .filter(x -> FieldDescribeExt.of(x).isCalculateFieldsNeedStoreInDB())
                .map(field -> graph.getNode(objectApiName, field.getApiName()).map(node -> {
                    // 拿到依赖的节点
                    Set<FieldNode> predecessors = graph.predecessors(node);
                    if (node.isCountField() || CollectionUtils.empty(predecessors)) {
                        // 依赖节点为空，代表是孤立的节点
                        return Sets.newHashSet(NodeEdgePair.of(node,
                                RelateEdge.of(RelateEdge.RelateEdgeNode.of(RelateType.S2S))));
                    }
                    return Sets.<NodeEdgePair>newHashSet();
                }).orElse(Sets.newHashSet()))
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
    }
}
