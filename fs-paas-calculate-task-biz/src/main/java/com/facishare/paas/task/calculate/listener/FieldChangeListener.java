package com.facishare.paas.task.calculate.listener;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.task.calculate.comsumer.FieldChangeConsumer;
import com.facishare.paas.task.calculate.comsumer.ObjectChangeConsumer;
import com.facishare.paas.task.calculate.config.Namespaces;
import com.facishare.paas.task.calculate.model.FieldChangeMessage;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.Charset;

/**
 * Created by l<PERSON><PERSON> on 2019/1/16
 */
public class FieldChangeListener extends BaseListener<FieldChangeMessage> {

    @Autowired
    private FieldChangeConsumer fieldChangeConsumer;

    @Autowired
    private ObjectChangeConsumer objectChangeConsumer;

    @Override
    protected FieldChangeMessage parseMessage(MessageExt message) {
        String body = new String(message.getBody(), Charset.forName("utf-8"));
        FieldChangeMessage fieldChangeMessage = JSON.parseObject(body, FieldChangeMessage.class);
        fieldChangeMessage.setMessageId(message.getMsgId());

        return fieldChangeMessage;
    }

    @Override
    protected String getArgDesc(FieldChangeMessage arg) {
        return JSON.toJSONString(arg);
    }

    @Override
    protected boolean isSkipMessage(String tenantId, MessageExt message) {
        return !Namespaces.currentNamespace().isNamespaceTenant(tenantId, message);
    }

    @Override
    protected boolean skip(FieldChangeMessage arg, MessageExt message) {
        return !Namespaces.currentNamespace().isNamespaceTenant(arg.getTenantId(), message);
    }

    @Override
    protected String getTenantId(FieldChangeMessage arg) {
        return arg.getTenantId();
    }

    @Override
    protected void doConsume(FieldChangeMessage arg) {
        if (arg.isFieldMessage()) {
            fieldChangeConsumer.execute(arg);
        } else {
            objectChangeConsumer.execute(arg);
        }
    }
}
