package com.facishare.paas.task.calculate.model

import com.alibaba.fastjson.JSON
import spock.lang.Specification

/**
 * create by <PERSON><PERSON><PERSON> on 2020/09/30
 */
class CalculateNodeExtTest extends Specification {

    def "test_s2sFormulaNodes"() {
        when:
        CalculateDataChangeMessage dataChangeMessage = JSON.parseObject(body, CalculateDataChangeMessage)
        List<CalculateNode> calculateNodes = dataChangeMessage.getCalculateNodes()
        then:
        Optional<CalculateNodeExt.CalculateNodeGroup> calculateNodeGroup = CalculateNodeExt.s2sFormulaNodes(calculateNodes, dataChangeMessage.getDescribeApiName())
        def s2sFormulaNodes = calculateNodeGroup.get().getCalculateNodes()
        println JSON.toJSONString(s2sFormulaNodes)
        calculateNodes.removeAll(s2sFormulaNodes)
        println JSON.toJSONString(calculateNodes)
        1 == 1
        where:
        body    || _
        '''{
    "batch": false,
    "calculateNodes": [
        {
            "calculateValue": false,
            "fieldApiName": "field_m1lfp__c",
            "nodeType": "FORMULA",
            "objectApiName": "object_k3Kz0__c",
            "order": 51,
            "relateType": "S2S"
        },
        {
            "calculateValue": false,
            "fieldApiName": "field_QM76r__c",
            "nodeType": "FORMULA",
            "objectApiName": "object_R7poC__c",
            "order": 2,
            "relateType": "S2S"
        },
        {
            "calculateValue": false,
            "fieldApiName": "field_951vu__c",
            "nodeType": "FORMULA",
            "objectApiName": "object_R7poC__c",
            "order": 5,
            "relateType": "S2S"
        },
        {
            "calculateValue": false,
            "fieldApiName": "field_g39SS__c",
            "nodeType": "FORMULA",
            "objectApiName": "object_R7poC__c",
            "order": 4,
            "relateType": "S2S"
        },
        {
            "calculateValue": false,
            "fieldApiName": "field_JfMrd__c",
            "nodeType": "FORMULA",
            "objectApiName": "object_k3Kz0__c",
            "order": 5,
            "referenceFieldName": "field_4h8b6__c",
            "relateType": "R2L"
        },
        {
            "calculateValue": false,
            "fieldApiName": "field_q2d0T__c",
            "nodeType": "FORMULA",
            "objectApiName": "object_k3Kz0__c",
            "order": 8,
            "relateType": "S2S"
        },
        {
            "calculateValue": false,
            "fieldApiName": "field_h2a0c__c",
            "nodeType": "FORMULA",
            "objectApiName": "object_k3Kz0__c",
            "order": 46,
            "relateType": "S2S"
        },
        {
            "calculateValue": false,
            "fieldApiName": "field_t2k0M__c",
            "nodeType": "FORMULA",
            "objectApiName": "object_R7poC__c",
            "order": 4,
            "relateType": "S2S"
        },
        {
            "calculateValue": false,
            "fieldApiName": "field_h9b5P__c",
            "nodeType": "FORMULA",
            "objectApiName": "object_k3Kz0__c",
            "order": 49,
            "relateType": "S2S"
        }
    ],
    "dataIdList": [
        "5e8313782995a300010bab88"
    ],
    "describeApiName": "object_R7poC__c",
    "eventId": "5f72cfe7b83703000131c36b",
    "op": "Edit",
    "originalMessageId": "AC11291700002A9F000002B96E5F9EE0",
    "tenantId": "1"
}''' || _
    }
}
