package com.facishare.paas.task.calculate.util

import spock.lang.Specification

/**
 * create by <PERSON><PERSON><PERSON> on 2020/11/05
 */
class ConsistentHashingTest extends Specification {
    def "GetNodeByKey"() {
        given:
        def nodes = (0..node).collect { new ConsistentHashing.Node("key": it) }
        ConsistentHashing consistentHashing = new ConsistentHashing(nodes, HashAlgorithm.FNV1A_32_HASH)
        when:
        def result = tenantIds.groupBy { consistentHashing.getNodeByKey(it) }
        then:
        println(result)

        println result.collectEntries { key, value -> [key, value.size()] }
        true

        where:
        node | tenantIds                                                               || _
        23   | ["71698", "78057", "74255", "71554", "78060", "1", "7689"]              || _
        10   | (0..10000).collect {
            it.toString()
        }                                                                              || _
        160  | """7689,632829,598335,604891,595453,606802,488989,632477,580984,419804,627115,307447,664226,597566,
629927,507719,127914,501449,662757,90706,516148,49148,199529,526380,606457,488995,589227,615315,594192,389877,568645,
496991,549366,617168,643224,633761,17824,620093,481495,636965,562487,626246,55002,542661,511164,424059,616899,636781,
565556,85421,648162,632768,592135,111024,624115,331894,461402,570814,623264,503575,629257,619615,32099,542956,603207,
59472,541219,581718,635758,597902,622836,588110,646222,200501,61456,642824,6030,597846,35187,595995,78722,210294,
77617,57515,339525,633975,381492,598144,642028,102177,570967,611667,256184,653632,455014,489207,101003,324519,448142,
491187,82897,99598,472252,689193,663598""".split(",").collect { it } || _
    }
}
