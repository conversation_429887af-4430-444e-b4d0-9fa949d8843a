<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
    <context:component-scan base-package="com.facishare.paas.appframework,com.facishare.paas.task"/>
    <context:annotation-config/>

    <import resource="classpath:spring/common.xml"/>
    <import resource="classpath:spring/metadata.xml"/>
    <import resource="classpath:spring/log.xml"/>
    <import resource="classpath:spring/flow.xml"/>
    <import resource="classpath:spring/privilege.xml"/>
    <import resource="classpath:spring/licence.xml"/>
    <import resource="classpath:spring/fsi.xml"/>
    <import resource="classpath:spring/dubbo.xml"/>
    <import resource="classpath:spring/restdriver.xml"/>
    <!--<import resource="classpath:spring/calculateDataListener.xml"/>-->
    <!--<import resource="classpath:spring/metadataListener.xml"/>-->
    <!--<import resource="classpath:spring/jobSchedule.xml"/>-->
    <import resource="classpath:spring/function-service.xml"/>

    <bean id="autoConf"
          class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
          p:fileEncoding="UTF-8"
          p:ignoreResourceNotFound="true"
          p:ignoreUnresolvablePlaceholders="false"
          p:location="classpath:application.properties"
          p:configName="dubbo-common,fs-paas-metadata-mongo,fs-paas-appframework-rest,fs-crm-java-config,fs-crm-printconfig,
          fs-metadata,fs-crm-icon-path,fs-crm-java-detailpage-layout-setting,fs-crm-add-function-code,fs-crm-sys-variable,
          new-predefined-object"/>

    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"
          c:placeholderConfigurer-ref="autoConf"/>

    <!--上报错误日志-->
    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>

    <bean id="bizConfApi" class="com.facishare.restful.client.FRestApiProxyFactoryBean">
        <property name="type" value="com.fxiaoke.bizconf.api.BizConfApi"/>
    </bean>


    <bean id="restServiceProxyFactory" class="com.facishare.rest.core.RestServiceProxyFactory"
          p:configName="fs-paas-appframework-rest" init-method="init"/>

    <!--更新计算任务状态接口-->
    <bean id="updateJobStatus" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.task.calculate.proxy.FormulaJobScheduleProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>
</beans>