package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.common.service.model.ManageGroup;
import com.facishare.paas.appframework.privilege.dto.PageInfo;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Service
public class DataPrivilegeCommonService {

  private FsGrayReleaseBiz dataAuthGray = FsGrayRelease.getInstance("data-auth");


  public boolean enableDataQueryByDescribeAndScopeValidation(String tenantId) {
    return dataAuthGray.isAllow("enableDataQueryByDescribeAndScopeValidation", tenantId);
  }


  public PageInfo getDefaultPageInfo(Integer page, Integer size) {
    if (page == null) {
      page = 1;
    }

    if (size == null) {
      size = 20;
    }

    return PageInfo.builder().currentPage(page).pageSize(size).totalPage(0).total(0).build();
  }

  public List<String> checkEntityIdsByManageGroup(Collection<String> entityIds, ManageGroup manageGroup) {
    List<String> result = Lists.newArrayList();

    if (manageGroup == null) {
      return result;
    }

    Set<String> supportApiNames = manageGroup.getSupportApiNames();

    if (CollectionUtils.isEmpty(supportApiNames)) {
      return result;
    }

    if (CollectionUtils.isEmpty(entityIds)) {
      result.addAll(supportApiNames);
      return result;
    }

    for (String v : entityIds) {
      if (supportApiNames.contains(v)) {
        result.add(v);
      }
    }

    return result;
  }

  public Set<String> checkDeptAndOrgIdsByManageScope(Collection<String> queryIds, Set<String> deptAndOrgIds) {
    Set<String> result = Sets.newHashSet();

    if (CollectionUtils.isEmpty(deptAndOrgIds)) {
      return result;
    }

    if (CollectionUtils.isEmpty(queryIds)) {
      result.addAll(deptAndOrgIds);
      return result;
    }

    for (String v : queryIds) {
      if (deptAndOrgIds.contains(v)) {
        result.add(v);
      }
    }

    return result;
  }

}
