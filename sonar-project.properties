sonar.sourceEncoding=UTF-8
sonar.qualitygate.wait=true
sonar.qualitygate.timeout=600
sonar.log.level=INFO
sonar.scm.provider=git
sonar.host.url=https://oss.firstshare.cn/sonarqube
sonar.login=squ_19bd8deac1b6fd9b541e89f4b5c24eb80af392d3
sonar.projectName=fs-webpage-customer
sonar.links.scm=https://git.firstshare.cn/Qixin/fs-webpage-customer.git
sonar.links.ci=https://git.firstshare.cn/Qixin/fs-webpage-customer/-/pipelines
sonar.links.homepage=https://git.firstshare.cn/Qixin/fs-webpage-customer
sonar.links.issue=https://git.firstshare.cn/Qixin/fs-webpage-customer/-/issues
sonar.findbugs.allowuncompiledcode=true
sonar.core.codeCoveragePlugin=jacoco
sonar.coverage.exclusions=**/dto/**,**/model/**,**/arg/**,**/result/**