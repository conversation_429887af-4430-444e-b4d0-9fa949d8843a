package com.facishare.paas.expression.util

import groovy.text.GStringTemplateEngine

class CaseFunctionGen {
    private static CODE_TEMPLATE = """
package com.facishare.paas.expression

trait LogicCaseFunction {
<%
for(i in 1..50){
%>
    def <V> V CASE(expression,<%for(int j=1;j<=i;j++){%> k<%=j%>, V v<%=j%>,<%}%> V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression){<%for(int j=1;j<=i;j++){%>
            case k<%=j%> : return v<%=j%><%}%>
            default: return defaultResult
        }
    }
<%    
}
%> 
}
"""
    static void main(String[] args) {
        def engine = new GStringTemplateEngine()
        def caseFunction = engine.createTemplate(CODE_TEMPLATE).make()
        new File("src/main/groovy/com/facishare/paas/expression/functions/LogicCaseFunction.groovy")
                .withWriter {out -> out.println(caseFunction)}
    }

}
