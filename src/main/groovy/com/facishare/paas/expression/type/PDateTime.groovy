package com.facishare.paas.expression.type

import com.facishare.paas.timezone.DateTimeFormat
import com.facishare.paas.timezone.TimeZoneContext
import com.facishare.paas.timezone.TimeZoneContextHolder
import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString

import java.math.RoundingMode
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

/**
 * Created by liyiguang on 2017/9/9.
 */
@ToString
@EqualsAndHashCode
class PDateTime implements Comparable<PDateTime> {
    private static DateTimeFormatter LONG_DATE_PATTERN_LINE = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    private final LocalDateTime value

    PDateTime(LocalDateTime value) {
        this.value = value
    }

    static PDateTime of(long timestamp) {
        if (TimeZoneContextHolder.isGray()) {
            def localDateTime = DateTimeFormat.DATE_TIME.convertToLocalDateTime(timestamp, TimeZoneContextHolder.getTenantTimeZone())
            return new PDateTime(localDateTime)
        }
        return new PDateTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault()))
    }

    static PDateTime of(long timestamp, ZoneId zoneId) {
        return new PDateTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), zoneId))
    }

    static PDateTime of(String dateTime) {
        return new PDateTime(LocalDateTime.parse(dateTime, LONG_DATE_PATTERN_LINE))
    }

    static PDateTime of(int year, int month, int day, int hour, int minute, int second) {
        return new PDateTime(LocalDateTime.of(year, month, day, hour, minute, second))
    }

    PDateTime plus(Years years) {
        return new PDateTime(value.plusYears(years.getValue()))
    }

    PDateTime plus(Months months) {
        return new PDateTime(value.plusMonths(months.getValue()))
    }

    PDateTime plus(Days days) {
        return new PDateTime(value.plusDays(days.getValue()))
    }

    PDateTime plus(Hours hours) {
        return new PDateTime(value.plusHours(hours.getValue()))
    }

    PDateTime plus(Minutes minutes) {
        return new PDateTime(value.plusMinutes(minutes.getValue()))
    }

    PDateTime minus(Years years) {
        return new PDateTime(value.minusYears(years.getValue()))
    }

    PDateTime minus(Months months) {
        return new PDateTime(value.minusMonths(months.getValue()))
    }

    PDateTime minus(Days days) {
        return new PDateTime(value.minusDays(days.getValue()))
    }

    PDateTime minus(Hours hours) {
        return new PDateTime(value.minusHours(hours.getValue()))
    }

    PDateTime minus(Minutes minutes) {
        return new PDateTime(value.minusMinutes(minutes.getValue()))
    }

    BigDecimal minus(PDateTime dateTime) {
        Objects.requireNonNull(dateTime)
        BigDecimal ret = BigDecimal.valueOf(this.toTimeStamp() - dateTime.toTimeStamp()) / (60 * 60 * 1000)
        ret = ret.setScale(2, RoundingMode.HALF_DOWN)
        return ret
    }

//    double plus(PDateTime dateTime) {
//        Objects.requireNonNull(dateTime)
//        return (this.toTimeStamp() + dateTime.toTimeStamp())/(60 * 60 * 1000)
//    }
//    todo 加属性，把多时区无关属性传进来
    long toTimeStamp() {
        if (TimeZoneContextHolder.isGray()) {
            return DateTimeFormat.DATE_TIME.convertToTimestamp(value, TimeZoneContextHolder.getTenantTimeZone())
        }
        ZonedDateTime zdt = ZonedDateTime.of(value, ZoneId.systemDefault())
        return zdt.toInstant().toEpochMilli()
    }

    int getYear() {
        return value.getYear()
    }

    int getMonth() {
        return value.getMonthValue()
    }

    int getDay() {
        return value.getDayOfMonth()
    }

    @Override
    int compareTo(PDateTime o) {
        Objects.requireNonNull(o)
        return Long.compare(this.toTimeStamp(), o.toTimeStamp())
    }

    @Override
    String toString() {
        return value.format(LONG_DATE_PATTERN_LINE)
    }
}
