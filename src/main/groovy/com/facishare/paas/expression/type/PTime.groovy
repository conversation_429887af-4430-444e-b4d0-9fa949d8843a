package com.facishare.paas.expression.type

import com.facishare.paas.timezone.DateTimeFormat
import com.facishare.paas.timezone.TimeZoneContextHolder
import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString
import groovy.util.logging.Slf4j
import java.math.RoundingMode
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

/**
 * Created by liyiguang on 2017/9/10.
 */
@ToString
@EqualsAndHashCode
@Slf4j
class PTime implements Comparable<PTime> {
    private static DateTimeFormatter LONG_DATE_PATTERN_LINE = DateTimeFormatter.ofPattern("HH:mm:ss")

    private final LocalDateTime value

    private static final ZoneId DEFAULT_TIME_ZONE = ZoneId.of("Asia/Shanghai")

    PTime(LocalDateTime value) {
        this.value = value
    }

    static PTime of(long timestamp) {
        if (TimeZoneContextHolder.isGray()) {
            def localDateTime = DateTimeFormat.TIME.convertToLocalDateTime(timestamp, TimeZoneContextHolder.getTenantTimeZone())
            return new PTime(localDateTime)
        }
        long baseTime = timestamp % (1000 * 60 * 60 * 24)
        if (baseTime >= 57600000) {
            baseTime = baseTime - 86400000
        }
        def systemDefault = ZoneId.systemDefault()
        if (DEFAULT_TIME_ZONE != systemDefault) {
            log.warn("The system time zone is not EAST 8,systemTimeZone : {}" ,systemDefault)
        }
        log.debug("The system time zone is {}", systemDefault)
        return new PTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(baseTime), systemDefault))
    }

    PTime plus(Hours hours) {
        return new PTime(value.plusHours(hours.getValue()))
    }

    PTime plus(Minutes minutes) {
        return new PTime(value.plusMinutes(minutes.getValue()))
    }

    PTime minus(Hours hours) {
        return new PTime(value.minusHours(hours.getValue()))
    }

    PTime minus(Minutes minutes) {
        return new PTime(value.minusMinutes(minutes.getValue()))
    }

    double minus(PTime time) {
        Objects.requireNonNull(time)
        BigDecimal ret = BigDecimal.valueOf(this.toTimeStamp() - time.toTimeStamp()) / (60 * 60 * 1000)
        ret = ret.setScale(2, RoundingMode.HALF_DOWN)
        return ret.toDouble()
    }

//    double plus(PTime time) {
//        Objects.requireNonNull(time)
//        return this.toTimeStamp() + time.toTimeStamp() / (60 * 60 * 1000)
//    }

    long toTimeStamp() {
        if (TimeZoneContextHolder.isGray()) {
            return DateTimeFormat.TIME.convertToTimestamp(value, TimeZoneContextHolder.getTenantTimeZone())
        }
        ZonedDateTime zdt = ZonedDateTime.of(value, ZoneId.systemDefault())
        return zdt.toInstant().toEpochMilli()
    }

    int getHour() {
        return value.getHour()
    }

    int getMinute() {
        return value.getMinute()
    }

    int getSecond() {
        return value.getSecond()
    }

    @Override
    int compareTo(PTime o) {

        Objects.requireNonNull(o)
        int cmp = (hour - o.hour)
        if (cmp == 0) {
            cmp = (minute - o.minute)
            if (cmp == 0) {
                cmp = (second - o.second)
            }
        }
        return cmp
    }

    @Override
    String toString() {
        return value.format(LONG_DATE_PATTERN_LINE)
    }
}
