package com.facishare.paas.expression.type

import com.facishare.paas.timezone.DateTimeFormat
import com.facishare.paas.timezone.TimeZoneContext
import com.facishare.paas.timezone.TimeZoneContextHolder
import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString

import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter

/**
 * Created by liyiguang on 2017/9/10.
 */
@ToString
@EqualsAndHashCode
class PDate implements Comparable<PDate> {

    private static DateTimeFormatter LONG_DATE_PATTERN_LINE = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    private static long DAY = 24 * 60 * 60 * 1000

    private final LocalDate value

    PDate(LocalDate value) {
        this.value = value
    }

    static PDate of(long timestamp) {
        if (TimeZoneContextHolder.isGray()) {
            def localDateTime = DateTimeFormat.DATE.convertToLocalDateTime(timestamp, TimeZoneContext.DEFAULT_TIME_ZONE)
            return new PDate(localDateTime.toLocalDate())
        }
        def dateTime = PDateTime.of(timestamp)
        return PDate.of(dateTime.getYear(), dateTime.getMonth(), dateTime.getDay())
    }

    static PDate of(String date) {
        return new PDate(LocalDate.parse(date, LONG_DATE_PATTERN_LINE))
    }

    static PDate of(int year, int month, int day) {
        return new PDate(LocalDate.of(year, month, day))
    }

    PDate plus(Years years) {
        return new PDate(value.plusYears(years.getValue()))
    }

    PDate plus(Months months) {
        return new PDate(value.plusMonths(months.getValue()))
    }

    PDate plus(Days days) {
        return new PDate(value.plusDays(days.getValue()))
    }

    PDate minus(Years years) {
        return new PDate(value.minusYears(years.getValue()))
    }

    PDate minus(Months months) {
        return new PDate(value.minusMonths(months.getValue()))
    }

    PDate minus(Days days) {
        return new PDate(value.minusDays(days.getValue()))
    }

    int minus(PDate date) {
        Objects.requireNonNull(date)
        return (this.toTimeStamp() - date.toTimeStamp()) / DAY
    }

//    double plus(PDate date) {
//        Objects.requireNonNull(date);
//        return this.toTimeStamp() + date.toTimeStamp()/DAY
//    }

    long toTimeStamp() {
        if (TimeZoneContextHolder.isGray()) {
            return DateTimeFormat.DATE.convertToTimestamp(value, TimeZoneContext.DEFAULT_TIME_ZONE)
        }
        Instant instant = value.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()
        return Date.from(instant).getTime()
    }

    @Override
    int compareTo(PDate o) {
        Objects.requireNonNull(o)
        int cmp = (year - o.year)
        if (cmp == 0) {
            cmp = (month - o.month)
            if (cmp == 0) {
                cmp = (day - o.day)
            }
        }
        return cmp
    }

    int getYear() {
        return value.getYear()
    }

    int getMonth() {
        return value.getMonthValue()
    }

    int getDay() {
        return value.getDayOfMonth()
    }

    @Override
    String toString() {
        return value.format(LONG_DATE_PATTERN_LINE)
    }
}
