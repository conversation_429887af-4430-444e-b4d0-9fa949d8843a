package com.facishare.paas.expression.functions

trait LogicCaseFunction {

    def <V> V CASE(expression, k1, V v1, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, k28, V v28, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            case k28: return v28
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, k28, V v28, k29, V v29, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            case k28: return v28
            case k29: return v29
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, k28, V v28, k29, V v29, k30, V v30, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            case k28: return v28
            case k29: return v29
            case k30: return v30
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, k28, V v28, k29, V v29, k30, V v30, k31, V v31, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            case k28: return v28
            case k29: return v29
            case k30: return v30
            case k31: return v31
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, k28, V v28, k29, V v29, k30, V v30, k31, V v31, k32, V v32, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            case k28: return v28
            case k29: return v29
            case k30: return v30
            case k31: return v31
            case k32: return v32
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, k28, V v28, k29, V v29, k30, V v30, k31, V v31, k32, V v32, k33, V v33, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            case k28: return v28
            case k29: return v29
            case k30: return v30
            case k31: return v31
            case k32: return v32
            case k33: return v33
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, k28, V v28, k29, V v29, k30, V v30, k31, V v31, k32, V v32, k33, V v33, k34, V v34, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            case k28: return v28
            case k29: return v29
            case k30: return v30
            case k31: return v31
            case k32: return v32
            case k33: return v33
            case k34: return v34
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, k28, V v28, k29, V v29, k30, V v30, k31, V v31, k32, V v32, k33, V v33, k34, V v34, k35, V v35, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            case k28: return v28
            case k29: return v29
            case k30: return v30
            case k31: return v31
            case k32: return v32
            case k33: return v33
            case k34: return v34
            case k35: return v35
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, k28, V v28, k29, V v29, k30, V v30, k31, V v31, k32, V v32, k33, V v33, k34, V v34, k35, V v35, k36, V v36, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            case k28: return v28
            case k29: return v29
            case k30: return v30
            case k31: return v31
            case k32: return v32
            case k33: return v33
            case k34: return v34
            case k35: return v35
            case k36: return v36
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, k28, V v28, k29, V v29, k30, V v30, k31, V v31, k32, V v32, k33, V v33, k34, V v34, k35, V v35, k36, V v36, k37, V v37, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            case k28: return v28
            case k29: return v29
            case k30: return v30
            case k31: return v31
            case k32: return v32
            case k33: return v33
            case k34: return v34
            case k35: return v35
            case k36: return v36
            case k37: return v37
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, k28, V v28, k29, V v29, k30, V v30, k31, V v31, k32, V v32, k33, V v33, k34, V v34, k35, V v35, k36, V v36, k37, V v37, k38, V v38, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            case k28: return v28
            case k29: return v29
            case k30: return v30
            case k31: return v31
            case k32: return v32
            case k33: return v33
            case k34: return v34
            case k35: return v35
            case k36: return v36
            case k37: return v37
            case k38: return v38
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, k28, V v28, k29, V v29, k30, V v30, k31, V v31, k32, V v32, k33, V v33, k34, V v34, k35, V v35, k36, V v36, k37, V v37, k38, V v38, k39, V v39, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            case k28: return v28
            case k29: return v29
            case k30: return v30
            case k31: return v31
            case k32: return v32
            case k33: return v33
            case k34: return v34
            case k35: return v35
            case k36: return v36
            case k37: return v37
            case k38: return v38
            case k39: return v39
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, k28, V v28, k29, V v29, k30, V v30, k31, V v31, k32, V v32, k33, V v33, k34, V v34, k35, V v35, k36, V v36, k37, V v37, k38, V v38, k39, V v39, k40, V v40, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            case k28: return v28
            case k29: return v29
            case k30: return v30
            case k31: return v31
            case k32: return v32
            case k33: return v33
            case k34: return v34
            case k35: return v35
            case k36: return v36
            case k37: return v37
            case k38: return v38
            case k39: return v39
            case k40: return v40
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, k28, V v28, k29, V v29, k30, V v30, k31, V v31, k32, V v32, k33, V v33, k34, V v34, k35, V v35, k36, V v36, k37, V v37, k38, V v38, k39, V v39, k40, V v40, k41, V v41, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            case k28: return v28
            case k29: return v29
            case k30: return v30
            case k31: return v31
            case k32: return v32
            case k33: return v33
            case k34: return v34
            case k35: return v35
            case k36: return v36
            case k37: return v37
            case k38: return v38
            case k39: return v39
            case k40: return v40
            case k41: return v41
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, k28, V v28, k29, V v29, k30, V v30, k31, V v31, k32, V v32, k33, V v33, k34, V v34, k35, V v35, k36, V v36, k37, V v37, k38, V v38, k39, V v39, k40, V v40, k41, V v41, k42, V v42, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            case k28: return v28
            case k29: return v29
            case k30: return v30
            case k31: return v31
            case k32: return v32
            case k33: return v33
            case k34: return v34
            case k35: return v35
            case k36: return v36
            case k37: return v37
            case k38: return v38
            case k39: return v39
            case k40: return v40
            case k41: return v41
            case k42: return v42
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, k28, V v28, k29, V v29, k30, V v30, k31, V v31, k32, V v32, k33, V v33, k34, V v34, k35, V v35, k36, V v36, k37, V v37, k38, V v38, k39, V v39, k40, V v40, k41, V v41, k42, V v42, k43, V v43, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            case k28: return v28
            case k29: return v29
            case k30: return v30
            case k31: return v31
            case k32: return v32
            case k33: return v33
            case k34: return v34
            case k35: return v35
            case k36: return v36
            case k37: return v37
            case k38: return v38
            case k39: return v39
            case k40: return v40
            case k41: return v41
            case k42: return v42
            case k43: return v43
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, k28, V v28, k29, V v29, k30, V v30, k31, V v31, k32, V v32, k33, V v33, k34, V v34, k35, V v35, k36, V v36, k37, V v37, k38, V v38, k39, V v39, k40, V v40, k41, V v41, k42, V v42, k43, V v43, k44, V v44, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            case k28: return v28
            case k29: return v29
            case k30: return v30
            case k31: return v31
            case k32: return v32
            case k33: return v33
            case k34: return v34
            case k35: return v35
            case k36: return v36
            case k37: return v37
            case k38: return v38
            case k39: return v39
            case k40: return v40
            case k41: return v41
            case k42: return v42
            case k43: return v43
            case k44: return v44
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, k28, V v28, k29, V v29, k30, V v30, k31, V v31, k32, V v32, k33, V v33, k34, V v34, k35, V v35, k36, V v36, k37, V v37, k38, V v38, k39, V v39, k40, V v40, k41, V v41, k42, V v42, k43, V v43, k44, V v44, k45, V v45, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            case k28: return v28
            case k29: return v29
            case k30: return v30
            case k31: return v31
            case k32: return v32
            case k33: return v33
            case k34: return v34
            case k35: return v35
            case k36: return v36
            case k37: return v37
            case k38: return v38
            case k39: return v39
            case k40: return v40
            case k41: return v41
            case k42: return v42
            case k43: return v43
            case k44: return v44
            case k45: return v45
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, k28, V v28, k29, V v29, k30, V v30, k31, V v31, k32, V v32, k33, V v33, k34, V v34, k35, V v35, k36, V v36, k37, V v37, k38, V v38, k39, V v39, k40, V v40, k41, V v41, k42, V v42, k43, V v43, k44, V v44, k45, V v45, k46, V v46, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            case k28: return v28
            case k29: return v29
            case k30: return v30
            case k31: return v31
            case k32: return v32
            case k33: return v33
            case k34: return v34
            case k35: return v35
            case k36: return v36
            case k37: return v37
            case k38: return v38
            case k39: return v39
            case k40: return v40
            case k41: return v41
            case k42: return v42
            case k43: return v43
            case k44: return v44
            case k45: return v45
            case k46: return v46
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, k28, V v28, k29, V v29, k30, V v30, k31, V v31, k32, V v32, k33, V v33, k34, V v34, k35, V v35, k36, V v36, k37, V v37, k38, V v38, k39, V v39, k40, V v40, k41, V v41, k42, V v42, k43, V v43, k44, V v44, k45, V v45, k46, V v46, k47, V v47, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            case k28: return v28
            case k29: return v29
            case k30: return v30
            case k31: return v31
            case k32: return v32
            case k33: return v33
            case k34: return v34
            case k35: return v35
            case k36: return v36
            case k37: return v37
            case k38: return v38
            case k39: return v39
            case k40: return v40
            case k41: return v41
            case k42: return v42
            case k43: return v43
            case k44: return v44
            case k45: return v45
            case k46: return v46
            case k47: return v47
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, k28, V v28, k29, V v29, k30, V v30, k31, V v31, k32, V v32, k33, V v33, k34, V v34, k35, V v35, k36, V v36, k37, V v37, k38, V v38, k39, V v39, k40, V v40, k41, V v41, k42, V v42, k43, V v43, k44, V v44, k45, V v45, k46, V v46, k47, V v47, k48, V v48, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            case k28: return v28
            case k29: return v29
            case k30: return v30
            case k31: return v31
            case k32: return v32
            case k33: return v33
            case k34: return v34
            case k35: return v35
            case k36: return v36
            case k37: return v37
            case k38: return v38
            case k39: return v39
            case k40: return v40
            case k41: return v41
            case k42: return v42
            case k43: return v43
            case k44: return v44
            case k45: return v45
            case k46: return v46
            case k47: return v47
            case k48: return v48
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, k28, V v28, k29, V v29, k30, V v30, k31, V v31, k32, V v32, k33, V v33, k34, V v34, k35, V v35, k36, V v36, k37, V v37, k38, V v38, k39, V v39, k40, V v40, k41, V v41, k42, V v42, k43, V v43, k44, V v44, k45, V v45, k46, V v46, k47, V v47, k48, V v48, k49, V v49, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            case k28: return v28
            case k29: return v29
            case k30: return v30
            case k31: return v31
            case k32: return v32
            case k33: return v33
            case k34: return v34
            case k35: return v35
            case k36: return v36
            case k37: return v37
            case k38: return v38
            case k39: return v39
            case k40: return v40
            case k41: return v41
            case k42: return v42
            case k43: return v43
            case k44: return v44
            case k45: return v45
            case k46: return v46
            case k47: return v47
            case k48: return v48
            case k49: return v49
            default: return defaultResult
        }
    }

    def <V> V CASE(expression, k1, V v1, k2, V v2, k3, V v3, k4, V v4, k5, V v5, k6, V v6, k7, V v7, k8, V v8, k9, V v9, k10, V v10, k11, V v11, k12, V v12, k13, V v13, k14, V v14, k15, V v15, k16, V v16, k17, V v17, k18, V v18, k19, V v19, k20, V v20, k21, V v21, k22, V v22, k23, V v23, k24, V v24, k25, V v25, k26, V v26, k27, V v27, k28, V v28, k29, V v29, k30, V v30, k31, V v31, k32, V v32, k33, V v33, k34, V v34, k35, V v35, k36, V v36, k37, V v37, k38, V v38, k39, V v39, k40, V v40, k41, V v41, k42, V v42, k43, V v43, k44, V v44, k45, V v45, k46, V v46, k47, V v47, k48, V v48, k49, V v49, k50, V v50, V defaultResult) {
        Objects.requireNonNull(expression)
        switch (expression) {
            case k1: return v1
            case k2: return v2
            case k3: return v3
            case k4: return v4
            case k5: return v5
            case k6: return v6
            case k7: return v7
            case k8: return v8
            case k9: return v9
            case k10: return v10
            case k11: return v11
            case k12: return v12
            case k13: return v13
            case k14: return v14
            case k15: return v15
            case k16: return v16
            case k17: return v17
            case k18: return v18
            case k19: return v19
            case k20: return v20
            case k21: return v21
            case k22: return v22
            case k23: return v23
            case k24: return v24
            case k25: return v25
            case k26: return v26
            case k27: return v27
            case k28: return v28
            case k29: return v29
            case k30: return v30
            case k31: return v31
            case k32: return v32
            case k33: return v33
            case k34: return v34
            case k35: return v35
            case k36: return v36
            case k37: return v37
            case k38: return v38
            case k39: return v39
            case k40: return v40
            case k41: return v41
            case k42: return v42
            case k43: return v43
            case k44: return v44
            case k45: return v45
            case k46: return v46
            case k47: return v47
            case k48: return v48
            case k49: return v49
            case k50: return v50
            default: return defaultResult
        }
    }

}

