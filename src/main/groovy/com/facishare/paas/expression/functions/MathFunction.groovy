package com.facishare.paas.expression.functions

/**
 * Created by l<PERSON><PERSON><PERSON><PERSON> on 2017/9/10.
 */
trait MathFunction {

    double ABS(value) {
        return Math.abs(value)
    }

    BigDecimal MIN(BigDecimal number1, BigDecimal number2) {
        if (number1 == null || number2 == null) {
            return null
        }
        return (number1 <= number2) ? number1 : number2
    }

    Double MIN(Number number1, Number number2) {
        if (number1 == null || number2 == null) {
            return null
        }
        return Math.min(number1.doubleValue(), number2.doubleValue())
    }

    BigDecimal MAX(BigDecimal number1, BigDecimal number2) {
        if (number1 == null || number2 == null) {
            return null
        }
        return (number1 >= number2) ? number1 : number2
    }

    Double MAX(Number number1, Number number2) {
        if (number1 == null || number2 == null) {
            return null
        }
        return Math.max(number1.doubleValue(), number2.doubleValue())
    }

    BigDecimal MULTIPLE(BigDecimal number1, BigDecimal number2) {
        return number1 * number2
    }

    double MULTIPLE(Number number1, Number number2) {
        BigDecimal bigDecimal1 = BigDecimal.valueOf(number1.doubleValue())
        BigDecimal bigDecimal2 = BigDecimal.valueOf(number2.doubleValue())
        return (bigDecimal1 * bigDecimal2).toDouble()
    }

    BigDecimal MOD(BigDecimal number1, BigDecimal number2) {
        return number1.divideAndRemainder(number2)[0]
    }

    double MOD(Number number1, Number number2) {
        BigDecimal bigDecimal1 = BigDecimal.valueOf(number1.doubleValue())
        BigDecimal bigDecimal2 = BigDecimal.valueOf(number2.doubleValue())

        return bigDecimal1.divideAndRemainder(bigDecimal2)[0].doubleValue()
    }

    BigDecimal ADDS(BigDecimal number1, BigDecimal number2) {
        return number1.add(number2)
    }

    double ADDS(Number number1, Number number2) {
        BigDecimal bigDecimal1 = BigDecimal.valueOf(number1.doubleValue())
        BigDecimal bigDecimal2 = BigDecimal.valueOf(number2.doubleValue())
        return bigDecimal1.add(bigDecimal2).doubleValue()
    }

    BigDecimal SUBTRACTS(BigDecimal number1, BigDecimal number2) {
        return number1.subtract(number2)
    }

    double SUBTRACTS(Number number1, Number number2) {
        BigDecimal bigDecimal1 = BigDecimal.valueOf(number1.doubleValue())
        BigDecimal bigDecimal2 = BigDecimal.valueOf(number2.doubleValue())
        return bigDecimal1.subtract(bigDecimal2).doubleValue()
    }

    BigDecimal ROUNDUP(BigDecimal number1, BigDecimal places) {
        return number1.setScale(places.intValue(), BigDecimal.ROUND_UP)
    }

    double ROUNDUP(Number param, Number places) {
        BigDecimal bigDecimal = new BigDecimal(String.valueOf(param)).setScale(places.intValue(), BigDecimal.ROUND_UP)
        return bigDecimal.doubleValue()
    }

    BigDecimal ROUNDDOWN(BigDecimal number1, BigDecimal places) {
        return number1.setScale(places.intValue(), BigDecimal.ROUND_DOWN)
    }

    double ROUNDDOWN(Number param, Number places) {
        BigDecimal bigDecimal = new BigDecimal(String.valueOf(param)).setScale(places.intValue(), BigDecimal.ROUND_DOWN)
        return bigDecimal.doubleValue()
    }

    BigDecimal ROUNDHALFUP(BigDecimal number1, BigDecimal places) {
        return number1.setScale(places.intValue(), BigDecimal.ROUND_HALF_UP)
    }

    double ROUNDHALFUP(Number param, Number places) {
        BigDecimal bigDecimal = new BigDecimal(String.valueOf(param)).setScale(places.intValue(), BigDecimal.ROUND_HALF_UP)
        return bigDecimal.doubleValue()
    }

    BigDecimal ROUNDHALFDOWN(BigDecimal number1, BigDecimal places) {
        return number1.setScale(places.intValue(), BigDecimal.ROUND_HALF_DOWN)
    }

    double ROUNDHALFDOWN(Number param, Number places) {
        BigDecimal bigDecimal = new BigDecimal(String.valueOf(param)).setScale(places.intValue(), BigDecimal.ROUND_HALF_DOWN)
        return bigDecimal.doubleValue()
    }
}