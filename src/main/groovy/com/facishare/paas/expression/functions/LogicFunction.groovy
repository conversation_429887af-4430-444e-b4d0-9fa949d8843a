package com.facishare.paas.expression.functions

import com.google.common.base.Strings
import org.apache.commons.lang3.StringUtils

import java.util.regex.Pattern

/**
 * Created by liyiguang on 2017/9/10.
 */
trait LogicFunction {

    private static Pattern pattern = Pattern.compile("""^[+-]?\\d+(,\\d+)*(.\\d+([Ee]\\d+)?)?\$""")

    boolean NOT(boolean arg) {
        return !arg
    }

    boolean AND(boolean ... args) {
        for (boolean a : args) {
            if(!a) {
                return false
            }
        }
        return true
    }

    boolean ISNULL(Object object) {
        if(object == null) {
            return true
        }
        if (object instanceof List) {
            return object.isEmpty()
        }
        return object instanceof String && Strings.isNullOrEmpty(String.valueOf(object))
    }

    boolean NOTNULL(Object object) {
        return !ISNULL(object)
    }

    boolean OR(boolean ... args) {
        for (boolean a : args) {
            if(a) {
                return true
            }
        }
        return false
    }


    def IF(boolean expression, Closure trueValue, Closure falseValue) {
        if (expression) {
            return trueValue.call()
        } else {
            return falseValue.call()
        }
    }

    def <T> T IF(boolean expression, T trueValue, T falseValue) {
        return expression ? trueValue : falseValue
    }

    BigDecimal IF(boolean expression, BigDecimal trueValue, int falseValue) {
        return expression ? trueValue : falseValue
    }

    BigDecimal IF(boolean expression, int trueValue, int falseValue) {
        return expression ? trueValue : falseValue
    }

    BigDecimal IF(boolean expression, int trueValue, BigDecimal falseValue) {
        return expression ? trueValue : falseValue
    }

    BigDecimal IF(boolean expression, BigDecimal trueValue, BigDecimal falseValue) {
        return expression ? trueValue : falseValue
    }

    BigDecimal IF(boolean expression, double trueValue, int falseValue) {
        return expression ? trueValue : falseValue
    }

    BigDecimal IF(boolean expression, int trueValue, double falseValue) {
        return expression ? trueValue : falseValue
    }


    BigDecimal IF(boolean expression, double trueValue, BigDecimal falseValue) {
        return expression ? trueValue : falseValue
    }

    BigDecimal IF(boolean expression, BigDecimal trueValue, double falseValue) {
        return expression ? trueValue : falseValue
    }

    boolean ISBLANK(String object) {
        return StringUtils.isBlank(object)
    }

    boolean NOTBLANK(String object) {
        return !ISBLANK(object)
    }

    boolean ISNUMBER(String object) {
        return pattern.matcher(object).matches()
    }

    def <T> T NULLVALUE(Object cond, T object) {
        return ISNULL(cond) ? object : null
    }

    def <T> T NULL2DEFAULT(T value, T defaultValue) {
        return ISNULL(value) ? defaultValue : value
    }
}