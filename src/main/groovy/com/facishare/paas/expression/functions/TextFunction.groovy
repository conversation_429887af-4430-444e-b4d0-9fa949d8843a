package com.facishare.paas.expression.functions

import com.google.common.base.Strings

/**
 * Created by l<PERSON><PERSON><PERSON><PERSON> on 2017/9/10.
 */
trait TextFunction {

    String[] digits = ["零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"]
    String[] radices = ["", "拾", "佰", "仟"]
    String[] bigRadices = ["", "万", "亿", "万", "京"]

    boolean STARTWITH(String s, String start) {
        if (s == null) {
            return false
        }
        return s.startsWith(start)
    }

    boolean ENDWITH(String s, String end) {
        if (s == null) {
            return false
        }
        return s.endsWith(end)
    }

    boolean EQUALS(String s, String start) {
        if (s == null) {
            return false
        }
        return s == start
    }

    long LEN(String s) {
        if (s == null) {
            return 0
        }
        return s.length()
    }

    boolean CONTAINS(String s, String start) {
        if (s == null) {
            return false
        }
        return s.contains(start)
    }

    boolean CONTAINS(Collection s, String start) {
        if (s == null) {
            return false
        }
        return CONTAINS(s.toString(), start)
    }

    boolean INCLUDES(String[] options, String[] array) {
        if (options == null) {
            return false
        }

        if (array == null) {
            return false
        }

        List<String> list = Arrays.asList(array)
        boolean result = false
        for (String option : options)
            if (list.contains(option)) {
                result = true
            } else {
                result = false
                break
            }
        return result
    }

    boolean ISSelectOptionVAL(String option, String[] array) {
        if (option == null) {
            return false
        }
        if (array == null) {
            return false
        }
        return Arrays.asList(array).contains(option)
    }


    String SPLIT(String value, String regex, int index) {
        if (value == null || regex == null) {
            return null
        }

        String[] ret = value.split(regex)
        if (index < ret.length && index >= 0) {
            return ret[index]
        }
        return null
    }

    String TRIM(String value) {
        if (value == null) {
            return null
        }
        int len = value.length()
        int st = 0
        char[] val = value.toCharArray()

        while ((st < len) && ((val[st] <= ' ') || (val[st] == 0x202D) || (val[st] == 0x202C))) {
            st++
        }
        while ((st < len) && ((val[len - 1] <= ' ') || (val[len - 1] == 0x202D) || (val[len - 1] == 0x202C))) {
            len--
        }
        return ((st > 0) || (len < value.length())) ? value.substring(st, len) : value
    }

    boolean MATCH(String value, String regexp) {
        if (value == null) {
            return false
        }
        return value.matches(regexp)
    }


    String NUMBERSTRINGRMB(int number) {
        return NUMBERSTRINGRMB(BigDecimal.valueOf(number))
    }

    String NUMBERSTRING(int number) {
        return NUMBERSTRING(BigDecimal.valueOf(number))
    }

    String NUMBERSTRINGRMB(String number) {
        if (Strings.isNullOrEmpty(number)) {
            return null;
        }
        return NUMBERSTRINGRMB(new BigDecimal(number));
    }

    String NUMBERSTRINGRMB(BigDecimal number) {
        if (number == null) {
            return null;
        }
        //去掉小数点后多余的0
        number = number.stripTrailingZeros();

        String[] decimals = ["角", "分"]
        StringBuffer outputCharacters = new StringBuffer()

        String strVal = (number * 100).toPlainString().split("\\.")[0]
        String head = ""
        String end = ""
        if (number == 0 || "00" == strVal) {
            head = "0"
            end = "00"
        } else {
            if (strVal.length() >= 2) {
                head = strVal.substring(0, strVal.length() - 2)         //整数部分
                end = strVal.substring(strVal.length() - 2)
            } else {
                end = "0" + strVal
            }
        }

        if (head.contains('-')) {
            outputCharacters.append("负")
            head = head.replace('-', "")
        }

        boolean hasHead = false;
        //整数部分
        if (!Strings.isNullOrEmpty(head) && Long.parseLong(head) > 0) {
            hasHead = true
            int zeroCount = 0
            for (int i = 0; i < head.length(); i++) //从最高位开始依次往下处理
            {
                int p = head.length() - i - 1    // 第几位
                String d = head.substring(i, i + 1)
                int quotient = p / 4
                int modulus = p % 4
                if ("0" == d) {
                    zeroCount++
                } else {
                    if (zeroCount > 0) {
                        outputCharacters.append(digits[0])
                    }
                    zeroCount = 0
                    outputCharacters.append(digits[Integer.parseInt(d)]).append(radices[modulus])
                }
                if (modulus == 0 && zeroCount < 4) {
                    outputCharacters.append(bigRadices[quotient])
                }
            }
            outputCharacters.append("元")
        }

        //小数部分
        if ("" != end && "00" != end) {
            char[] endChars = end.toCharArray()
            for (int i = 0; i < 2; i++) {
                String d = endChars[i]
                if ("0" != d || (i == 0 && hasHead)) {
                    outputCharacters.append(digits[Integer.parseInt(d)])
                    if ("0" != d) {
                        outputCharacters.append(decimals[i])
                    }
                }
            }
        }

        if (Strings.isNullOrEmpty(outputCharacters.toString())) {
            outputCharacters.append("零元")
        }
        if (end == "00" && !number.toPlainString().contains(".")) {
            outputCharacters.append("整")
        }
        return outputCharacters.toString()
    }


    String NUMBERSTRING(BigDecimal number) {
        if (number == null) {
            return null;
        }
        if (number == 0) {
            return digits[0]
        }
        StringBuffer outputCharacters = new StringBuffer()
        String strVal = number.stripTrailingZeros().toPlainString()
        String head = ""
        String end = ""

        String[] parts = strVal.split("\\.")
        if (parts.length > 1) {
            head = parts[0]
            end = parts[1]
        } else {
            head = strVal
        }

        if (head.contains('-')) {
            outputCharacters.append("负")
            head = head.replace('-', "")
        }
        //整数部分
        if (!Strings.isNullOrEmpty(head)) {
            if (Long.parseLong(head) > 0) {

                int zeroCount = 0
                for (int i = 0; i < head.length(); i++) //从最高位开始依次往下处理
                {
                    int p = head.length() - i - 1    // 第几位
                    String d = head.substring(i, i + 1)
                    int quotient = p / 4
                    int modulus = p % 4
                    if ("0".equals(d)) {
                        zeroCount++
                    } else {
                        if (zeroCount > 0) {
                            outputCharacters.append(digits[0])
                        }
                        zeroCount = 0
                        outputCharacters.append(digits[Integer.parseInt(d)]).append(radices[modulus])
                    }
                    if (modulus == 0 && zeroCount < 4) {
                        outputCharacters.append(bigRadices[quotient])
                    }
                }
            } else if (Long.parseLong(head) == 0) {
                outputCharacters.append("零")
            }
        }

        //小数部分
        if (!Strings.isNullOrEmpty(end) && Long.parseLong(end) > 0) {
            outputCharacters.append("点")
            for (int i = 0; i < end.length(); i++) {
                String d = end.substring(i, i + 1)
                outputCharacters.append(digits[Integer.parseInt(d)])
            }
        }

        return outputCharacters.toString()
    }


    BigDecimal VALUE(String value) {
        try {
            BigDecimal bigDecimal = new BigDecimal(value)
            return bigDecimal
        } catch (Exception) {
        }
        return null
    }

    String NULL2EMPTY(Object value) {
        return value == null ? "" : String.valueOf(value)
    }
}
