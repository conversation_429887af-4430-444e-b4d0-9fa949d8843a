package com.facishare.paas.expression.extension

import java.math.MathContext

class NumberDivExtension {
    // This is an arbitrary value, picked as a reasonable choice for a precision
    // for typical user math when a non-terminating result would otherwise occur.
    private static final int DIVISION_EXTRA_PRECISION = 15;

    static Number div(Number left, Number right) {
        BigDecimal bigLeft = toBigDecimal(left);
        BigDecimal bigRight = toBigDecimal(right);
        try {
            return bigLeft.divide(bigRight);
        } catch (ArithmeticException e) {
            // set a DEFAULT precision if otherwise non-terminating
            int precision = Math.max(bigLeft.precision(), bigRight.precision()) + DIVISION_EXTRA_PRECISION;
            return bigLeft.divide(bigRight, new MathContext(precision));
        }
    }

    private static BigDecimal toBigDecimal(Number n) {
        return (n instanceof BigDecimal ? (BigDecimal) n : new BigDecimal(n.toString()));
    }
}
