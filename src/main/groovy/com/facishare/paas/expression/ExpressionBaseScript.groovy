package com.facishare.paas.expression

import com.facishare.paas.expression.extension.NumberDivExtension
import com.facishare.paas.expression.functions.*
import com.facishare.paas.expression.util.FunctionUtil
import com.google.common.base.Strings

/**
 * Created by l<PERSON><PERSON><PERSON><PERSON> on 2017/9/7.
 */
abstract class ExpressionBaseScript extends Script implements
        LogicFunction, DateTimeFunction, TextFunction, MathFunction, CollectionFunction, LogicCaseFunction {

    Object runWithExtension() {
        //通过参数控制是否使用div重载方法
        boolean useNumberDivExtension = Boolean.TRUE == getVariable(FunctionUtil.USE_NUMBER_DIV_EXTENSION)
        if (useNumberDivExtension) {
            use(NumberDivExtension) {
                return this.run()
            }
        } else {
            return this.run()
        }
    }

    Object getVariable(String key) {
        Binding binding = getBinding()
        if (binding == null || !binding.hasVariable(key)) {
            return null
        }
        return binding.getVariable(key)
    }

    BigDecimal EXCHANGERATE(String fromCurrency, String toCurrency) {
        return EXCHANGERATE(fromCurrency, toCurrency, null)
    }

    BigDecimal EXCHANGERATE(String fromCurrency, String toCurrency, BigDecimal defaultValue) {
        if (Objects.equals(fromCurrency, toCurrency)) {
            return new BigDecimal("1")
        }
        String key = FunctionUtil.buildExchangeRateKey(fromCurrency, toCurrency)
        Map<String, String> exchangeRateMap = getVariable(FunctionUtil.EXCHANGE_RATE_KEY)
        if (exchangeRateMap == null || Strings.isNullOrEmpty(exchangeRateMap.get(key))) {
            return defaultValue
        }
        return new BigDecimal(exchangeRateMap.get(key))
    }

}
