package com.facishare.paas.expression;

import com.facishare.paas.expression.exception.ExpressionCompileException;
import com.facishare.paas.expression.util.ExpressionContext;
import com.github.jedis.support.MergeJedisCmd;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.groovy.control.CompilationFailedException;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 表达式引擎服务实现类
 * <p>
 * Created by liyiguang on 2017/7/23.
 */
@Service("expressionService")
@Slf4j
public class ExpressionServiceImpl implements ExpressionService {

    private final ExpressionEngine expressionEngine;

    public ExpressionServiceImpl() {
        this.expressionEngine = new ExpressionEngine();
    }

    public ExpressionServiceImpl(MergeJedisCmd expressionRedisSupport, int expireTime) {
        this.expressionEngine = new ExpressionEngine(expressionRedisSupport, expireTime);
    }

    @Override
    public <T> T evaluate(String expression, Map<String, Object> bindings) {
        return evaluate(expression, bindings, false);
    }

    @Override
    public <T> T evaluate(String expression, Map<String, Object> bindings, boolean useScriptPool) {
        return (T) expressionEngine.evaluate(expression, bindings, useScriptPool);
    }

    @Override
    public void compile(String expression, Map<String, Type> bindTypes, Type returnType) {
        try {
            initExpressionContext(bindTypes);

            StringBuilder sb = new StringBuilder();
            sb.append("import groovy.transform.Field\n");
            for (String key : bindTypes.keySet()) {
                sb.append(variableDefine(key, bindTypes.get(key)));
                sb.append("\n");
            }
            sb.append(returnType(returnType) + " value() {");
            sb.append(expression.trim());
            sb.append("}");

            log.debug("compile script:\n{}", sb);
            expressionEngine.compile(sb.toString());
        } catch (CompilationFailedException e) {
            log.warn("compile failed, expression: {},bindingTypes: {},returnType: {}", expression, bindTypes, returnType, e);
            String message = e.getMessage();
            throw new ExpressionCompileException(message);
        } finally {
            ExpressionContext.clear();
        }
    }

    private void initExpressionContext(Map<String, Type> bindTypes) {
        if (Objects.isNull(bindTypes) || bindTypes.isEmpty()) {
            return;
        }
        Map<String, Class<?>> bindingVariableTypes = new HashMap<>();
        bindTypes.forEach((k, v) -> bindingVariableTypes.put(k, v.getClazz()));
        ExpressionContext.get().setBindingVariableTypes(bindingVariableTypes);
    }

    private String variableDefine(String key, Type type) {
        switch (type) {
            case INTEGER:
                return "@Field long " + key;
            case DECIMAL:
                return "@Field BigDecimal " + key;
            case STRING:
                return "@Field String " + key;
            case DATETIME:
                return "@Field PDateTime " + key;
            case DATE:
                return "@Field PDate " + key;
            case TIME:
                return "@Field PTime " + key;
            case BOOLEAN:
                return "@Field boolean " + key;
            case LIST:
                return "@Field List " + key;
            default:
                return "";
        }
    }

    public String returnType(Type type) {
        switch (type) {
            case INTEGER:
                return "long";
            case DECIMAL:
                return "BigDecimal";
            case STRING:
                return "String";
            case DATETIME:
                return "PDateTime";
            case DATE:
                return "PDate";
            case TIME:
                return "PTime";
            case BOOLEAN:
                return "boolean";
            case LIST:
                return "List";
            default:
                return "";
        }
    }
}
