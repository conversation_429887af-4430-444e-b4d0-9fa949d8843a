package com.facishare.paas.expression;

import com.facishare.paas.expression.util.ExpressionContext;
import com.google.common.collect.ImmutableSet;
import groovy.util.Eval;
import org.codehaus.groovy.ast.AnnotationNode;
import org.codehaus.groovy.ast.DynamicVariable;
import org.codehaus.groovy.ast.expr.*;
import org.codehaus.groovy.control.customizers.SecureASTCustomizer;

import java.util.List;
import java.util.Set;

/**
 * Created by zhouwr on 2023/2/24.
 */
public class SecuritySandboxExpressionChecker implements SecureASTCustomizer.ExpressionChecker {

    private static final Set<String> CLASS_BLACK_LIST = ImmutableSet.of(
            Eval.class.getName(), System.class.getName(), Runtime.class.getName()
    );

    protected static final Set<String> METHOD_BLACK_LIST = ImmutableSet.of(
            "getClass",
            "getMetaClass",
            "metaClass",
            "class",
            "forName",
            "wait",
            "notify",
            "notifyAll",
            "invokeMethod",
            "finalize",
            "evaluate",
            "me",
            "getRuntime",
            "exec"
    );

    @Override
    public boolean isAuthorized(Expression expression) {
        //禁用函数指针
        if (expression instanceof MethodPointerExpression) {
            return false;
        }

        //禁用构造函数
        if (expression instanceof ConstructorCallExpression) {
            return false;
        }

        if (expression instanceof ClassExpression) {
            if (CLASS_BLACK_LIST.contains(expression.getType().getName())) {
                return false;
            }
        }

        /**
         * 禁止构造数组
         */
        if (expression instanceof ArrayExpression) {
            return false;
        }

        //Range表达式 1..10
        if (expression instanceof RangeExpression) {
            return false;
        }

        //方法调用黑名单
        if (expression instanceof MethodCallExpression) {
            String methodName = ((MethodCallExpression) expression).getMethodAsString();
            return !METHOD_BLACK_LIST.contains(methodName);
        }

        //禁用字符串的乘法
//        if (expression instanceof BinaryExpression) {
//            BinaryExpression binaryExpression = (BinaryExpression) expression;
//            Expression leftExpression = binaryExpression.getLeftExpression();
//            if ("*".equals(binaryExpression.getOperation().getText())) {
//                Class<?> leftType = leftExpression.getType().getTypeClass();
//                if (leftExpression instanceof VariableExpression) {
//                    VariableExpression variableExpression = (VariableExpression) leftExpression;
//                    if (variableExpression.getAccessedVariable() instanceof DynamicVariable) {
//                        leftType = ExpressionContext.get().getBindingVariableType(variableExpression.getName());
//                    }
//                }
//                if (leftType == String.class) {
//                    throw new SecurityException("String multiplication is not allowed");
//                }
//            }
//        }

        //禁用注解
        List<AnnotationNode> annotationNodeList = expression.getAnnotations();
        if (annotationNodeList != null && !annotationNodeList.isEmpty()) {
            return false;
        }

        return true;
    }
}
