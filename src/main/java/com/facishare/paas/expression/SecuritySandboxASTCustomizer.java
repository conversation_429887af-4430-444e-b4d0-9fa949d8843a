package com.facishare.paas.expression;

import org.codehaus.groovy.ast.AnnotationNode;
import org.codehaus.groovy.ast.ClassNode;
import org.codehaus.groovy.ast.expr.ClosureExpression;
import org.codehaus.groovy.ast.stmt.BlockStatement;
import org.codehaus.groovy.ast.stmt.ExpressionStatement;
import org.codehaus.groovy.classgen.GeneratorContext;
import org.codehaus.groovy.control.CompilationFailedException;
import org.codehaus.groovy.control.SourceUnit;
import org.codehaus.groovy.control.customizers.SecureASTCustomizer;

import java.util.List;

/**
 * Created by zhouwr on 2023/3/1.
 */
public class SecuritySandboxASTCustomizer extends SecureASTCustomizer {

    private final boolean runtime;

    public SecuritySandboxASTCustomizer(boolean isRuntime) {
        this.runtime = isRuntime;
    }

    @Override
    public void call(final SourceUnit source, final GeneratorContext context, final ClassNode classNode) throws CompilationFailedException {
        //禁用class的注解
        List<AnnotationNode> classAnnotations = classNode.getAnnotations();
        if (classAnnotations != null && !classAnnotations.isEmpty()) {
            classAnnotations.forEach(classAnnotation -> {
                if (classAnnotation.getClassNode().getName().equals("groovy.transform.ASTTest")) {
                    throw new SecurityException("Annotation [groovy.transform.ASTTest] is not allowed");
                }
            });
        }

        classNode.getMethods().forEach(methodNode -> {
            if ((!runtime && methodNode.isScriptBody()) || methodNode.isSynthetic() || "main".equals(methodNode.getName())) {
                return;
            }
            //禁用method的注解
            List<AnnotationNode> methodAnnotations = methodNode.getAnnotations();
            if (methodAnnotations != null && !methodAnnotations.isEmpty()) {
                throw new SecurityException("Annotation is not allowed");
            }
            //禁用多行代码
            if (methodNode.getCode() instanceof BlockStatement) {
                BlockStatement body = (BlockStatement) methodNode.getCode();
                if (body.getStatements().size() > 1) {
                    throw new SecurityException("Multiline code is not allowed");
                }
                body.getStatements().forEach(statement -> {
                    if (statement instanceof ExpressionStatement) {
                        ExpressionStatement expressionStatement = (ExpressionStatement) statement;
                        if (expressionStatement.getExpression() instanceof ClosureExpression) {
                            throw new SecurityException("ClosureExpression is not allowed");
                        }
                    }
                });
            }
        });
        super.call(source, context, classNode);
    }
}
