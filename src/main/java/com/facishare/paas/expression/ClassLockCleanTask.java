package com.facishare.paas.expression;

import com.fxiaoke.common.TaskScheduler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Iterator;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
public class ClassLockCleanTask {
    private final ClassLoader classLoader;

    public ClassLockCleanTask(ClassLoader classLoader) {
        this.classLoader = classLoader;
    }

    public void start() {
        // 上来先调用一次，把错误信息提前暴露，不同版本jdk的内部结构不一样
        TaskScheduler.system().scheduleWithFixedDelay(this::doClean, 1, 60, TimeUnit.MINUTES);
        log.warn("clean class lock task start");
    }

    @SuppressWarnings("all")
    private void doClean() {
        try {
            ClassLoader tmpLoader = classLoader;
            while (tmpLoader != null) {
                Optional<Field> map = Arrays.stream(tmpLoader.getClass().getFields()).filter(f -> "parallelLockMap".equals(f.getName())).findFirst();
                if (map.isPresent()) {
                    Field field = map.get();
                    Map<String, Object> values;
                    try {
                        field.setAccessible(true);
                        values = (Map<String, Object>) field.get(tmpLoader);
                    } catch (IllegalAccessException e) {
                        log.warn("cannot get [parallelLockMap] field from {}", tmpLoader, e);
                        return;
                    }
                    removeExpressionScript(values, tmpLoader);
                } else {
                    log.warn("NOTFOUND [parallelLockMap] field from {}", tmpLoader);
                }
                tmpLoader = tmpLoader.getParent();
            }
        } catch (Exception e) {
            log.warn("clean class lock failed", e);
        }
    }

    private void removeExpressionScript(Map<String, Object> parallelLockMap, ClassLoader loader) {
        int fxNum = 0;
        int gsNum = 0;
        int total = parallelLockMap.size();
        Iterator<Map.Entry<String, Object>> it = parallelLockMap.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<String, Object> next = it.next();
            if (StringUtils.contains(next.getKey(), "FXExpressionScript")) {
                it.remove();
                fxNum++;
            } else if (StringUtils.contains(next.getKey(), "GStringTemplateScript")) {
                it.remove();
                gsNum++;
            } else {
                // do nothing
            }
        }
        log.warn("clean class lock,total:{},fx:{},gs:{},loader:{}", total, fxNum, gsNum, loader);
    }

}
