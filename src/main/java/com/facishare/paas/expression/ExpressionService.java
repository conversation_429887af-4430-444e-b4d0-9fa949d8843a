package com.facishare.paas.expression;

import java.util.Map;

/**
 * 表达式引擎服务接口
 * <p>
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/7/23.
 */
public interface ExpressionService {

    /**
     * 表达式求职
     *
     * @param expression
     * @param bindings
     * @param <T>
     * @return
     */
    <T> T evaluate(String expression, Map<String, Object> bindings);

    /**
     * 表达式求职
     *
     * @param expression   要计算的表达式
     * @param bindings     表达式中的变量绑定的值
     * @param useScriptPool 是否使用脚本池
     * @param <T>
     * @return
     */
    <T> T evaluate(String expression, Map<String, Object> bindings, boolean useScriptPool);

    /**
     * 编译检查
     *
     * @param expression
     * @param bindTypes
     * @return
     */
    void compile(String expression, Map<String, Type> bindTypes, Type returnType);
}
