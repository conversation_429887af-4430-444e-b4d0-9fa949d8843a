package com.facishare.paas.expression;

import org.codehaus.groovy.transform.stc.AbstractTypeCheckingExtension;
import org.codehaus.groovy.transform.stc.StaticTypeCheckingVisitor;

/**
 * Created by liyiguang on 2018/3/3.
 */
public class TypeCheckEx extends AbstractTypeCheckingExtension {
    public TypeCheckEx(StaticTypeCheckingVisitor typeCheckingVisitor) {
        super(typeCheckingVisitor);
    }

}
