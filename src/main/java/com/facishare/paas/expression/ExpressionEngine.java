package com.facishare.paas.expression;

import com.facishare.paas.expression.redis.CodeRedisCache;
import com.facishare.paas.expression.transform.ExpressionTransform;
import com.facishare.paas.expression.util.ExpressionContext;
import com.fxiaoke.common.StopWatch;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import groovy.lang.*;
import groovy.transform.CompileStatic;
import groovy.transform.TypeChecked;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.pool2.BaseKeyedPooledObjectFactory;
import org.apache.commons.pool2.KeyedObjectPool;
import org.apache.commons.pool2.KeyedPooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericKeyedObjectPool;
import org.apache.commons.pool2.impl.GenericKeyedObjectPoolConfig;
import org.codehaus.groovy.ast.ModuleNode;
import org.codehaus.groovy.ast.stmt.BlockStatement;
import org.codehaus.groovy.ast.stmt.ExpressionStatement;
import org.codehaus.groovy.control.*;
import org.codehaus.groovy.control.customizers.ASTTransformationCustomizer;
import org.codehaus.groovy.control.customizers.SecureASTCustomizer;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.codehaus.groovy.control.Phases.INSTRUCTION_SELECTION;

/**
 * 表达式引擎
 * <p>
 * Created by liyiguang on 2017/7/23.
 */
@Slf4j
public class ExpressionEngine {
    private static final String JDK_VERSION = "1.8";
    private static final String DEFAULT_IMPORT_CLASSES = "com.facishare.paas.expression.type.";
    private static final ThreadLocal<String> EXPRESSION_CACHE = new ThreadLocal<>();

    private final Cache<String, Class<?>> classCache;
    private final KeyedObjectPool<String, Script> scriptPool;
    private final CompilerConfiguration compileConfig;
    private final GroovyClassLoader loader;

    public ExpressionEngine() {
        this(null, 0);
    }

    public ExpressionEngine(MergeJedisCmd redisSupport, int expireTime) {
        //构造Class的缓存
        classCache = CacheBuilder.newBuilder()
                .expireAfterAccess(1, TimeUnit.HOURS)
                .maximumSize(10000)
                .initialCapacity(6000)
                .concurrencyLevel(8)
                .recordStats()
                .build();

        //构造Script实例池
        scriptPool = buildScriptPool();

        compileConfig = new CompilerConfiguration();
        //指定JDK版本
        compileConfig.setTargetBytecode(JDK_VERSION);
        //使用静态编译
        compileConfig.addCompilationCustomizers(new ASTTransformationCustomizer(CompileStatic.class));
        setSecureASTCustomizer(compileConfig, false);
        setTypeCheck(compileConfig);
        setScriptBaseClass(compileConfig);

        CompilerConfiguration runtimeConfig = new CompilerConfiguration();
        //指定JDK版本
        runtimeConfig.setTargetBytecode(JDK_VERSION);
        setSecureASTCustomizer(runtimeConfig, true);
        setScriptBaseClass(runtimeConfig);
        runtimeConfig.addCompilationCustomizers(new ASTTransformationCustomizer(ExpressionTransform.class));

        if (redisSupport != null) {
            loader = new ExpressionClassLoader(GroovyClassLoader.class.getClassLoader(), runtimeConfig, new CodeRedisCache(redisSupport, expireTime));
        } else {
            loader = new ExpressionClassLoader(GroovyClassLoader.class.getClassLoader(), runtimeConfig, null);
        }

        //不查找自定义的MetaClass
        GroovySystem.getMetaClassRegistry().getMetaClassCreationHandler().setDisableCustomMetaClassLookup(true);

        //启动定时清理parallelLockMap的任务
        startCleanTask();
    }

    private KeyedObjectPool<String, Script> buildScriptPool() {
        GenericKeyedObjectPoolConfig<Script> config = new GenericKeyedObjectPoolConfig<>();
        config.setMaxTotalPerKey(2);
        config.setMaxIdlePerKey(1);
//        config.setMinIdlePerKey(1);
        config.setMaxTotal(300);

        KeyedPooledObjectFactory<String, Script> factory = new BaseKeyedPooledObjectFactory<String, Script>() {
            @Override
            public Script create(String key) {
                String expression = EXPRESSION_CACHE.get();
                return createScript(expression, key);
            }

            @Override
            public PooledObject<Script> wrap(Script script) {
                return new DefaultPooledObject<>(script);
            }
        };

        return new GenericKeyedObjectPool<>(factory, config);
    }

    private void startCleanTask() {
        new ClassLockCleanTask(loader).start();
    }

    private void setScriptBaseClass(CompilerConfiguration config) {
        config.setScriptBaseClass(ExpressionBaseScript.class.getCanonicalName());
    }

    private void setTypeCheck(CompilerConfiguration config) {
        config.addCompilationCustomizers(new ASTTransformationCustomizer(Collections.singletonMap("extensions", "com.facishare.paas.expression.TypeCheckEx"), TypeChecked.class));
    }

    private void setSecureASTCustomizer(CompilerConfiguration config, boolean isRuntime) {
        SecureASTCustomizer secureASTCustomizer = new SecuritySandboxASTCustomizer(isRuntime);
        secureASTCustomizer.addExpressionCheckers(new SecuritySandboxExpressionChecker());
        secureASTCustomizer.setStatementsWhitelist(Lists.newArrayList(ExpressionStatement.class, BlockStatement.class));
        secureASTCustomizer.setIndirectImportCheckEnabled(true);
        secureASTCustomizer.setStarImportsWhitelist(Lists.newArrayList(DEFAULT_IMPORT_CLASSES));
        secureASTCustomizer.setImportsWhitelist(Lists.newArrayList("java.lang.Object", "groovy.transform.Field"));
        secureASTCustomizer.setStaticImportsWhitelist(Lists.newArrayList());
        secureASTCustomizer.setStaticStarImportsWhitelist(Lists.newArrayList("java.lang.Object.*"));
        config.addCompilationCustomizers(secureASTCustomizer);
    }

    public Object evaluate(String expression, Map<String, Object> bindings) {
        return evaluate(expression, bindings, false);
    }

    public Object evaluate(String expression, Map<String, Object> bindings, boolean useScriptPool) {
        StopWatch stopWatch = StopWatch.createStarted("ExpressionEngine.evaluate." + useScriptPool);
        Script script = null;
        String scriptKey = null;
        try {
            initExpressionContext(bindings);
            EXPRESSION_CACHE.set(expression);
            scriptKey = calcSHA1(expression);
            stopWatch.lap("calcSHA1");
            if (useScriptPool) {
                script = scriptPool.borrowObject(scriptKey);
                stopWatch.lap("getScriptFromPool");
            } else {
                script = createScript(expression, scriptKey);
                stopWatch.lap("createScript");
            }
            if (script instanceof NullScript) {
                log.warn("use NullScript,expression:{}", expression);
                return null;
            }

            Binding binding = createBinding(bindings);
            stopWatch.lap("createBinding");

            script.setBinding(binding);
            Object result = ((ExpressionBaseScript) script).runWithExtension();
            stopWatch.lap("runScript");

            return result;
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            }
            throw new RuntimeException(e);
        } finally {
            ExpressionContext.clear();
            EXPRESSION_CACHE.remove();
            if (useScriptPool && Objects.nonNull(script)) {
                //清空缓存中script的binding
                if (!(script instanceof NullScript)) {
                    script.setBinding(null);
                }
                //归还script对象实例
                returnScriptToPool(scriptKey, script, expression);
                stopWatch.lap("returnScriptToPool");
            }
            stopWatch.logSlow(50);
        }
    }

    private void initExpressionContext(Map<String, Object> bindings) {
        if (Objects.isNull(bindings) || bindings.isEmpty()) {
            return;
        }
        Map<String, Class<?>> bindingTypes = new HashMap<>();
        bindings.forEach((key, value) -> {
            if (Objects.isNull(value)) {
                return;
            }
            bindingTypes.put(key, value.getClass());
        });
        ExpressionContext.get().setBindingVariableTypes(bindingTypes);
    }

    private void returnScriptToPool(String key, Script script, String expression) {
        if (Objects.isNull(script)) {
            return;
        }
        try {
            scriptPool.returnObject(key, script);
        } catch (Exception e) {
            log.warn("returnScript failed,expression:{} ", expression, e);
        }
    }

    private Script createScript(String expression, String hashKey) {
        if (log.isDebugEnabled()) {
            log.debug("createScript,expression:{}", expression);
        }
        StopWatch stopWatch = StopWatch.createStarted("ExpressionEngine.createScript");
        try {
            Class<?> clazz;
            AtomicBoolean needParse = new AtomicBoolean(false);
            try {
                clazz = classCache.get(hashKey, () -> {
                    needParse.set(true);
                    Class<?> newClazz = parseClass(hashKey, expression);
                    stopWatch.lap("parseClass");
                    return newClazz;
                });
            } catch (ExecutionException e) {
                throw new RuntimeException(e);
            }
            if (!needParse.get()) {
                stopWatch.lap("getClassFromCache");
            }
            //编译失败直接返回空
            if (NullScript.class.equals(clazz)) {
                return new NullScript();
            }
            Script script = newInstance(clazz);
            stopWatch.lap("newInstance");
            return script;
        } finally {
            stopWatch.logSlow(30);
        }
    }

    private Script newInstance(Class<?> scriptClass) {
        try {
            return (Script) scriptClass.newInstance();
        } catch (Exception e) {
            throw new GroovyRuntimeException("Failed to create Script instance for class: " + scriptClass + ". Reason: " + e, e);
        }
    }

    private Binding createBinding(Map<String, Object> bindings) {
        Binding binding = new Binding();
        if (Objects.nonNull(bindings)) {
            for (Map.Entry<String, Object> entry : bindings.entrySet()) {
                if (entry.getValue() instanceof Double || entry.getValue() instanceof Float) {
                    Number value = (Number) entry.getValue();
                    binding.setVariable(entry.getKey(), BigDecimal.valueOf(value.doubleValue()));
                } else {
                    binding.setVariable(entry.getKey(), entry.getValue());
                }
            }
        }
        return binding;
    }

    private Class<?> parseClass(String hashKey, String expression) {
        try {
            return loader.parseClass(expression, getScriptName(hashKey));
        } catch (Exception e) {
            log.warn("parseClass failed,expression:{} ", expression, e);
            return NullScript.class;
        }
    }

    private String getScriptName(String hashKey) {
        return "FXExpressionScript" + hashKey + ".groovy";
    }

    private String calcSHA1(String script) {
        if (Objects.isNull(script)) {
            return "";
        }
        return DigestUtils.sha1Hex(script);
    }

    public void compile(String expression) {
        GroovyCodeSource groovyCodeSource = new GroovyCodeSource(expression, "script", "/groovy/script");
        CompilationUnit compilationUnit = new CompilationUnit(compileConfig);
        compilationUnit.addSource(groovyCodeSource.getName(), groovyCodeSource.getScriptText());
        addDefaultImport(compilationUnit);

        compilationUnit.compile(INSTRUCTION_SELECTION);
    }

    private void addDefaultImport(CompilationUnit compilationUnit) {
        compilationUnit.addPhaseOperation(new CompilationUnit.SourceUnitOperation() {
            public void call(SourceUnit source) throws CompilationFailedException {
                ModuleNode ast = source.getAST();
                ast.addStarImport(DEFAULT_IMPORT_CLASSES);
            }
        }, Phases.CONVERSION);
    }

    private static class NullScript extends Script {
        @Override
        public Object run() {
            return null;
        }
    }
}
