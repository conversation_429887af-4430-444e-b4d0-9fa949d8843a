package com.facishare.paas.expression;

import com.facishare.paas.expression.exception.ExpressionDefineException;
import com.facishare.paas.expression.redis.CodeRedisCache;
import com.fxiaoke.common.StopWatch;
import com.google.common.collect.Maps;
import groovy.lang.GroovyClassLoader;
import groovy.lang.GroovyCodeSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.groovy.ast.ClassNode;
import org.codehaus.groovy.control.*;
import org.springframework.util.CollectionUtils;

import java.security.AccessController;
import java.security.PrivilegedAction;
import java.util.Map;
import java.util.Objects;

/**
 * Created by liwei on 2019/10/8
 */
@Slf4j
public class ExpressionClassLoader extends GroovyClassLoader {
    protected static String SCRIPT_NAME = "FXExpressionScript";
    private static Map<String, Map<byte[], byte[]>> codeMap = Maps.newConcurrentMap();
    private CodeRedisCache codeRedisCache;

    public ExpressionClassLoader(ClassLoader loader, CompilerConfiguration config, CodeRedisCache codeRedisCache) {
        super(loader, config);
        this.codeRedisCache = codeRedisCache;
    }

    @Override
    protected void setClassCacheEntry(Class cls) {

    }

    @Override
    public Class loadClass(final String name, boolean lookupScriptFiles, boolean preferClassOverScript, boolean resolve) throws ClassNotFoundException, CompilationFailedException {
        //自定义表达式只在当前loader查找
        if (StringUtils.contains(name, SCRIPT_NAME)) {
            return findLoadedClass(name);
        }
        return super.loadClass(name, lookupScriptFiles, preferClassOverScript, resolve);
    }

    @Override
    public Class parseClass(GroovyCodeSource codeSource, boolean shouldCacheSource) throws CompilationFailedException {
        StopWatch stopWatch = StopWatch.createStarted("ExpressionClassLoader.parseClass");
        String name = codeSource.getName().replace(".groovy", "");
        try {
            Class theClass = null;
            if (codeRedisCache == null) {
                theClass = super.parseClass(codeSource, shouldCacheSource);
                stopWatch.lap("super.parseClass");
                return theClass;
            }

            try {
                Map<byte[], byte[]> classCodeMap = codeRedisCache.getCode(name);
                stopWatch.lap("getCache");
                if (!CollectionUtils.isEmpty(classCodeMap)) {
                    InnerLoader loader = createInnerLoader();
                    Class answer = null;
                    for (Map.Entry<byte[], byte[]> entry : classCodeMap.entrySet()) {
                        String defineClassName = new String(entry.getKey());
                        Class defineClass = null;
                        try {
                            defineClass = loader.defineClass(defineClassName, entry.getValue());
                        } catch (LinkageError e) {
                            log.warn("defineClass error name:{} ", defineClassName, e);
                            try {
                                defineClass = Class.forName(defineClassName, true, loader);
                            } catch (ClassNotFoundException ex) {
                                log.error("findClass forName error className:{} ", defineClassName, ex);
                                throw new ExpressionDefineException("class define error");
                            }
                        }
                        //setClassCacheEntry(defineClass);
                        if (defineClassName.equals(name)) answer = defineClass;
                    }
                    stopWatch.lap("defineClass");
                    //没找到class的话继续执行，重新编译
                    if (Objects.nonNull(answer)) {
                        return answer;
                    }
                }
            } catch (Throwable e) {
                //从redis缓存获取字节码转class失败的话继续执行，重新编译
                log.warn("loadClass error name:{} ", name, e);
            }
            theClass = super.parseClass(codeSource, shouldCacheSource);
            stopWatch.lap("super.parseClass");
            codeRedisCache.setCode(name, codeMap.get(name));
            stopWatch.lap("setCache");

            return theClass;
        } finally {
            if (codeRedisCache != null) {
                codeMap.remove(name);
            }
            stopWatch.logSlow(30);
        }
    }

    private InnerLoader createInnerLoader() {
        return AccessController.doPrivileged((PrivilegedAction<InnerLoader>) () -> new InnerLoader(ExpressionClassLoader.this));
    }

    @Override
    protected ClassCollector createCollector(CompilationUnit unit, SourceUnit su) {
        if (codeRedisCache == null) {
            return super.createCollector(unit, su);
        }
        InnerLoader loader = createInnerLoader();
        return new ExpressionClassCollector(loader, unit, su);
    }

    public static class ExpressionClassCollector extends ClassCollector {
        private CompilationUnit unit;

        protected ExpressionClassCollector(InnerLoader cl, CompilationUnit unit, SourceUnit su) {
            super(cl, unit, su);
            this.unit = unit;
        }

        @Override
        protected Class createClass(byte[] code, ClassNode classNode) {
            Class theClass = super.createClass(code, classNode);
            if (classNode.getName().startsWith(SCRIPT_NAME)) {
                BytecodeProcessor bytecodePostprocessor = this.unit.getConfiguration().getBytecodePostprocessor();
                byte[] fcode = code;
                if (bytecodePostprocessor != null) {
                    fcode = bytecodePostprocessor.processBytecode(classNode.getName(), code);
                }

                String scriptName = classNode.getName().split("\\$")[0];
                codeMap.putIfAbsent(scriptName, Maps.newHashMap());
                codeMap.get(scriptName).put(classNode.getName().getBytes(), fcode);
            }
            return theClass;
        }
    }
}
