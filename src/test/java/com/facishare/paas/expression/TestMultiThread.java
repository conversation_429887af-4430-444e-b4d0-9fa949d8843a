package com.facishare.paas.expression;

import com.fxiaoke.api.IdGenerator;
import com.github.trace.TraceContext;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;

/**
 * Created by zhouwr on 2023/6/8.
 */
public class TestMultiThread {

    public static final int MAN_NUM = 10000;

    private static final ExpressionService expressionService = new ExpressionServiceImpl();

    public static void main(String[] args) {
        String expression = "a/(1+b)";
        Map data = ImmutableMap.of("a", new BigDecimal("309.60000000"), "b", new BigDecimal("13.00").divide(BigDecimal.valueOf(100)));
        Object result = expressionService.evaluate(expression, data);
        BigDecimal format = ((BigDecimal)result).setScale(8, RoundingMode.HALF_UP);
        System.out.println(result);
        System.out.println(format);

//        String expression1 = "STARTWITH(field,'a')";
//        String expression2 = "IF(false,1,2)\nIF(true,3,4)";
//        String expression3 = "CASE(field,\"A\",1,\"B\",2,3)";
//
//        List<String> expressionList = Lists.newArrayList(expression1, expression2, expression3);
//
//        List<Map<String, Object>> bindingsList1 = Lists.newArrayList();
//        bindingsList1.add(ImmutableMap.of("field", "abc"));
//        bindingsList1.add(Maps.newHashMap());
//        bindingsList1.add(ImmutableMap.of("field", "A"));
//        List<Object> expectedList1 = Lists.newArrayList(true, 3, BigDecimal.valueOf(1));
//
//        List<Map<String, Object>> bindingsList2 = Lists.newArrayList();
//        bindingsList2.add(ImmutableMap.of("field", "bac"));
//        bindingsList2.add(Maps.newHashMap());
//        bindingsList2.add(ImmutableMap.of("field", "B"));
//        List<Object> expectedList2 = Lists.newArrayList(false, 4, BigDecimal.valueOf(2));
//
//        List<Map<String, Object>> bindingsList3 = Lists.newArrayList();
//        bindingsList3.add(ImmutableMap.of("field", "cba"));
//        bindingsList3.add(Maps.newHashMap());
//        bindingsList3.add(ImmutableMap.of("field", "C"));
//        List<Object> expectedList3 = Lists.newArrayList(false, 4, BigDecimal.valueOf(3));
//
//        CountDownLatch countDownLatch = new CountDownLatch(6);
//        List<Thread> threads = Lists.newArrayList();
//        threads.add(newTestThread(expressionList, bindingsList1, expectedList1, countDownLatch));
//        threads.add(newTestThread(expressionList, bindingsList2, expectedList2, countDownLatch));
//        threads.add(newTestThread(expressionList, bindingsList3, expectedList3, countDownLatch));
//        threads.add(newTestThread(expressionList, bindingsList1, expectedList1, countDownLatch));
//        threads.add(newTestThread(expressionList, bindingsList2, expectedList2, countDownLatch));
//        threads.add(newTestThread(expressionList, bindingsList3, expectedList3, countDownLatch));
//
//        long start = System.currentTimeMillis();
//        threads.forEach(Thread::start);
//
//        try {
//            countDownLatch.await();
//        } catch (InterruptedException e) {
//            throw new RuntimeException(e);
//        }
//
//        String cost = BigDecimal.valueOf(System.currentTimeMillis() - start).divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP).toPlainString();
//        System.out.println("cost:" + cost + "s");
    }

    private static Thread newTestThread(List<String> expressionList, List<Map<String, Object>> bindingsList,
                                        List<Object> expectedList, CountDownLatch countDownLatch) {
        return new Thread(() -> {
//            String traceId = IdGenerator.get();
            for (int i = 0; i < MAN_NUM; i++) {
//                TraceContext.get().setTraceId(traceId + "_" + i);
                for (int j = 0; j < expressionList.size(); j++) {
                    evaluate(expressionList.get(j), bindingsList.get(j), expectedList.get(j));
                }
            }
            countDownLatch.countDown();
        });
    }

    private static void evaluate(String expression, Map<String, Object> bindings, Object expected) {
        Object result = expressionService.evaluate(expression, bindings, true);
        assert Objects.equals(result, expected);
    }
}
