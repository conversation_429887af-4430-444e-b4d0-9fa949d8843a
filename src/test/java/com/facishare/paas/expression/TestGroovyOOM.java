package com.facishare.paas.expression;

import com.facishare.paas.expression.type.PDateTime;
import com.google.common.collect.Maps;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

public class TestGroovyOOM {
    private static ExpressionService expressionService = new ExpressionServiceImpl();
    private static AtomicInteger count = new AtomicInteger();

    public static void evaluate() throws Throwable {
        String expression = "datetime0 - datetime01 + " + count.get();
        Map bindings = Maps.newHashMap();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date date = format.parse("2017-01-06");
        Date date1 = format.parse("2016-01-06");
        bindings.put("datetime0", PDateTime.of(date.getTime()));
        bindings.put("datetime01", PDateTime.of(date1.getTime()));


        Object ret = expressionService.evaluate(expression, bindings);

        System.out.println("evaluate " + count.incrementAndGet() + ", ret: " + ret);
    }

    public static void testOOM() throws Throwable {

        int n = 10;
        for (int i = 0; i < n; i++) {
            evaluate();
        }

    }

    public static void printClassLoaderLockInfo(ClassLoader classLoader) {
        try {
            Field field = ClassLoader.class.getDeclaredField("parallelLockMap");
            field.setAccessible(true);  //设置访问性

            ConcurrentHashMap lockMap = (ConcurrentHashMap) field.get(classLoader);
            System.out.println(classLoader.toString() + " parallelLockMap size: " + (lockMap == null ? null : lockMap.size()));
            lockMap.clear();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void printAllClassLoaderLockInfo(ClassLoader classLoader) {
        while (classLoader != null) {
            printClassLoaderLockInfo(classLoader);
            classLoader = classLoader.getParent();
        }
    }

    public static void main(String[] args) throws Throwable {
        testOOM();

        System.out.println("total count is " + count.get());

        printAllClassLoaderLockInfo(Thread.currentThread().getContextClassLoader());
        printAllClassLoaderLockInfo(TestGroovyOOM.class.getClassLoader());

        while (true) {
            Thread.sleep(1000L);
        }
    }
}
