package com.facishare.paas.expression


import spock.lang.Specification

/**
 *
 * Created by liyiguang on 2017/7/24.
 */
class ExpressionEngineTest extends Specification {
    def "test evaluate"() {
        given:
        ExpressionEngine engine = new ExpressionEngine()
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                | bindings                                                                         || expectedResult
        expression()                              | bindings()                                                                       || expectedResult()
        "EXCHANGERATE(fromCurrency, toCurrency)"  | [fromCurrency: "USD", toCurrency: "CNY", _exchange_rate_: ["USD-CNY": "6.59"]]   || new BigDecimal("6.59")
        "EXCHANGERATE(fromCurrency,toCurrency,1)" | [fromCurrency: "USD", toCurrency: "CNY", _exchange_rate_: ["USD-CNY": "6.59"]]   || new BigDecimal("6.59")
        "EXCHANGERATE(fromCurrency,toCurrency,1)" | [fromCurrency: "USD", toCurrency: "CNY", _exchange_rate_: ["USD-JPD": "600.59"]] || new BigDecimal("1")
        "EXCHANGERATE(fromCurrency,toCurrency,1)" | [fromCurrency: "USD", toCurrency: "CNY"]                                         || new BigDecimal("1")
        "EXCHANGERATE(\"USD\",\"CNY\",1)"         | [:]                                                                              || new BigDecimal("1")
        "EXCHANGERATE(\"USD\",\"CNY\",1)"         | null                                                                             || new BigDecimal("1")
    }

    def expectedResult() {
        2
    }

    def bindings() {
        def map = [name: "name", value: 100, success: true]
        map
    }

    def expression() {
        """
        IF(true,IF(true,IF(true,2,5/0),5/0),4/0)
        """
    }

    def "test compile"() {
        given:
        ExpressionEngine engine = new ExpressionEngine()
        when:
        engine.compile(expression)
        then:
        noExceptionThrown()
        where:
        expression                        | bindings
        expression2()                     | bindings()
        """IF(true,4,4)"""                | bindings()
        """NULLVALUE("test","default")""" | bindings()
        """NULLVALUE(null,"default")"""   | bindings()
    }

    def expression2() {
        """
        NOT(true)
        """
    }

}
