# fs-webpage-customer 项目

## 项目概述

fs-webpage-customer 是一个基于Maven的多模块项目，主要用于客户网页相关功能的开发和管理。该项目采用了标准的分层架构设计，将不同功能模块进行了清晰的划分。项目支持多语言、组件化设计和灵活的权限控制，是一个功能完备的企业级Web应用。

## 项目结构

该项目是一个多模块Maven项目，包含以下模块：

### 1. fs-webpage-customer-api
API接口模块，定义系统对外暴露的接口和数据模型。
- 定义服务对外暴露的接口
- 包含各种DTO、请求和响应对象
- 作为其他模块调用本服务的依赖包

### 2. fs-webpage-customer-core
核心业务逻辑模块，实现业务核心功能。
- 实现核心业务逻辑
- 定义服务内部使用的实体和工具类
- 依赖api模块，被provider模块依赖

### 3. fs-webpage-customer-provider
服务提供者模块，负责具体服务实现和集成。
- 提供实际的服务实现
- 集成各种外部依赖和中间件
- 处理Web请求和服务发布
- 依赖core模块，实现服务的具体功能

### 4. fs-webpage-customer-designer
设计器模块，提供网页设计相关功能。
- 提供网页设计功能相关的实现
- 依赖core模块
- 包含设计器相关的特定功能

## 系统架构

项目采用了经典的多层架构设计：

```
+--------------------------------------------+
|              Web/App 客户端                |
+--------------------------------------------+
                    |
+--------------------------------------------+
|      fs-webpage-customer-provider          |
|   +-------------------------------+        |
|   |        控制器层 (Controller)   |        |
|   +-------------------------------+        |
|                  |                         |
|   +-------------------------------+        |
|   | REST服务层 (REST Service)      |        |
|   +-------------------------------+        |
|                  |                         |
|   +-------------------------------+        |
|   |     业务服务层 (Service)       |        |
|   +-------------------------------+        |
|                  |                         |
|   +-------------------------------+        |
|   |     数据访问层 (DAO)           |        |
|   +-------------------------------+        |
+--------------------------------------------+
                    |
        +-----------------------+
        |                       |
+--------------------+  +--------------------+
| fs-webpage-        |  | 外部系统/服务      |
| customer-designer  |  | (PaaS, CRM等)     |
+--------------------+  +--------------------+
        |
+--------------------------------------------+
|        fs-webpage-customer-core            |
|   +-------------------------------+        |
|   |      核心业务逻辑             |        |
|   +-------------------------------+        |
|   |      内部服务实现             |        |
|   +-------------------------------+        |
|   |      工具类/通用组件          |        |
|   +-------------------------------+        |
+--------------------------------------------+
                    |
+--------------------------------------------+
|        fs-webpage-customer-api             |
|   +-------------------------------+        |
|   |       接口定义                |        |
|   +-------------------------------+        |
|   |       模型定义                |        |
|   +-------------------------------+        |
|   |       常量/异常定义           |        |
|   +-------------------------------+        |
+--------------------------------------------+
                    |
+--------------------------------------------+
|              数据存储层                    |
|      (MongoDB, Redis, MySQL等)             |
+--------------------------------------------+
```

### 模块间依赖关系图
```
fs-webpage-customer (parent)
├── fs-webpage-customer-api
├── fs-webpage-customer-core (依赖 api)
├── fs-webpage-customer-provider (依赖 core)
└── fs-webpage-customer-designer (依赖 core)
```

## 核心特性

1. **多模块设计**
   - 清晰划分职责边界
   - 松耦合的组件设计
   - 可独立演进的模块结构

2. **完善的分层架构**
   - 表现层：处理用户请求，返回响应
   - 业务逻辑层：实现核心业务规则
   - 数据访问层：封装数据存储细节

3. **国际化支持**
   - 完善的多语言支持机制
   - 基于键值对的国际化资源管理

4. **组件化设计**
   - 高度灵活的组件体系
   - 支持动态UI配置和渲染

5. **权限控制**
   - 基于角色的访问控制
   - 多级权限管理机制

## 技术栈

- Spring Framework (Context, MVC等)
- Dubbo (RPC框架)
- MongoDB (数据存储)
- Caffeine/Redis (缓存)
- Spock/JUnit (测试框架)
- Lombok (代码简化)
- Fastjson/Jackson (JSON处理)

## Maven依赖分析

### 父项目依赖
项目继承自 `com.fxiaoke.common:fxiaoke-parent-pom:1.0.0-SNAPSHOT`

### 核心依赖版本
```xml
<properties>
    <perf4j.version>0.9.16</perf4j.version>
    <fs-cep-spring-plugin.version>1.0.3-SNAPSHOT</fs-cep-spring-plugin.version>
    <fs-qixin-common-beans.version>1.0.2-SNAPSHOT</fs-qixin-common-beans.version>
    <fs-qixin-objgroup-manage-common.version>1.3.9-SNAPSHOT</fs-qixin-objgroup-manage-common.version>
    <fs-enterpriserelation-rest-api.version>2.1.1-SNAPSHOT</fs-enterpriserelation-rest-api.version>
    <powermock.version>2.0.9</powermock.version>
    <mockito.version>3.3.0</mockito.version>
</properties>
```

### 模块依赖关系

#### fs-webpage-customer-core 核心依赖
- lombok：用于简化Java代码
- fastjson：JSON处理
- guava：Google工具类库
- Spring相关：
  - spring-context
  - spring-test
- 配置相关：
  - config-core
  - spring-support

#### fs-webpage-customer-provider 核心依赖
- Spring框架相关：
  - spring-webmvc
  - spring-beans
  - spring-context
  - spring-aop
  - spring-jdbc
- 数据库相关：
  - mongo-spring-support
- Dubbo相关：
  - dubbo
  - dubbo-restful-plugin
- 日志相关：
  - slf4j-api
  - logback-classic
- 缓存相关：
  - caffeine
  - jedis-spring-support
- 测试相关：
  - junit
  - spock-core
  - spock-spring
  - mockito-core
  - powermock

### 依赖问题修复建议

当前项目存在以下依赖问题需要解决：

1. Spring相关依赖缺失
```xml
<!-- 在fs-webpage-customer-core/pom.xml中添加 -->
<dependency>
    <groupId>org.springframework</groupId>
    <artifactId>spring-context</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework</groupId>
    <artifactId>spring-beans</artifactId>
</dependency>
```

2. Lombok依赖问题
```xml
<!-- 在fs-webpage-customer-core/pom.xml中确保有此依赖 -->
<dependency>
    <groupId>org.projectlombok</groupId>
    <artifactId>lombok</artifactId>
</dependency>
```

### 最佳实践建议

1. 版本管理
   - 建议在父pom中统一管理依赖版本
   - 使用dependencyManagement来控制版本
   - 避免在子模块中直接指定版本号

2. 依赖隔离
   - core模块应该只包含必要的核心依赖
   - provider模块负责集成更多的外部依赖
   - 避免循环依赖

3. 测试依赖
   - 测试相关依赖应该使用test scope
   - 建议统一使用spock进行测试

4. 版本冲突解决
   - 使用`mvn dependency:tree`检查依赖树
   - 必要时使用exclusions排除冲突依赖
   - 关注传递依赖带来的版本冲突

## 开发指南

### 环境准备
1. JDK 1.8+
2. Maven 3.6+
3. MongoDB
4. Redis

### 构建项目
```bash
# 编译整个项目
mvn clean install

# 跳过测试编译
mvn clean install -DskipTests
```

### 开发建议
1. 遵循模块职责划分，避免跨模块直接调用
2. 编写单元测试覆盖主要业务逻辑
3. 遵循代码规范和命名约定
4. 使用统一的异常处理机制
5. 关注性能和安全性 

## 站点CMS技术方案

站点CMS是一个用于管理站点页面、主题、菜单和文件资源的系统。本技术方案提供了站点与工作区资源之间的引用关系管理能力。

### 核心实体

#### 1. 工作区（WorkSpace）

工作区是资源的集合，支持以下功能：
- 创建、查询、修改和删除工作区
- 查询关联工作区
- 获取工作区可选列表

#### 2. 文件（File）

文件是工作区中的资源，支持以下功能：
- 文件上传与管理
- 文件分类存储
- 文件访问控制

#### 3. 引用关系（Reference）

引用关系用于管理站点页面对文件资源的引用，支持以下功能：
- 创建和维护资源引用关系
- 查询资源的引用情况
- 引用关系差异比较与处理

### 核心接口

#### 1. 工作区管理接口

- `findByApiName`：根据apiName查询工作区
- `findWorkSpaceList`：查询工作区列表
- `saveWorkSpace`：保存工作区
- `findRelatedWorkSpace`：查询关联工作区
- `optionalItemList`：获取工作区可选列表

#### 2. 文件管理接口

- `findByApiName`：根据apiName查询文件
- `findByWorkSpace`：查询工作区下的文件列表
- `saveFile`：保存文件
- `moveFile`：移动文件

#### 3. 站点集成接口

- `updateSitePages`：更新站点页面内容，并维护文件引用关系
- `findSiteByApiNameForManager`：查询站点信息，包含引用的文件资源信息

### 技术实现

#### MongoDB存储架构

使用MongoDB存储三种核心实体：
- `WorkSpaceEntity`：工作区实体类，存储工作区基本信息
- `FileEntity`：文件实体类，存储文件元数据和引用信息
- `ReferenceEntity`：引用关系实体类，存储站点页面与文件资源之间的引用关系

#### 引用关系管理

引用关系管理是本方案的核心，主要包括：
1. 引用关系的建立：站点页面保存时，创建对文件资源的引用关系
2. 引用关系的差异比较与处理：站点页面更新时，计算新旧引用关系的差异，添加新的引用关系并删除不再需要的引用关系
3. 引用关系的查询：查询站点页面引用的文件资源，以及查询文件资源被哪些站点页面引用

#### 文件路径处理

支持TNPath与NPath的转换，确保文件可以正确访问：
1. TNPath：临时文件路径，通常以"/tn/"开头
2. NPath：正式文件路径，通常以"/n/"开头
3. 访问URL生成：根据文件路径生成可访问的URL 