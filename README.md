## 工程目的
提供统一的面向PaaS的应用服务框架

## 工程结构
Maven工程结构

```bash
fs-paas-appframework
  └─fs-paas-app-api
  └─fs-paas-app-core
  └─fs-paas-app-license
  └─fs-paas-app-log
  └─fs-paas-app-metadata
  └─fs-paas-app-privilege
  └─fs-paas-app-flow
  └─fs-paas-app-web
  └─fs-paas-app-runtime
  └─fs-paas-app-fcp
  └─fs-paas-app-udobj-rest
```
模块说明
- fs-paas-app-api 核心模块API
- fs-paas-app-core 核心模块领域模型实现
- fs-paas-app-license 对PaaS license服务的封装模块
- fs-paas-app-log 对PaaS审计日志服务封装模块
- fs-paas-app-metadata 对PaaS元数据服务的封装模块
- fs-paas-app-privilege 对PaaS权限服务的封装模块
- fs-paas-app-flow 对PaaS流程相关服务的封装模块（审批，工作流，BPM）
- fs-paas-app-web 应用框架REST服务扩展模块
- fs-paas-app-runtime 应用框架二次开发运行时模块
- fa-paas-app-fcp 应用框架fcp协议接入点 (主要是为了兼容老接口)
- s-paas-app-udobj-rest 兼容老的udobj项目中的REST服务






