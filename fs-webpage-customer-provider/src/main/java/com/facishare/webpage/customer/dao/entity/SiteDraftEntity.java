package com.facishare.webpage.customer.dao.entity;

import com.facishare.webpage.customer.api.constant.ClientType;
import lombok.Data;
import org.mongodb.morphia.annotations.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/11/27.
 */
@Data
@Entity(value = "SiteDraftEntity", noClassnameStored = true)
@Indexes({
        @Index(fields = {@Field("tenantId"), @Field("siteApiName"), @Field("clientType")}, options = @IndexOptions(background = true, unique = true)),
})
public class SiteDraftEntity {
    @Property("siteApiName")
    private String siteApiName;
    @Property("draftData")
    private String draftData;
    @Property("description")
    private String description;
    @Property("appId")
    private String appId;
    @Property("tenantId")
    private int tenantId;
    @Id
    private String id;
    @Property("creatorId")
    private Integer creatorId;
    @Property("createTime")
    private Long createTime;
    @Property("updaterId")
    private Integer updaterId;
    @Property("updateTime")
    private Long updateTime;
    @Property("deleteId")
    private String deleteId;
    @Property("version")
    private Long version;
    @Property("clientType")
    private String clientType = ClientType.web.getValue();
}
