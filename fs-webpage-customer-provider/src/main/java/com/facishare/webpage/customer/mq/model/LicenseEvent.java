package com.facishare.webpage.customer.mq.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * Created by zhangyu on 2019/11/13
 */
@Data
public class LicenseEvent implements Serializable {

    private int tenantId;
    private String licenseVersion;
    private Set<String> moduleCodes;
    private List<MqModulePara> mqModuleParas;
    private String orderNumber;
    private long createTime;
    private long startTime;
    private long expiredTime;

    @Data
    public class MqModulePara implements Serializable {
        private String moduleCode; // 对应的moduleCode
        private String paraKey;
        private String paraValue;
    }

}
