package com.facishare.webpage.customer.mq;

import com.facishare.enterprise.event.EnterpriseAddEvent;
import com.facishare.qixin.common.utils.ProtoBufSerial;
import com.facishare.uc.api.model.enterprise.EnterpriseWebRegisterDto;
import com.facishare.webpage.customer.common.WebPageCommonService;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/6 4:32 下午
 */
@Component("enterpriseAddConsumer")
public class EnterpriseAddConsumer {

    private static final Logger logger = LoggerFactory.getLogger(EnterpriseAddConsumer.class);

    public static final List<Integer> blackEnterpriseList;
    //原配置文件 fs-webpage-enterpriseAdd-mq-consumer
    private String configName = "fs-ui-paas-mq-config";
    private String sectionName = "EnterpriseAddConsumer";

    static {
        blackEnterpriseList = Lists.newArrayList();
        blackEnterpriseList.add(EnterpriseWebRegisterDto.EnterpriseRegistrationSource.Yunzhijia.getValue());
        blackEnterpriseList.add(EnterpriseWebRegisterDto.EnterpriseRegistrationSource.KINGDEE_KIS.getValue());
        blackEnterpriseList.add(EnterpriseWebRegisterDto.EnterpriseRegistrationSource.KINGDEE_K3.getValue());
        /** 开放钉钉企业微信注册
         blackEnterpriseList.add(EnterpriseWebRegisterDto.EnterpriseRegistrationSource.WECHAT_WORK.getValue());
         blackEnterpriseList.add(EnterpriseWebRegisterDto.EnterpriseRegistrationSource.DingTalk.getValue());
         */
    }

    private AutoConfMQPushConsumer consumer;
    @Autowired
    private WebPageCommonService webPageCommonService;

    @PostConstruct
    public void init() {
        MessageListenerConcurrently listener = (messages, context) -> {
            messages.forEach(this::processMessage);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        String relSectionName = sectionName;
        String env = System.getProperty("env");
        if (StringUtils.isNotBlank(env)) {
            relSectionName = sectionName + "_" + env;
        }

        consumer = new AutoConfMQPushConsumer(configName, relSectionName, listener);
        consumer.start();
        logger.info("EnterpriseAddConsumer configName {}, sectionName {} init over", configName, relSectionName);
    }

    public void processMessage(Message message) {
        try {
            MessageHelper.fillContextFromMessage(TraceContext.get(), message);
            switch (message.getFlag()) {
                case 1:
                    dealEnterpriseAddEvent(message);
                    break;
                default:
                    return;
            }

        } finally {
            TraceContext.remove();
        }
    }

    private void dealEnterpriseAddEvent(Message message) {
        EnterpriseAddEvent enterpriseAddEvent = ProtoBufSerial.fromProto(message.getBody(), EnterpriseAddEvent.class);
        logger.info("enterpriseAddEvent:{}", enterpriseAddEvent);
        int enterpriseId = enterpriseAddEvent.getEnterpriseId();
        //黑名单来源的企业使用旧版CRM
        if (blackEnterpriseList.contains(enterpriseAddEvent.getSource())) {
            webPageCommonService.goOldCRM(enterpriseId);
            return;
        }
        //新注册的企业使用新版CRM
        webPageCommonService.enterpriseAdd(enterpriseId);
    }

}
