package com.facishare.webpage.customer.mq.model;

import com.google.common.collect.Sets;
import lombok.Data;

import java.io.Serializable;
import java.util.Set;

/**
 * Created by zhangyu on 2020/1/13
 */
@Data
public class RoleUserModifyEvent implements Serializable {

    private String tenantId;
    private String appId;
    private Set<String> roles;
    private Set<String> users = Sets.newHashSet();
    private Integer type;


    private String outerTenantId;
    //1.角色下删除用户 2.角色下添加用户 3.用户角色更新 4.删除角色 5.企业清空数据
    /**
     * 删除角色时，该角色下存在的人员；
     * 删除角色后角色下相应人员也会被删除，数据权限需要在删除角色前提供该角色下的人员信息
     */
    private Set<String> orgUsers = Sets.newHashSet();

    /**
     * 精准的人员角色删除和新增关系
     */
    private Set<UserRoleLink> delLink;
    private Set<UserRoleLink> insLink;

    @Data
    public static class UserRoleLink implements Serializable{
        private String userId;
        private String roleId;
    }

}