package com.facishare.webpage.customer.mq.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangyu on 2021/1/25
 */
@Data
public class AppViewChangeEvent implements Serializable {

    public static final String TOPIC = "AppViewChange";

    private String fsEa;
    private String appId;
    private String componentId;
    /**
     * 添加全公司可见
     */
    private Boolean addEaAuth;
    /**
     * 删除全公司可见
     */
    private Boolean delEaAuth;
    /**
     * 添加可见部门
     */
    private List<Integer> insertDepartmentList;
    /**
     * 删除可见部门
     */
    private List<Integer> deleteDepartmentList;
    /**
     * 添加可见人员
     */
    private List<Integer> insertUserList;
    /**
     * 删除可见人员
     */
    private List<Integer> deleteUserList;
    /**
     * 变更类型
     * 1：组件新增
     * 2：组件删除
     * 3：组件可见范围变更
     * 4：组件信息变更（名称、描述、logo、url等）
     * 5：自建应用启用/停用
     * 6: 删除全公司缓存
     */
    private Integer changeType;

}
