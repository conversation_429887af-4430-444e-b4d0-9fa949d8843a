package com.facishare.webpage.customer.mq.model;

import com.google.common.collect.Sets;
import lombok.Data;

import java.io.Serializable;
import java.util.Set;

/**
 * Created by zhangyu on 2020/1/14
 */
@Data
public class GroupModifyEvent implements Serializable {

    private String tenantId;

    private String appId;

    private int type;   //1、用户组 2、更新用户用户组

    private Set<String> relationIds;

    private int status; //1、启用  2、停用    3、删除

    private Set<String> newMembers = Sets.newHashSet();     //组成员 变化后 的组成员

    private Set<String> oldMembers = Sets.newHashSet();     //组成员 变化前 的组成员


}
