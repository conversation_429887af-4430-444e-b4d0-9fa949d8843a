package com.facishare.webpage.customer.mq;

import com.facishare.sandbox.listener.SandboxEvent;
import com.facishare.sandbox.module.CreateEvent;
import com.facishare.sandbox.module.DestroyEvent;
import com.facishare.sandbox.module.Module;
import com.facishare.webpage.customer.service.SandBoxCopyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
@Component("webPageSettingSandBox")
public class WebPageSettingSandBox extends SandboxEvent {

    private static final Logger LOGGER = LoggerFactory.getLogger(WebPageSettingSandBox.class);

    @Resource
    private SandBoxCopyService sandBoxCopyService;

    public WebPageSettingSandBox() {
        super("webpage_SANDBOX_EVENT_setting", Module.WebPageSetting);
    }

    @Override
    protected boolean onSandboxCreated(String module, CreateEvent createEvent) {
        LOGGER.info("WebPageSettingSandBox onSandboxCreated module : {}, createEvent : {}", module, createEvent);
        try {
            String type = createEvent.getType();
            if ("global".equals(type)) {
                sandBoxCopyService.copyGlobalSetting(createEvent.getFrom().getEnterpriseId(), createEvent.getTo().getEnterpriseId());
                LOGGER.info("WebPageSettingSandBox onSandboxCreated success module : {}, createEvent : {}", module, createEvent);
            }
            return true;
        } catch (Exception e) {
            LOGGER.error("WebPageSettingSandBox error by createEvent : {}", createEvent, e);
        }
        return false;
    }

    @Override
    protected boolean onSandboxDestroy(String module, DestroyEvent destroyEvent) {
        try {
            sandBoxCopyService.deleteGlobalSetting(destroyEvent.getEnterpriseId());
        } catch (Exception e) {
            LOGGER.error("WebPageSettingSandBox error by destroyEvent : {}", destroyEvent, e);
        }
        return false;
    }
}
