package com.facishare.webpage.customer.mq;

import com.facishare.converter.EIEAConverter;
import com.facishare.qixin.enterpriserelation.model.CreateDownstreamOuterUserLinkAppRoleEvent;
import com.facishare.qixin.enterpriserelation.model.DeleteDownstreamOuterUserRoleEvent;
import com.facishare.qixin.enterpriserelation.model.DownstreamOuterUserData;
import com.facishare.webpage.customer.constant.WebPageConstants;
import com.facishare.webpage.customer.remote.CrossAppCacheService;
import com.fxiaoke.enterpriserelation2.arg.BatchGetFsAccountByOuterUidsArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.BatchGetFsAccountByOuterUidsResult;
import com.fxiaoke.enterpriserelation2.service.FxiaokeAccountService;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2021/1/22
 */
@Component
public class CrossAppMqConsumer {

    private static final Logger logger = LoggerFactory.getLogger(CrossAppMqConsumer.class);

    private AutoConfMQPushConsumer consumer;

    //原配置文件 fs-webpage-cross-app-change-mq
    //专属云topic fs-linkapp-notify
    //灰度环境 topic
    private String configName = "fs-ui-paas-mq-config";

    private String sectionName = "CrossAppMqConsumer";

    @Resource
    private FxiaokeAccountService fxiaokeAccountService;
    @Resource
    private CrossAppCacheService crossAppCacheService;
    @Resource
    private EIEAConverter eieaConverter;

    @PostConstruct
    public void init() {
        MessageListenerConcurrently listener = (messages, context) -> {
            messages.forEach(this::processMessage);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        String relSectionName = sectionName;
        String env = System.getProperty("env");
        if (StringUtils.isNotBlank(env)) {
            relSectionName = sectionName + "_" + env;
        }

        consumer = new AutoConfMQPushConsumer(configName, relSectionName, listener);
        consumer.start();
        logger.info("OuterUserConsumer configName {}, sectionName {} init over", configName, relSectionName);
    }

    public void processMessage(Message message) {
        try {
            MessageHelper.fillContextFromMessage(TraceContext.get(), message);
            String tags = message.getTags();
            if (DeleteDownstreamOuterUserRoleEvent.TAG.equals(tags)) {
                DeleteDownstreamOuterUserRoleEvent deleteEvent = new DeleteDownstreamOuterUserRoleEvent();
                deleteEvent.fromProto(message.getBody());
                deleteCrossAppCache(deleteEvent);
            }
            if (CreateDownstreamOuterUserLinkAppRoleEvent.TAG.equals(tags)){
                CreateDownstreamOuterUserLinkAppRoleEvent createEvent = new CreateDownstreamOuterUserLinkAppRoleEvent();
                createEvent.fromProto(message.getBody());

                DeleteDownstreamOuterUserRoleEvent deleteEvent = new DeleteDownstreamOuterUserRoleEvent();
                deleteEvent.setLinkAppId(createEvent.getLinkAppId());
                deleteEvent.setUpstreamEa(createEvent.getUpstreamEa());
                deleteEvent.setDeleteDownstreamOuterUserDatas(createEvent.getDownstreamOuterUserDatas());
                deleteCrossAppCache(deleteEvent);
            }
        } catch (Exception e) {
            logger.error("CrossAppMqConsumer error", e);
        } finally {
            TraceContext.remove();
        }
    }

    private void deleteCrossAppCache(DeleteDownstreamOuterUserRoleEvent deleteEvent) {
        logger.info("deleteCrossAppCache deleteEvent : {}", deleteEvent);
        List<DownstreamOuterUserData> deleteDownstreamOuterUserDatas = deleteEvent.getDeleteDownstreamOuterUserDatas();
        if (CollectionUtils.isEmpty(deleteDownstreamOuterUserDatas)) {
            return;
        }
        List<Long> outerUIds = deleteDownstreamOuterUserDatas.stream().map(x -> x.getDownstreamOuterUid()).collect(Collectors.toList());
        List<BatchGetFsAccountByOuterUidsResult.FsAccount> fsAccounts = getFsAccounts(outerUIds);
        if (CollectionUtils.isEmpty(fsAccounts)) {
            return;
        }
        fsAccounts.stream().forEach(x -> {
            int enterpriseId = eieaConverter.enterpriseAccountToId(x.getEa());
            crossAppCacheService.removeCacheByEmployee(enterpriseId, x.getEmployeeId());
        });

    }

    private List<BatchGetFsAccountByOuterUidsResult.FsAccount> getFsAccounts(List<Long> outerUIds) {
        try {
            HeaderObj headerObj = HeaderObj.newInstance(0);
            headerObj.setAppId(WebPageConstants.CROSS_APPID);
            BatchGetFsAccountByOuterUidsArg arg = new BatchGetFsAccountByOuterUidsArg();
            arg.setOuterUids(outerUIds);
            RestResult<BatchGetFsAccountByOuterUidsResult> result = fxiaokeAccountService.batchGetFsAccountByOuterUids(headerObj, arg);
            return result.getData().getOuterUidFsAccountMap().values().stream().collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("batchGetFsAccountByOuterUids error by outerUids : {}", outerUIds, e);
        }
        return Lists.newArrayList();
    }

    @PreDestroy
    public void shutDown() {
        consumer.close();
    }

}
