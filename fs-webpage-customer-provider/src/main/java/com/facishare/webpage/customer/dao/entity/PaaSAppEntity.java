package com.facishare.webpage.customer.dao.entity;

import com.facishare.webpage.customer.api.constant.SourceType;
import com.facishare.webpage.customer.api.model.ScopeForCross;
import com.google.common.collect.Lists;
import lombok.Data;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.*;

import java.util.List;

/**
 * Created by zhangyu on 2020/11/12
 */
@Data
@Entity(value = "PaaSAppEntity", noClassnameStored = true)
@Indexes({
        @Index(fields = {@Field("tenantId"), @Field("appId")}
                , options = @IndexOptions(unique = true, dropDups = true, background = true)),
        @Index(fields = {@Field("tenantId"), @Field("status")},
                options = @IndexOptions(dropDups = true, background = true)),
        @Index(fields = {@Field("appId")}
                , options = @IndexOptions(dropDups = true, background = true))
})
public class PaaSAppEntity implements com.facishare.qixin.sysdb.model.Data{

    @Override
    public String getDataId() {
        return appId;
    }

    @Override
    public void setDataId(String dataId) {
        this.appId = dataId;
    }

    @Override
    public void setSystem(boolean systemFlag) {
        this.sourceType = systemFlag ? SourceType.SYSTEM : SourceType.CUSTOMER;
    }

    @Id
    private ObjectId id;
    @Property("tenantId")
    private int tenantId;
    @Property("appId")
    private String appId;
    @Property("scopeList")
    private List<String> scopeList = Lists.newArrayList();

    @Embedded("scopeListForCross")
    private List<ScopeForCross> scopeListForCross = Lists.newArrayList();
    @Property("name")
    private String name;
    @Property("description")
    private String description;
    @Property("iconIndex")
    private String iconIndex;
    @Property("icon")
    private String icon;
    // com.facishare.webpage.customer.api.constant.PaaSAppStatus
    @Property("status")
    private int status;
    @Property("sourceType")
    private String sourceType;
    @Property("creatorId")
    private int creatorId;
    @Property("createTime")
    private long createTime;
    @Property("updaterId")
    private int updaterId;
    @Property("updateTime")
    private long updateTime;
    /**
     * 是否允许该应用 使用个人级应用视图
     */
    @Property("useUserPageTempleFlag")
    private boolean useUserPageTempleFlag = false;
    /**
     * 上传图片类型
     */
    @Property("uploadType")
    private String uploadImgType;

    /**
     * 应用类型
     */
    @Property("appType")
    private int appType;
    /**
     * 适用终端
     */
    @Property("accessType")
    private String accessType;

    /**
     * 应用web端跳转地址
     */
    @Property("weburl")
    private String weburl;

    /**
     * 应用web端跳转地址
     */
    @Property("weburl")
    private String appurl;
    /**
     * 父应用ID
     */
    @Property("parentAppId")
    private String parentAppId;
    /**
     * 自定义插件
     */
    @Property("plugins")
    private String plugins;

    @Property("customIcon")
    private Boolean customIcon; // 预置互联应用基础信息支持修改功能, 点击修改保存后该值即为true

}
