package com.facishare.webpage.customer.mq;

import com.alibaba.fastjson.JSON;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.api.constant.Constant;
import com.facishare.webpage.customer.api.constant.SourceType;
import com.facishare.webpage.customer.api.model.HomePageLayoutTO;
import com.facishare.webpage.customer.api.model.LayoutType;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.config.HomePageTemplateConfig;
import com.facishare.webpage.customer.config.model.WebPageTemplate;
import com.facishare.webpage.customer.constant.WebPageConstants;
import com.facishare.webpage.customer.dao.HomePageLayoutDao;
import com.facishare.webpage.customer.dao.TenantMenuDao;
import com.facishare.webpage.customer.dao.entity.HomePageLayoutEntity;
import com.facishare.webpage.customer.dao.entity.TenantMenuEntity;
import com.facishare.webpage.customer.mq.model.LicenseEvent;
import com.facishare.webpage.customer.remote.ObjectService;
import com.facishare.webpage.customer.core.util.ScopesUtil;
import com.facishare.webpage.customer.util.TempleIdUtil;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.List;

/**
 * Created by zhangyu on 2019/11/13
 */
@Component("licenseMqConsumer")
public class LicenseMqConsumer {

    private static final Logger logger = LoggerFactory.getLogger(LicenseMqConsumer.class);

    private static final int systemEmployeeId = 0;

    private static String defaultVersion = "default";

    private AutoConfMQPushConsumer consumer;
    //原配置文件：fs-webpage-license-mq-consumer
    //原topic：PaasLicense2
    private String configName = "fs-ui-paas-mq-config";
    private String sectionName = "LicenseMqConsumer";

    @Autowired
    private HomePageLayoutDao homePageLayoutDao;
    @Autowired
    private HomePageTemplateConfig homePageTemplateConfig;
    @Autowired
    private TenantMenuDao tenantMenuDao;
    @Autowired
    private ObjectService objectService;

    @PostConstruct
    public void init() {
        MessageListenerConcurrently listener = (messages, context) -> {
            messages.forEach(this::processMessage);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        String relSectionName = sectionName;
        String env = System.getProperty("env");
        if (StringUtils.isNotBlank(env)) {
            relSectionName = sectionName + "_" + env;
        }

        consumer = new AutoConfMQPushConsumer(configName, relSectionName, listener);
        consumer.start();
        logger.info("LicenseMqConsumer configName {}, sectionName {} init over", configName, relSectionName);
    }

    public void processMessage(Message message) {
        try {
            MessageHelper.fillContextFromMessage(TraceContext.get(), message);
            LicenseEvent licenseEvent = JSON.parseObject(message.getBody(), LicenseEvent.class);
            //清除缓存
            objectService.cleanCache(licenseEvent.getTenantId());
            logger.info("LicenseMqConsumer licenseEvent:{}", licenseEvent);
            WebPageTemplate webPageTemplate = getWebPageTemplate(licenseEvent.getLicenseVersion());
            initHomePageTemple(licenseEvent.getTenantId(), webPageTemplate.getHomePageIdList());
            initMenuTemple(licenseEvent.getTenantId(), webPageTemplate.getMenuIdList(), webPageTemplate.getPriorityLevel());
        } finally {
            TraceContext.remove();
        }
    }

    private WebPageTemplate getWebPageTemplate(String licenseVersion) {
        WebPageTemplate webPageTemplate = homePageTemplateConfig.getTemplateIds(licenseVersion);
        if (webPageTemplate != null) {
            return webPageTemplate;
        }
        return homePageTemplateConfig.getTemplateIds(defaultVersion);
    }

    private void initMenuTemple(int tenantId, List<String> menuIds, int priorityLevel) {
        logger.info("initMenuTemple begin tenantId {},menuIds {}", tenantId, menuIds);
        if (CollectionUtils.isEmpty(menuIds)) {
            logger.warn("initMenuTemple failed case by menuIds is empty!");
            return;
        }
        menuIds.stream().forEach(x -> {
            TenantMenuEntity tenantMenuById = tenantMenuDao.findTenantMenuById(x);
            if (tenantMenuById == null) {
                return;
            }
            TenantMenuEntity tenantMenuEntity = tenantMenuDao.findTenantMenuBySourceId(tenantId, x);
            if (tenantMenuEntity != null) {
                return;
            }
            tenantMenuEntity = new TenantMenuEntity();
            tenantMenuEntity.setSourceId(x);
            tenantMenuEntity.setTenantId(tenantId);
            tenantMenuEntity.setId(TempleIdUtil.buildId(tenantId));
            tenantMenuEntity.setPriorityLevel(priorityLevel);
            tenantMenuEntity.setAppType(BizType.CRM.getType());
            tenantMenuEntity.setSourceType(SourceType.SYSTEM);
            tenantMenuEntity.setAppId(WebPageConstants.APP_CRM);
            tenantMenuEntity.setStatus(1);
            tenantMenuEntity.setCreateTime(System.currentTimeMillis());
            tenantMenuEntity.setUpdateTime(System.currentTimeMillis());
            tenantMenuEntity.setChange(false);
            tenantMenuEntity.setCreatorId(systemEmployeeId);
            tenantMenuEntity.setUpdaterId(systemEmployeeId);
            tenantMenuDao.save(tenantMenuEntity);
            logger.debug("initMenuTemple end success tenantMenuEntity {}", tenantMenuEntity);
        });
    }

    private void initHomePageTemple(int tenantId, List<String> layoutIds) {
        logger.info("LicenseMqConsumer all layoutIds {}", layoutIds);
        layoutIds.stream().forEach(layoutId -> {
            logger.debug("LicenseMqConsumer one layoutId {}", layoutId);
            HomePageLayoutEntity homePageLayoutBySourceId = homePageLayoutDao.getHomePageLayoutBySourceId(tenantId, layoutId, null);
            if (homePageLayoutBySourceId == null) {
                HomePageLayoutEntity homePageLayoutById = homePageLayoutDao.getHomePageLayoutById(layoutId, null);
                if (homePageLayoutById == null) {
                    return;
                }
                HomePageLayoutEntity systemHomePageLayoutEntity = homePageLayoutDao.getSystemHomePageLayoutEntity(tenantId, Constant.APP_CRM, homePageLayoutById.getScopes());
                if (systemHomePageLayoutEntity != null) {
                    return;
                }
                HomePageLayoutEntity homePageLayoutByName = homePageLayoutDao.getHomePageLayoutByName(tenantId, 0, homePageLayoutById.getAppId(), homePageLayoutById.getName(), LayoutType.SYSTEM);
                if (homePageLayoutByName == null) {
                    HomePageLayoutTO homePageLayoutTO = new HomePageLayoutTO();
                    homePageLayoutTO.setLayoutId(TempleIdUtil.buildId(tenantId));
                    homePageLayoutTO.setLayoutType(LayoutType.SYSTEM);
                    homePageLayoutTO.setSystem(true);
                    homePageLayoutTO.setPriorityLevel(homePageLayoutById.getPriorityLevel());
                    homePageLayoutTO.setPageLayoutType(homePageLayoutById.getPageLayoutType());
                    homePageLayoutTO.setDataVersion(homePageLayoutById.getDataVersion());
                    List<Scope> scopes = ScopesUtil.resoleStringScope(homePageLayoutById.getScopes());
                    homePageLayoutTO.setScopes(scopes);
                    HomePageLayoutEntity homePageLayoutEntity = homePageLayoutDao.insertHomePageLayout(
                            Constant.APP_CRM,
                            BizType.CRM.getType(),
                            tenantId,
                            systemEmployeeId,
                            homePageLayoutTO,
                            layoutId,
                            false);
                    logger.info("HomePageTemplate one homePageLayoutEntity {}", homePageLayoutEntity);
                }
            }
        });
    }


    @PreDestroy
    public void shutDown() {
        consumer.shutdown();
    }

}
