package com.facishare.webpage.customer.mq;

import com.facishare.sandbox.listener.SandboxEvent;
import com.facishare.sandbox.module.CreateEvent;
import com.facishare.sandbox.module.DestroyEvent;
import com.facishare.sandbox.module.Module;
import com.facishare.webpage.customer.service.SandBoxCopyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Created by zhangyu on 2021/1/26
 */
@Component("webMainChannelSandBox")
public class WebMainChannelSandBox extends SandboxEvent {

    private static final Logger logger = LoggerFactory.getLogger(WebMainChannelSandBox.class);

    @Resource
    private SandBoxCopyService sandBoxCopyService;

    public WebMainChannelSandBox() {
        super("webpage_SANDBOX_EVENT_webChannel", Module.WebMainChannel);
    }

    @Override
    protected boolean onSandboxCreated(String module, CreateEvent createEvent) {
        logger.info("WebMainChannelSandBox onSandboxCreated module : {}, createEvent : {}", module, createEvent);
        try {
            sandBoxCopyService.copyWebMainChannel(createEvent.getFrom().getEnterpriseId(), createEvent.getTo().getEnterpriseId());
            return true;
        } catch (Exception e) {
            logger.error("copyWebMainChannel error by createEvent : {}", createEvent, e);
        }
        return false;
    }

    @Override
    protected boolean onSandboxDestroy(String module, DestroyEvent destroyEvent) {
        logger.info("WebMainChannelSandBox onSandboxCreated module : {}, destroyEvent : {}", module, destroyEvent);
        try {
            sandBoxCopyService.deleteMainChannel(destroyEvent.getEnterpriseId());
            return true;
        } catch (Exception e) {
            logger.error("deleteMainChannel error by destroyEvent : {}", destroyEvent, e);
        }
        return false;
    }
}
