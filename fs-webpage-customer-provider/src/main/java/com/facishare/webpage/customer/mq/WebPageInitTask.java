package com.facishare.webpage.customer.mq;

import com.facishare.sandbox.listener.SandboxEvent;
import com.facishare.sandbox.module.CreateEvent;
import com.facishare.sandbox.module.DestroyEvent;
import com.facishare.sandbox.module.Module;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.common.CheckService;
import com.facishare.webpage.customer.common.WebPageCommonService;
import com.facishare.webpage.customer.service.HomePageBaseService;
import com.facishare.webpage.customer.service.SandBoxCopyService;
import com.facishare.webpage.customer.service.TenantPageTempleBaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Created by zhangyu on 2020/7/3
 */
@Component("webPageInitTask")
public class WebPageInitTask extends SandboxEvent {
    @Resource
    private HomePageBaseService homePageBaseService;
    @Resource
    private TenantPageTempleBaseService tenantPageTempleBaseService;
    @Resource
    private SandBoxCopyService sandBoxCopyService;
    @Resource
    private WebPageCommonService webPageCommonService;
    @Resource
    private CheckService checkService;

    private static final Logger logger = LoggerFactory.getLogger(WebPageInitTask.class);

    public WebPageInitTask() {
        super("cusPage_SANDBOX_EVENT_Page", Module.Page);
    }

    @Override
    protected boolean onSandboxCreated(String module, CreateEvent event) {
        logger.info("onSandboxCreated module {},event {}", module, event);

        int fromTenantId = event.getFrom().getEnterpriseId();
        int toTenantId = event.getTo().getEnterpriseId();
        String type = event.getType();
        try {
            boolean goNewCRM = checkService.checkGoNewCRM(fromTenantId);
            if (goNewCRM) {
                webPageCommonService.enterpriseAdd(toTenantId);
            }
        } catch (Exception e) {
            logger.error("goNewCRM error by enterpriseId: {}", toTenantId, e);
        }
        try {
            if ("webPage".equals(type)) {
                copyWebPage(fromTenantId, toTenantId);
            } else if ("appPage".equals(type)) {
                copyAppPage(fromTenantId, toTenantId);
            } else if ("SelfBuiltApplication".equals(type)) {
                sandBoxCopyService.copyMenusRegister(fromTenantId, toTenantId);
            }
            return true;
        } catch (Exception e) {
            logger.error("copyCustomerPage error by module : {}, event : {}", module, event, e);
        }
        return false;
    }

    @Override
    protected boolean onSandboxDestroy(String module, DestroyEvent destroyEvent) {
        return false;
    }

    private void copyWebPage(int fromTenantId, int toTenantId) {
        homePageBaseService.copyHomePage(fromTenantId, toTenantId, BizType.CUSTOMER.getType());
    }

    private void copyAppPage(int fromTenantId, int toTenantId) {
        tenantPageTempleBaseService.copyPageTemplates(fromTenantId, toTenantId);
    }
}
