package com.facishare.webpage.customer.mq.producer;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.mq.model.AllocateRecordEvent;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Slf4j
@Component
public class AllocateRecordMqProducer {
    private static final Logger logger = LoggerFactory.getLogger(InnerAppMqProducer.class);
    private static final String sectionName = "AllocateRecordMqProducer";
    private static final String configName = "fs-ui-paas-mq-config";
    private static final String topic = "allocate-record-type-change-event";

    private AutoConfMQProducer producer;

    @PostConstruct
    void init() {
        producer = new AutoConfMQProducer(configName, sectionName);
        log.info("Producer loaded config: {},sectionName:{} init over", configName, sectionName);
    }
    public boolean sendEvent(AllocateRecordEvent messageEvent) {
        Message message = new Message();
        message.setBody(JSONObject.toJSONBytes(messageEvent));
        message.setTopic(topic);
        SendResult sendResult = producer.send(message);
        if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
            logger.info("allocate-record-type-change-event msgId={}, status={},uuid {}", sendResult.getMsgId(), sendResult.getSendStatus());
            return true;
        } else {
            logger.error("allocate-record-type-change-event msgId={}, status={},uuid {}", sendResult.getMsgId(), sendResult.getSendStatus());
            return false;
        }

    }
}
