package com.facishare.webpage.customer.mq;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.employee.BatchGetEmployeeIdsByDepartmentId;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.webpage.customer.mq.model.AppViewChangeEvent;
import com.facishare.webpage.customer.remote.InnerAppCacheService;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.List;

/**
 * Created by zhangyu on 2021/1/25
 */
@Component
public class InnerAppMqConsumer {

    private static final Logger logger = LoggerFactory.getLogger(InnerAppMqConsumer.class);

    private AutoConfMQPushConsumer consumer;

    //原配置文件 fs-webpage-inner-app-change-mq
    //topic AppViewChange
    private String configName = "fs-ui-paas-mq-config";
    private String sectionName = "InnerAppMqConsumer";
    @Resource
    private InnerAppCacheService innerAppCacheService;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private EmployeeProviderService employeeProviderService;

    @PostConstruct
    public void init() {
        MessageListenerConcurrently listener = (messages, context) -> {
            messages.forEach(this::processMessage);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        String relSectionName = sectionName;
        String env = System.getProperty("env");
        if (StringUtils.isNotBlank(env)) {
            relSectionName = sectionName + "_" + env;
        }

        consumer = new AutoConfMQPushConsumer(configName, relSectionName, listener);
        consumer.start();
        logger.info("InnerAppMqConsumer configName {}, sectionName {} init over", configName, relSectionName);
    }

    public void processMessage(Message message) {
        try {
            MessageHelper.fillContextFromMessage(TraceContext.get(), message);
            AppViewChangeEvent appViewChangeEvent = JSON.parseObject(message.getBody(), AppViewChangeEvent.class);
            logger.info("InnerAppMqConsumer appViewChangeEvent : {}", appViewChangeEvent);
            String enterpriseAccount = appViewChangeEvent.getFsEa();
            int enterpriseId = eieaConverter.enterpriseAccountToId(enterpriseAccount);

            switch (appViewChangeEvent.getChangeType()) {
                case 1:
                case 2:
                case 4:
                case 5:
                    deleteCacheAll(enterpriseId);
                    break;
                case 3:
                    deleteCacheByScope(appViewChangeEvent, enterpriseId);
                    break;
                default:
                    break;
            }

        } catch (Exception e) {
            logger.error("InnerAppMqConsumer error", e);
        } finally {
            TraceContext.remove();
        }
    }

    private void deleteCacheByScope(AppViewChangeEvent appViewChangeEvent, int enterpriseId) {
        List<Integer> deleteCacheEmployees = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(appViewChangeEvent.getDeleteUserList())) {
            deleteCacheEmployees.addAll(appViewChangeEvent.getDeleteUserList());
        }
        if (CollectionUtils.isNotEmpty(appViewChangeEvent.getInsertUserList())) {
            deleteCacheEmployees.addAll(appViewChangeEvent.getInsertUserList());
        }
        deleteCacheByEmployeeId(enterpriseId, deleteCacheEmployees);

        List<Integer> deleteCacheDepartmentIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(appViewChangeEvent.getDeleteDepartmentList())) {
            deleteCacheDepartmentIds.addAll(appViewChangeEvent.getDeleteDepartmentList());
        }
        if (CollectionUtils.isNotEmpty(appViewChangeEvent.getInsertDepartmentList())) {
            deleteCacheDepartmentIds.addAll(appViewChangeEvent.getInsertDepartmentList());
        }
        deleteCacheByDepartmentId(enterpriseId, deleteCacheDepartmentIds);
        if ((appViewChangeEvent.getDelEaAuth() != null && appViewChangeEvent.getDelEaAuth())
                || (appViewChangeEvent.getAddEaAuth() != null && appViewChangeEvent.getAddEaAuth())) {
            deleteCacheAll(enterpriseId);
        }
    }

    /**
     * 根据人进行清除缓存
     *
     * @param enterpriseId
     * @param employeeIds
     */
    private void deleteCacheByEmployeeId(int enterpriseId, List<Integer> employeeIds) {
        if (CollectionUtils.isEmpty(employeeIds)) {
            return;
        }
        employeeIds.stream().forEach(x -> {
            innerAppCacheService.removeCacheByEmployee(enterpriseId, x);
        });
    }

    /**
     * 根据部门进行清除缓存
     *
     * @param enterpriseId
     * @param departmentIds
     */
    private void deleteCacheByDepartmentId(int enterpriseId, List<Integer> departmentIds) {
        if (CollectionUtils.isEmpty(departmentIds)) {
            return;
        }
        BatchGetEmployeeIdsByDepartmentId.Arg arg = new BatchGetEmployeeIdsByDepartmentId.Arg();
        arg.setEnterpriseId(enterpriseId);
        arg.setDepartmentIds(departmentIds);
        arg.setIncludeLowDepartment(true);
        arg.setRunStatus(RunStatus.ACTIVE);
        BatchGetEmployeeIdsByDepartmentId.Result result = employeeProviderService.batchGetEmployeeIdsByDepartmentId(arg);
        List<Integer> employeeIds = result.getEmployeeIds();
        if (CollectionUtils.isEmpty(employeeIds)) {
            return;
        }
        employeeIds.stream().forEach(x -> {
            innerAppCacheService.removeCacheByEmployee(enterpriseId, x);
        });
    }

    /**
     * 清除整个企业的缓存
     *
     * @param enterpriseId
     */
    private void deleteCacheAll(int enterpriseId) {
        innerAppCacheService.removeCacheByEnterpriseId(enterpriseId);
    }

    @PreDestroy
    public void shutDown() {
        consumer.close();
    }

}
