package com.facishare.webpage.customer.dao.entity;

import com.google.common.collect.Lists;
import lombok.Data;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.*;

import java.util.List;

/**
 * Created by <PERSON><PERSON> on 19/12/13.
 */
@Entity(value = "MenuEntity", noClassnameStored = true)
@Indexes({
        @Index(fields = {
                @Field("tenantId")
                , @Field("menuId")
                , @Field("collectionId")
        }
                /**
                 * 页面模板反查关联的菜单
                */
                , options = @IndexOptions(unique = true, background = true))
})
@Data
public class MenuEntity {

    @Id
    private ObjectId id;

    @Property("TId")
    private int tenantId;

    @Property("CId")
    private String collectionId;

    @Property("MId")
    private String menuId;

    @Property("apiName")
    private String apiName; // 自定义页面

    @Property("apiNameList")
    private List<String> apiNameList;   // bi

    @Property("supportClients")
    private List<String> supportClients;

    @Property("applyType")
    private int applyType;

    @Property("appId")
    private String appId;

    @Property("scopeList")
    private List<String> scopeList = Lists.newArrayList("D-999999");

    @Property("N")
    private String name;

    @Property("OApiName")
    private String objectApiName;

    @Property("ORTApiName")
    private String objectRecordTypeApiName;

    @Property("MT")
    private int menuType;

    @Property("NKey")
    private String nameI18Key;

    @Property("ICON")
    private String icon;

    @Property("URL")
    private String url;

    @Property("DT")
    private List<String> deviceTypes = Lists.newArrayList();

    @Property("PPL")
    private String personPrivilege;

    @Property("TPL")
    private String tenantPrivilege;

    @Property("SRC")
    private String source;

    @Property("CT")
    private long createTime;

    @Property("CID")
    private int creatorId;

    @Property("UT")
    private long updateTime;

    @Property("UID")
    private int updaterId;


}
