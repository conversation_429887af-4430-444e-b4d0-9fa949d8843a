package com.facishare.webpage.customer.mq.producer;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Created by huangzhw on 2017/4/22.
 */
@Slf4j
@Service//TODO
public class MQSender {
    /*@Resource(name = "linkAppMQSender")
    private AutoConfMQProducer mqSender;
    private AutoConfMQProducer cloudMqSender;
    @Autowired
    private CloudUtil cloudUtil;
    @Autowired
    private EIEAConverter eieaConverter;

    @PostConstruct
    private void init() {
        cloudMqSender = new AutoConfMQProducer("rocketmq-consumer.ini", "common,name_server_08,producer_fs-linkapp-notify_cloud");
    }

    private boolean isClound() {
        String ea = null;
        try {
            String ei = TraceContext.get().getEi();
            if (StringUtils.isEmpty(ei)) {
                ea = TraceContext.get().getEa();
            } else {
                ea = eieaConverter.enterpriseIdToAccount(Integer.valueOf(ei));
            }
            if (StringUtils.isNotEmpty(ea)) {
                CloudConfigVo cloudConfigVo = cloudUtil.getCloudConfig(ea).getData();
                return cloudConfigVo.isCloud();
            }
        } catch (Exception e) {
            log.warn("", e);
        }

        return false;
    }

    public void send(String tag, CanProto data) {
        try {
            SendResult sendResult = null;
            Message message = new Message();
            message.setTags(tag);
            message.setBody(data.toProto());
            if (isClound()) {
                log.info("is clound mq,ei=" + TraceContext.get().getEi());
                message.setTopic(cloudMqSender.getDefaultTopic());
                sendResult = cloudMqSender.send(message);
            } else {
                message.setTopic(mqSender.getDefaultTopic());
                sendResult = mqSender.send(message);
            }
            log.info("MQ - send success, tags:{}, data:{}", tag, data.toString());
            MqLogData mqLogData = MqLogData.builder().topic(sendResult.getMessageQueue().getTopic()).tagsOrFlag(tag).eventBody(data.toString()).msgId(sendResult.getMsgId()).build();
            EsLogSendUtil.sendErMqLog(mqLogData);
        } catch (Exception e) {
            log.warn("", e);
        }
    }*/
}
