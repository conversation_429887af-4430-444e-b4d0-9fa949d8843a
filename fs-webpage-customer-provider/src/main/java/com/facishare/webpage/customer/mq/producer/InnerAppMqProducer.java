package com.facishare.webpage.customer.mq.producer;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.mq.model.AppViewChangeEvent;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR> create by wangsh on 2023/11/20
 */
@Slf4j
@Component
public class InnerAppMqProducer {
    private static final Logger logger = LoggerFactory.getLogger(InnerAppMqProducer.class);
    private static final String sectionName = "InnerAppMqProducer";
    private static final String configName = "fs-ui-paas-mq-config";
    private static final String topic = "AppViewChange";

    private AutoConfMQProducer producer;

    @PostConstruct
    void init() {
        producer = new AutoConfMQProducer(configName, sectionName);
        log.info("Producer loaded config: {},sectionName:{} init over", configName, sectionName);
    }
    public boolean sendEvent(AppViewChangeEvent messageEvent) {
        Message message = new Message();
        message.setBody(JSONObject.toJSONBytes(messageEvent));
        message.setTopic(topic);
        SendResult sendResult = producer.send(message);
        if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
            logger.info("sendAppViewChangeEvent msgId={}, status={},uuid {}", sendResult.getMsgId(), sendResult.getSendStatus());
            return true;
        } else {
            logger.error("sendAppViewChangeEvent msgId={}, status={},uuid {}", sendResult.getMsgId(), sendResult.getSendStatus());
            return false;
        }

    }

}
