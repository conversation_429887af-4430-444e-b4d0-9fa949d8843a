package com.facishare.webpage.customer.mq;

import com.alibaba.fastjson.JSON;
import com.facishare.webpage.customer.mq.model.ObjectChangeEvent;
import com.facishare.webpage.customer.remote.ObjectService;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.google.common.base.Charsets;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * Created by z<PERSON><PERSON> on 2020/2/22
 */
@Service
public class ObjectEventConsumer {

    private static final Logger logger = LoggerFactory.getLogger(ObjectEventConsumer.class);

    private AutoConfMQPushConsumer consumer;
    @Autowired
    private ObjectService objectService;

    //原配置文件 fs-webpage-object-mq-consumer
    //原topic：object-describe
    private String configName = "fs-ui-paas-mq-config";
    private String sectionName = "ObjectEventConsumer";

    @PostConstruct
    public void init() {
        MessageListenerConcurrently listener = (messages, context) -> {
            messages.forEach(this::processMessage);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        String relSectionName = sectionName;
        String env = System.getProperty("env");
        if (StringUtils.isNotBlank(env)) {
            relSectionName = sectionName + "_" + env;
        }

        consumer = new AutoConfMQPushConsumer(configName, relSectionName, listener);
        consumer.start();
        logger.info("ObjectEventConsumer configName {}, sectionName {} init over", configName, relSectionName);
    }

    public void processMessage(MessageExt message) {

        ObjectChangeEvent event = JSON.parseObject(new String(message.getBody(), Charsets.UTF_8), ObjectChangeEvent.class);
        logger.info("ObjectEventConsumer getObjectChangeEvent {}", event);
        objectService.cleanCache(event.getTenantId());

    }

    @PreDestroy
    public void shutDown() {
        consumer.close();
    }

}
