package com.facishare.webpage.customer.mq;

import com.facishare.sandbox.listener.SandboxEvent;
import com.facishare.sandbox.module.CreateEvent;
import com.facishare.sandbox.module.DestroyEvent;
import com.facishare.sandbox.module.Module;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.dao.entity.HomePageLayoutEntity;
import com.facishare.webpage.customer.service.HomePageBaseService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by zhangyu on 2020/3/30
 */
@Component("homePageInitTask")
public class HomePageInitTask extends SandboxEvent {

    private static final Logger logger = LoggerFactory.getLogger(HomePageInitTask.class);
    @Resource
    private HomePageBaseService homePageBaseService;


    public HomePageInitTask() {
        super("webpage_SANDBOX_EVENT_HomePage", Module.HomePage);
    }

    @Override
    protected boolean onSandboxCreated(String module, CreateEvent event) {
        logger.info("onSandboxCreated module:{}, event:{}", module, event);
        try {
            homePageBaseService.copyHomePage(event.getFrom().getEnterpriseId(),
                    event.getTo().getEnterpriseId(), BizType.CRM.getType());
        } catch (Exception e) {
            logger.error("onSandboxCreated has error module:{}, event:{}", module, event, e);
            return false;
        }
        return true;
    }

    @Override
    protected boolean onSandboxDestroy(String module, DestroyEvent event) {
        logger.info("onSandboxCreated module:{}, event:{}", module, event);
        try {
            homePageBaseService.destroyHomePage(event.getEnterpriseId());
        } catch (Exception e) {
            logger.error("onSandboxCreated has error module:{}, event:{}", module, event, e);
            return false;
        }
        return true;
    }
}
