package com.facishare.webpage.customer.mq;

import com.facishare.sandbox.listener.SandboxEvent;
import com.facishare.sandbox.module.CreateEvent;
import com.facishare.sandbox.module.DestroyEvent;
import com.facishare.sandbox.module.Module;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.service.TenantMenuService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/12/13.
 */
@Component("menuInitTask")
public class MenuInitTask extends SandboxEvent {
    private static final Logger logger = LoggerFactory.getLogger(MenuInitTask.class);
    @Resource
    private TenantMenuService tenantMenuService;

    public MenuInitTask() {
        super("fs-webpage", Module.MENU);
    }

    @Override
    protected boolean onSandboxCreated(String module, CreateEvent event) {
        logger.info("onSandboxCreated module {},event {}", module, event);
        int fromTenantId = event.getFrom().getEnterpriseId();
        int toTenantId = event.getTo().getEnterpriseId();
        try {
            tenantMenuService.copyTenantMenu(fromTenantId, toTenantId, BizType.CRM.getType());
            return true;
        } catch (Exception e) {
            logger.error("onSandboxCreated module :{}, event : {}", module, event, e);
            return false;
        }
    }

    @Override
    protected boolean onSandboxDestroy(String module, DestroyEvent event) {
        logger.info("onSandboxCreated module {},event {}", module, event);
        int enterpriseId = event.getEnterpriseId();

        try {
            tenantMenuService.destroyTenantMenu(enterpriseId);
            return true;
        } catch (Exception e) {
            logger.error("onSandboxDestroy module :{}, event :{}", module, event, e);
            return false;
        }
    }
}
