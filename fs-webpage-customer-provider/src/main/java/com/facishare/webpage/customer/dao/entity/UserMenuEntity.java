package com.facishare.webpage.customer.dao.entity;

import com.google.common.collect.Lists;
import lombok.Data;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.*;

import java.io.Serializable;
import java.util.List;


/**
 * Created by she<PERSON> on 19/12/13.
 */
@Entity(value = "UserMenu", noClassnameStored = true)
@Indexes({
        @Index(fields = {@Field("tenantId"), @Field("employeeId"), @Field("tenantMenuId")})
})
@Data
public class UserMenuEntity implements Serializable {

    @Id
    private ObjectId id;

    @Property("tenantId")
    private int tenantId;

    @Property("employeeId")
    private int employeeId;

    @Property("createTime")
    private long createTime;

    @Property("updateTime")
    private long updateTime;

    @Embedded("menuDataEntities")
    private List<MenuDataEntity> menuDataEntities = Lists.newArrayList();

    @Property("tenantMenuId")
    private String tenantMenuId;

    @Property("status")
    private int status;


}
