package com.facishare.webpage.customer.mq;

import com.facishare.sandbox.listener.SandboxEvent;
import com.facishare.sandbox.module.CreateEvent;
import com.facishare.sandbox.module.DestroyEvent;
import com.facishare.sandbox.module.Module;
import com.facishare.webpage.customer.service.SandBoxCopyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Created by zhangyu on 2021/1/26
 */
@Component("paaSAppSandbox")
public class PaaSAppSandbox extends SandboxEvent {

    private static final Logger logger = LoggerFactory.getLogger(PaaSAppSandbox.class);

    @Resource
    private SandBoxCopyService sandBoxCopyService;

    public PaaSAppSandbox() {
        super("webpage_SANDBOX_EVENT_paaSApp", Module.PaaSApp);
    }

    @Override
    protected boolean onSandboxCreated(String module, CreateEvent createEvent) {
        logger.info("PaaSAppSandbox onSandboxCreated module : {}, createEvent : {}", module, createEvent);
        try {
            sandBoxCopyService.copyPaaSApp(createEvent.getFrom().getEnterpriseId(), createEvent.getTo().getEnterpriseId());
            logger.info("PaaSAppSandbox onSandboxCreated success module : {}, createEvent : {}", module, createEvent);
            return true;
        } catch (Exception e) {
            logger.error("copyPaaSApp error by createEvent : {}", createEvent, e);
        }
        return false;
    }

    @Override
    protected boolean onSandboxDestroy(String module, DestroyEvent destroyEvent) {
        try {
            sandBoxCopyService.deletePaaSApp(destroyEvent.getEnterpriseId());
            return true;
        } catch (Exception e) {
            logger.error("deletePaaSApp error by destroyEvent : {}", destroyEvent, e);
        }
        return false;
    }
}
