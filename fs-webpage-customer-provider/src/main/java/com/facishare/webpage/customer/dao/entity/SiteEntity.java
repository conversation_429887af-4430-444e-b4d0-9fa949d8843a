package com.facishare.webpage.customer.dao.entity;

import com.facishare.webpage.customer.api.constant.ClientType;
import com.facishare.webpage.customer.constant.PublishStatus;
import com.facishare.webpage.customer.api.model.ScopeForCross;
import lombok.Data;
import org.apache.commons.lang.BooleanUtils;
import org.mongodb.morphia.annotations.*;

import java.util.List;
import java.util.Objects;

/**
 * Created by zhouwr on 2024/11/5.
 */
@Data
@Entity(value = "SiteEntity", noClassnameStored = true)
@Indexes({
        @Index(fields = {@Field("tenantId"), @Field("apiName"), @Field("deleteId")}, options = @IndexOptions(background = true, unique = true)),
        @Index(fields = {@Field("tenantId"), @Field("siteId"), @Field("deleteId")}, options = @IndexOptions(background = true, unique = true)),
        @Index(fields = {@Field("tenantId"), @Field("appId")}, options = @IndexOptions(background = true))
})
public class SiteEntity {
    @Property("apiName")
    private String apiName;
    @Property("name")
    private String name;
    @Property("description")
    private String description;
    @Property("siteId")
    private String siteId;
    @Property("needLogin")
    private Boolean needLogin;
    @Embedded("scopeListForCross")
    private List<ScopeForCross> scopeListForCross;
    @Property("appId")
    private String appId;
    @Property("status")
    private Integer status;
    @Property("sourceType")
    private String sourceType;
    @Property("tenantId")
    private int tenantId;
    @Id
    private String id;
    @Property("creatorId")
    private Integer creatorId;
    @Property("createTime")
    private Long createTime;
    @Property("updaterId")
    private Integer updaterId;
    @Property("updateTime")
    private Long updateTime;
    @Property("deleteId")
    private String deleteId;
    @Embedded("langList")
    private List<SiteLang> langList;
    @Property("publishStatus")
    private Integer publishStatus = PublishStatus.DRAFT.getValue();
    @Property("publishVersion")
    private Long publishVersion;
    @Property("publisherId")
    private Integer publisherId;
    @Property("publishTime")
    private Long publishTime;

    @Property("appPublishStatus")
    private Integer appPublishStatus = PublishStatus.DRAFT.getValue();
    @Property("appPublishVersion")
    private Long appPublishVersion;
    @Property("appPublisherId")
    private Integer appPublisherId;
    @Property("appPublishTime")
    private Long appPublishTime;

    public boolean published(String clientType) {
        if (Objects.equals(clientType, ClientType.mobile.getValue())) {
            return Objects.nonNull(appPublishStatus) && PublishStatus.PUBLISHED.getValue() == appPublishStatus;
        } else {
            return Objects.nonNull(publishStatus) && PublishStatus.PUBLISHED.getValue() == publishStatus;
        }
    }

    public boolean unpublished(String clientType) {
        if (Objects.equals(clientType, ClientType.mobile.getValue())) {
            return Objects.isNull(appPublishStatus) || PublishStatus.DRAFT.getValue() == appPublishStatus;
        } else {
            return Objects.isNull(publishStatus) || PublishStatus.DRAFT.getValue() == publishStatus;
        }
    }

    public boolean needLogin() {
        return BooleanUtils.isTrue(this.getNeedLogin());
    }
}
