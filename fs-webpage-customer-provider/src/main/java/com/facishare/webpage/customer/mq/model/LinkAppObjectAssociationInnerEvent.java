package com.facishare.webpage.customer.mq.model;

import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

/**
 * 添加完应用的业务对象后初始化对象事件
 */
@Getter
@Setter
@ToString
//TODO delete
public class LinkAppObjectAssociationInnerEvent extends ApplicationEvent {
    public static final String TAG = "8001";
    @Tag(1)
    private String upstreamEa; // 上游企业
    @Tag(2)
    private String appId;  //应用id
    @Tag(3)
    private String objectApiName; //业务对象API_NAME

    public LinkAppObjectAssociationInnerEvent(Object source, String upstreamEa, String appId, String objectApiName) {
        super(source);
        this.upstreamEa = upstreamEa;
        this.appId = appId;
        this.objectApiName = objectApiName;
    }

    public static LinkAppObjectAssociationInnerEvent getInstance(Object source, String upstreamEa, String appId, String objectApiName) {
        return new LinkAppObjectAssociationInnerEvent(source, upstreamEa, appId, objectApiName);
    }
}
