#!/bin/bash

# 检查参数
if [ "$#" -ne 1 ]; then
    echo "使用方法: $0 diff_file_path"
    exit 1
fi

DIFF_FILE=$1

# 检查文件是否存在
if [ ! -f "$DIFF_FILE" ]; then
    echo "错误: 文件 $DIFF_FILE 不存在"
    exit 1
fi

# 设置API地址和请求头
API_URL="http://localhost/brush/goOldCrm"
CONTENT_TYPE="Content-Type: application/json"

# 设置输出文件
OUTPUT_FILE="goOldCrm_response_$(date +%Y%m%d_%H%M%S).log"
echo "响应结果将写入文件: $OUTPUT_FILE"

# 初始化计数器
counter=0

# 读取文件中的每个ID并发送请求
while IFS= read -r tenant_id; do
    # 跳过空行
    [ -z "$tenant_id" ] && continue
    
    # 增加计数器
    ((counter++))
    
    # 构建请求体
    REQUEST_BODY="{\"tenantId\":$tenant_id}"
    
    echo "正在处理租户ID: $tenant_id (第 $counter 个)"
    
    # 发送请求并获取HTTP状态码和响应内容
    response=$(curl -s -w "\n%{http_code}" -X POST \
        -H "$CONTENT_TYPE" \
        -H "x-fs-ei: $tenant_id" \
        -d "$REQUEST_BODY" \
        "$API_URL")
    
    # 分离响应内容和状态码
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | sed '$d')
    
    # 检查HTTP状态码
    if [ "$http_code" != "200" ]; then
        echo "错误 - 租户ID $tenant_id: HTTP状态码 $http_code"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] TenantId: $tenant_id, HTTP状态码: $http_code, Response: $response_body" >> "$OUTPUT_FILE"
        continue
    fi
    
    # 解析响应中的success字段
    success=$(echo "$response_body" | grep -o '"success":[^,}]*' | cut -d':' -f2)
    
    # 如果success为false，提取并打印message
    if [ "$success" = "false" ]; then
        message=$(echo "$response_body" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)
        echo "错误 - 租户ID $tenant_id: $message"
    fi
    
    # 将结果写入文件
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] TenantId: $tenant_id, Response: $response_body" >> "$OUTPUT_FILE"
    
    # 每处理100个请求休息一次
    if [ $((counter % 100)) -eq 0 ]; then
        echo "已处理 $counter 个请求，休息0.1秒..."
        sleep 0.1
    fi
    
done < "$DIFF_FILE"

echo "处理完成，共处理 $counter 个租户ID"
echo "详细响应结果请查看: $OUTPUT_FILE" 