#!/usr/bin/env python
# -*- coding: utf-8 -*-

import argparse

def read_ids_from_file(filename):
    with open(filename, 'r') as f:
        # 读取文件内容，去除空白字符，按逗号分割
        content = f.read().strip()
        # 将字符串转换为整数集合
        return set(int(x) for x in content.split(',') if x)

def main():
    # 设置命令行参数
    parser = argparse.ArgumentParser(description='比较两个文件中的租户ID差异')
    parser.add_argument('file1', help='第一个输入文件路径')
    parser.add_argument('file2', help='第二个输入文件路径')
    parser.add_argument('output', help='输出文件路径')
    
    args = parser.parse_args()
    
    # 读取两个文件中的ID
    all_ids = read_ids_from_file(args.file1)
    new_crm_ids = read_ids_from_file(args.file2)
    
    # 找出在all_ids中存在但在new_crm_ids中不存在的ID
    diff_ids = all_ids - new_crm_ids
    
    # 将结果排序
    sorted_diff_ids = sorted(list(diff_ids))
    
    # 将结果写入文件，每个ID占一行
    with open(args.output, 'w') as f:
        for id_num in sorted_diff_ids:
            f.write("%d\n" % id_num)
    
    print "比较结果已保存到文件：%s" % args.output
    print "总共有 %d 个不同的ID" % len(sorted_diff_ids)

if __name__ == '__main__':
    main() 