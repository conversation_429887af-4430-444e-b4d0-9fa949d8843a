package com.facishare.paas.appframework.common.util;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.service.dto.FindObjectPageComponentList;
import com.facishare.webpage.customer.api.constant.Constant;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeableConfig;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/9/9
 */
@Slf4j
public final class WebPageConfig {

    private static final String DEFAULT_BI_CARDID = "BI_";

    private static final Set<String> CONFIG_NAMES = Sets.newHashSet();
    public static final String FS_WEBPAGE_COMPONENT_NAME_CONFIG = "fs-webpage-component-name-config";
    public static final String FS_WEBPAGE_CUSTOMER_WEB_COMPONENT_LIST_MAP = "fs-webpage-customer-web-component-list-map";
    public static final String FS_WEBPAGE_CUSTOMER_WIDGETS_COLLECTION = "fs-webpage-customer-widgets-collection";
    public static final String FS_WEBPAGE_CUSTOMER_WIDGET_ALL = "fs-webpage-customer-widget-all";

    private List<FindObjectPageComponentList.ObjectPageComponent> objectPageComponentList = Collections.emptyList();

    public static WebPageConfig getInstance() {
        return WebPageConfigHolder.INSTANCE;
    }

    private WebPageConfig() {
        reload();
    }

    private void reload() {
        Collection<SimpleWebPageWidget> allWidgets = getAllWidgets();
        if (CollectionUtils.empty(allWidgets)) {
            return;
        }

        Map<String, SimpleWebPageWidgetCollection> widgetCollections = getWidgetCollections();
        Map<String, List<SimpleWebPageComponent>> allComponentList = getAllComponentList();
        Map<String, String> componentNameMap = getComponentNameMap();

        List<FindObjectPageComponentList.ObjectPageComponent> result = Lists.newArrayList();
        for (SimpleWebPageWidget widget : allWidgets) {
            FindObjectPageComponentList.ObjectPageComponent objectPageComponent = getObjectPageComponent(widget);
            List<FindObjectPageComponentList.ObjectPageComponent> list = fillTypeAndConfigBizId(widgetCollections, allComponentList, componentNameMap, widget, objectPageComponent);
            result.addAll(list);
        }
        objectPageComponentList = ImmutableList.copyOf(result);
    }

    @NotNull
    private static FindObjectPageComponentList.ObjectPageComponent getObjectPageComponent(SimpleWebPageWidget widget) {
        SimpleWebPageTenantPrivilege tenantPrivilege = widget.getTenantPrivilege();
        FindObjectPageComponentList.ObjectPageComponent objectPageComponent = new FindObjectPageComponentList.ObjectPageComponent();
        if (Objects.nonNull(tenantPrivilege)) {
            String functionCode = tenantPrivilege.getFunctionCode();
            if (!Strings.isNullOrEmpty(functionCode)) {
                objectPageComponent.setFunctionCode(functionCode);
            }
        }
        objectPageComponent.setApiName(widget.getApiName());
        objectPageComponent.setNameI18nKey(widget.getNameI18nKey());
        return objectPageComponent;
    }

    private Map<String, String> loadComponentNameConfig(IConfig config) {
        String configString = config.getString();
        try {
            if (Strings.isNullOrEmpty(configString)) {
                return Collections.emptyMap();
            }
            Map<String, List<String>> map = JSON.parseObject(configString, Map.class);
            if (CollectionUtils.empty(map)) {
                return Collections.emptyMap();
            }
            Map<String, String> cardIdComponentMap = Maps.newHashMap();
            map.forEach((name, cardIds) -> {
                cardIds.forEach(cardId -> {
                    cardIdComponentMap.put(cardId, name);
                });
            });
            return cardIdComponentMap;
        } catch (Exception e) {
            log.warn("loadComponentNameConfig fail! config:{}", configString, e);
            throw e;
        }
    }

    private Map<String, List<SimpleWebPageComponent>> loadWebComponents(IConfig config) {
        String configString = config.getString();
        try {
            if (Strings.isNullOrEmpty(configString)) {
                return Collections.emptyMap();
            }
            Map<String, String> configMap = JSON.parseObject(configString, Map.class);
            if (CollectionUtils.empty(configMap)) {
                return Collections.emptyMap();
            }
            Map<String, List<SimpleWebPageComponent>> tmp = new HashMap<>();
            configMap.forEach((key, configName) -> {
                if (!StringUtils.contains(key, "ObjectListPage")
                        && !StringUtils.contains(key, "ObjectDetailPage")
                        && !StringUtils.contains(key, "ObjectEditPage")) {
                    return;
                }
                IChangeableConfig iConfig = registerConfig(configName);
                String configStr = iConfig.getString();
                if (Strings.isNullOrEmpty(configStr)) {
                    return;
                }
                List<SimpleWebPageComponent> componentList = JSON.parseArray(configStr, SimpleWebPageComponent.class);
                tmp.put(key, componentList);
            });
            return tmp;
        } catch (Exception e) {
            log.warn("loadWebComponents fail! config:{}", configString, e);
            throw e;
        }
    }

    private Map<String, SimpleWebPageWidgetCollection> loadWidgetCollection(IConfig config) {
        String configString = config.getString();
        try {
            if (Strings.isNullOrEmpty(configString)) {
                return Collections.emptyMap();
            }
            List<SimpleWebPageWidgetCollection> collections = JSON.parseArray(configString, SimpleWebPageWidgetCollection.class);
            if (CollectionUtils.empty(collections)) {
                return Collections.emptyMap();
            }
            return collections.stream()
                    .collect(Collectors.toMap(SimpleWebPageWidgetCollection::getId, Function.identity(), (x, y) -> y));
        } catch (Exception e) {
            log.warn("loadWidgets fail! config:{}", configString, e);
            throw e;
        }
    }

    private Map<String, SimpleWebPageWidget> loadWidgets(IConfig config) {
        String configString = config.getString();
        try {
            Map<String, SimpleWebPageWidget> allWidgets = Maps.newHashMap();
            if (Strings.isNullOrEmpty(configString)) {
                return allWidgets;
            }
            Map<String, String> configMap = JSON.parseObject(configString, Map.class);
            if (CollectionUtils.empty(configMap)) {
                return allWidgets;
            }
            configMap.forEach((key, configName) -> {
                if (!Objects.equals("BASE", key)) {
                    return;
                }
                IChangeableConfig iConfig = registerConfig(configName);
                String configStr = iConfig.getString();
                if (Strings.isNullOrEmpty(configStr)) {
                    return;
                }
                List<SimpleWebPageWidget> simpleWebPageWidgets = JSON.parseArray(configStr, SimpleWebPageWidget.class);
                if (CollectionUtils.empty(simpleWebPageWidgets)) {
                    return;
                }
                simpleWebPageWidgets.forEach(simpleWebPageWidget -> allWidgets.put(simpleWebPageWidget.getId(), simpleWebPageWidget));

            });
            return allWidgets;
        } catch (Exception e) {
            log.warn("loadWidgets fail! config:{}", configString, e);
            throw e;
        }
    }

    public static List<FindObjectPageComponentList.ObjectPageComponent> findAllObjectPageComponentList() {
        return getInstance().objectPageComponentList;
    }

    public static List<FindObjectPageComponentList.ObjectPageComponent> findObjectPageComponentList(String bizId, String templeType) {
        try {
            String configBizId = bizId + Constant.SEPARATOR + templeType;
            return getInstance().objectPageComponentList.stream()
                    .filter(it -> Objects.equals(configBizId, it.getConfigBizId()))
                    .collect(Collectors.toList());
        } catch (Throwable e) {
            log.warn("findObjectPageComponentList fail!", e);
        }
        return Collections.emptyList();
    }

    private List<FindObjectPageComponentList.ObjectPageComponent> fillTypeAndConfigBizId(Map<String, SimpleWebPageWidgetCollection> widgetCollections,
                                                                                         Map<String, List<SimpleWebPageComponent>> allComponentList,
                                                                                         Map<String, String> componentNameMap,
                                                                                         SimpleWebPageWidget widget,
                                                                                         FindObjectPageComponentList.ObjectPageComponent objectPageComponent) {
        List<FindObjectPageComponentList.ObjectPageComponent> result = Lists.newArrayList();
        for (SimpleWebPageWidgetCollection widgetCollection : getWidgetCollectionWithDefineWidgets(widgetCollections, widget.getId())) {
            for (Pair<SimpleWebPageComponent, String> pair : getComponentByCollectionId(allComponentList, widgetCollection.getId())) {
                SimpleWebPageComponent component = pair.getKey();
                FindObjectPageComponentList.ObjectPageComponent copied = copy(objectPageComponent);

                result.add(copied);
                copied.setConfigBizId(pair.getValue());
                if (Objects.equals(SimpleWebPageComponent.WIDGET_COLLECTION_TYPE, component.getComponentType())) {
                    String type = getComponentName(componentNameMap, StringUtils.isEmpty(widget.getCardId()) ? widget.getId() : widget.getCardId());
                    copied.setType(type);
                }
            }
        }
        for (Pair<SimpleWebPageComponent, String> pair : getComponentCollectionById(allComponentList, widget.getId())) {
            SimpleWebPageComponent component = pair.getKey();

            FindObjectPageComponentList.ObjectPageComponent copied = copy(objectPageComponent);
            result.add(copied);
            copied.setConfigBizId(pair.getValue());
            if (Objects.equals(SimpleWebPageComponent.WIDGET_TYPE, component.getComponentType())) {
                String type = getComponentName(componentNameMap, StringUtils.isEmpty(widget.getCardId()) ? widget.getId() : widget.getCardId());
                copied.setType(type);
                String nameI18nKey = getNameI18nKey(component);
                if (StringUtils.isNotEmpty(nameI18nKey)) {
                    copied.setNameI18nKey(nameI18nKey);
                }
            }
        }
        return result;
    }

    private String getNameI18nKey(SimpleWebPageComponent component) {
        return org.apache.commons.lang3.StringUtils.defaultIfBlank(component.getTitleI18nKey(), component.getNameI18nKey());
    }

    private FindObjectPageComponentList.ObjectPageComponent copy(FindObjectPageComponentList.ObjectPageComponent objectPageComponent) {
        FindObjectPageComponentList.ObjectPageComponent result = new FindObjectPageComponentList.ObjectPageComponent();
        result.setApiName(objectPageComponent.getApiName());
        result.setNameI18nKey(objectPageComponent.getNameI18nKey());
        result.setFunctionCode(objectPageComponent.getFunctionCode());
        return result;
    }


    private Collection<SimpleWebPageWidget> getAllWidgets() {
        IChangeableConfig config = registerConfig(FS_WEBPAGE_CUSTOMER_WIDGET_ALL);
        Map<String, SimpleWebPageWidget> stringSimpleWebPageWidgetMap = loadWidgets(config);
        return stringSimpleWebPageWidgetMap.values();
    }

    private Map<String, SimpleWebPageWidgetCollection> getWidgetCollections() {
        IChangeableConfig config = registerConfig(FS_WEBPAGE_CUSTOMER_WIDGETS_COLLECTION);
        return loadWidgetCollection(config);
    }

    private Map<String, List<SimpleWebPageComponent>> getAllComponentList() {
        IChangeableConfig config = registerConfig(FS_WEBPAGE_CUSTOMER_WEB_COMPONENT_LIST_MAP);
        return loadWebComponents(config);
    }

    private Map<String, String> getComponentNameMap() {
        IChangeableConfig config = registerConfig(FS_WEBPAGE_COMPONENT_NAME_CONFIG);
        return loadComponentNameConfig(config);
    }

    private IChangeableConfig registerConfig(String configName) {
        if (CONFIG_NAMES.add(configName)) {
            log.info("register config:{}", configName);
            return ConfigFactory.getConfig(configName, config -> reload(), false);
        }
        return ConfigFactory.getConfig(configName);
    }

    private List<SimpleWebPageWidgetCollection> getWidgetCollectionWithDefineWidgets(Map<String, SimpleWebPageWidgetCollection> widgetCollections,
                                                                                     String widgetId) {
        return widgetCollections.values().stream()
                .filter(it -> CollectionUtils.nullToEmpty(it.getWidgets()).contains(widgetId))
                .collect(Collectors.toList());
    }

    private List<Pair<SimpleWebPageComponent, String>> getComponentCollectionById(Map<String, List<SimpleWebPageComponent>> allComponentList, String id) {
        List<Pair<SimpleWebPageComponent, String>> result = Lists.newArrayList();
        allComponentList.forEach((key, value) -> value.stream()
                .filter(it -> Objects.equals(id, it.getId()))
                .forEach(it -> result.add(Pair.of(it, key))));
        return result;
    }

    private List<Pair<SimpleWebPageComponent, String>> getComponentByCollectionId(Map<String, List<SimpleWebPageComponent>> allComponentList, String collectionId) {
        List<Pair<SimpleWebPageComponent, String>> result = Lists.newArrayList();
        allComponentList.forEach((key, value) -> value.stream()
                .filter(it -> Objects.equals(collectionId, it.getCollectionId()))
                .forEach(it -> result.add(Pair.of(it, key))));
        return result;
    }

    private String getComponentName(Map<String, String> componentNameMap, String componentId) {
        String componentName = componentNameMap.getOrDefault(componentId, "");
        if (StringUtils.isNotEmpty(componentName)) {
            return componentName;
        }
        if (componentId.startsWith(DEFAULT_BI_CARDID)) {
            return componentNameMap.get(DEFAULT_BI_CARDID);
        }

        if (componentId.endsWith("__c")) {
            return "custom_comp";
        }
        return componentId;
    }

    private static class WebPageConfigHolder {
        private static final WebPageConfig INSTANCE = new WebPageConfig();
    }

    @Data
    private static class SimpleWebPageWidget {
        private String id;
        private String cardId;
        private String apiName;
        private SimpleWebPageTenantPrivilege tenantPrivilege;
        private String nameI18nKey;
    }

    @Data
    private static class SimpleWebPageTenantPrivilege {
        private String functionCode;
    }

    @Data
    private static class SimpleWebPageWidgetCollection {
        private String id;
        private String nameI18nKey;
        private List<String> widgets;
    }

    @Data
    private static class SimpleWebPageComponent {
        private static final Integer GROUP_TYPE = 1;
        private static final Integer MENU_COLLECTION_TYPE = 2;
        private static final Integer WIDGET_COLLECTION_TYPE = 3;
        private static final Integer WIDGET_TYPE = 4;
        private String id;
        /**
         * component类型，1 分组；2 MenuCollection；3 WidgetCollection；4 Widget；
         */
        private Integer componentType;
        /**
         * 数据CollectionId，当 componentType 为 2 时标识 menuCollection 的Id；当为 3 时，标识 widgetCollection 的Id
         */
        private String collectionId;
        private String titleI18nKey;
        private String nameI18nKey;
    }
}
