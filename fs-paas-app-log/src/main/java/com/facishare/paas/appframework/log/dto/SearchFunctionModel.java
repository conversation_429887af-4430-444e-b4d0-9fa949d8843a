package com.facishare.paas.appframework.log.dto;


import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * Created by fengjy in 2019/1/8 20:04
 */
public class SearchFunctionModel {
    @Data
    public static class Arg{
        private String tenantId;
        private String logId;
        private String functionApiName;
        private String traceId;
        private Boolean success;
        private String name;
        private Integer page = 1;
        private Integer pageSize = 20;
        private Date operationTimeFrom;
        private Date operationTimeTo;
        private Map<String, Boolean> sorts;
    }
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class Result extends BaseResult {
        List<FunctionLogInfo> results;
        private int totalPage;
        private long totalCount;
        private int page;
        private int pageSize;
    }
}
