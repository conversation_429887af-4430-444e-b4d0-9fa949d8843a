package com.facishare.paas.task.calculate.service

import com.facishare.paas.appframework.metadata.DescribeLogicService
import com.facishare.paas.appframework.metadata.relation.FieldRelationGraph
import com.facishare.paas.appframework.metadata.relation.FieldRelationGraphService
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.task.calculate.dispatcher.UpdateDispatcher
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * create by z<PERSON><PERSON> on 2020/08/10
 */
@ContextConfiguration("classpath:application-test.xml")
class AbstractCalculatorTest extends Specification {
    @Autowired
    private DescribeLogicService describeLogicService
    @Autowired
    private FieldRelationGraphService fieldRelationGraphService

    static {
        System.setProperty("spring.profiles.active", "fstest")
    }

    def "test getCalculateNodeEdgePairs"() {
        given:
        UpdateDispatcher updateDispatcher = UpdateDispatcher.builder().build()
        IObjectDescribe describe = describeLogicService.findObject(tenantId, apiName)
        FieldRelationGraph graph = fieldRelationGraphService.buildReverseFullDependencyGraph(describe, null, true, false, true, true, true)
        when:
        def result = updateDispatcher.getCalculateNodeEdgePairs(fieldChanges, describe, graph)
        then:
        println result
        1 == 1
        where:
        tenantId | apiName           | fieldChanges
        "74255"  | "object_cmnFg__c" | ["field_31w7A__c"]
    }
}
