package com.facishare.paas.task.calculate.util

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.common.util.JacksonUtils
import com.facishare.paas.task.calculate.config.CalculateConfig
import com.facishare.paas.task.calculate.model.CalculateDataChangeMessage
import com.facishare.paas.task.calculate.model.MessageQueueScale
import spock.lang.Specification

import java.util.concurrent.atomic.AtomicInteger

class MessageConvertTest extends Specification {

    def setup() {
        CalculateConfig.Config config = new CalculateConfig.Config();
        config.setSlowTimesThreshold(10)
        CalculateConfig.config = config
    }

    def "getQueueIndexName"() {
        given:
        SlowTenantCache.localCache.put("1", new AtomicInteger(time))

        def messageConvert = MessageConvert.builder()
                .tenantId("1")
                .type(type)
                .isBatch(isBatch)
                .isDelayed(isDelayed)
                .build()
        when:
        String name = messageConvert.getQueueIndexName()
        MessageQueueScale messageQueueIndex = MessageQueueScale.getByName(name)
        println name
        println messageQueueIndex.name()
        println "================"
        then:
        noExceptionThrown()
        where:
        time | type      | isBatch | isDelayed
        0    | "COUNT"   | false   | false
        0    | "FORMULA" | false   | false
        100  | "COUNT"   | false   | false
        100  | "FORMULA" | false   | false
        0    | "COUNT"   | true    | false
        0    | "COUNT"   | true    | true
        0    | "FORMULA" | true    | false
        100  | "COUNT"   | true    | false
        100  | "FORMULA" | true    | false
    }

    def "test getIndex"() {
        given:
        MessageQueueScale messageQueueIndex = MessageQueueScale.getByName(name)
        when:
        int index = MessageQueueScale.getIndex(messageQueueIndex, "0.0.0.0", size)
        println("name:" + name + ";index:" + index)
        then:
        noExceptionThrown()
        where:
        name                 | size
        "FORMULA"            | 16
        "COUNT"              | 16
        "SLOW"               | 16
        "SLOW"               | 16
        "BATCH_FORMULA"      | 16
        "BATCH_COUNT"        | 16
        "DELAY_COUNT"        | 16
        "BATCH_SLOW_FORMULA" | 16
        "BATCH_SLOW_COUNT"   | 16
    }

    def "test"() {
        when:
        def m = CalculateDataChangeMessage.builder().batch(true).build()
        then:
        println JacksonUtils.toJson(m)
        println JSON.toJSONString(m)

        println JSON.parseObject(JSON.toJSONString(m), CalculateDataChangeMessage)
        println JacksonUtils.fromJson(JacksonUtils.toJson(m), CalculateDataChangeMessage)
        1 == 1

    }
}
