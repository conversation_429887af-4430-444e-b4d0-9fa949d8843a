<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- ch.qos.logback.core.ConsoleAppender 控制台输出 -->
    <!-- <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
         <encoder>
             <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%n</pattern>
         </encoder>
     </appender>-->

    <!-- 日志中默认打印traceId和userId，方便定位问题,异常栈中去掉包含如下字符的行避免打印很多无用的信息 -->
    <property name="defaultPattern" value="%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%rEx{full,
                java.lang.Thread,
                javassist,
                sun.reflect,
                org.springframework,
                org.apache,
                org.eclipse.jetty,
                $Proxy,
                java.net,
                java.io,
                javax.servlet,
                org.junit,
                com.mysql,
                com.sun,
                org.mybatis.spring,
                cglib,
                CGLIB,
                java.util.concurrent,
                okhttp,
                org.jboss,
                }%n"/>

    <appender name="calculateLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/calculate.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/calculate.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <!-- 日志中默认打印traceId和userId，方便定位问题-->
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>

    <appender name="PerfLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/perf.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/perf.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>1GB</maxFileSize>
            <totalSizeCap>10GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>

    <appender name="monitor" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/monitor.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/monitor.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <!-- 日志中默认打印traceId和userId，方便定位问题-->
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>

    <appender name="Error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <file>${catalina.home}/logs/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/error.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <!-- 日志中默认打印traceId和userId，方便定位问题-->
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>

    <appender name="OSS_Trace" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/trace.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/trace.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>3</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <!-- 日志中默认打印traceId和userId，方便定位问题-->
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>

    <appender name="PERF_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>2048</queueSize>
        <appender-ref ref="PerfLog"/>
    </appender>

    <appender name="CALCULATE_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>2048</queueSize>
        <appender-ref ref="calculateLog"/>
    </appender>

    <appender name="MONITOR_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>2048</queueSize>
        <appender-ref ref="monitor"/>
    </appender>

    <logger name="com.facishare.paas.metadata" level="WARN" additivity="false">
        <appender-ref ref="CALCULATE_ASYNC"/>
        <appender-ref ref="Error"/>
    </logger>

    <logger name="com.facishare.paas.appframework" level="INFO" additivity="false">
        <appender-ref ref="CALCULATE_ASYNC"/>
        <appender-ref ref="Error"/>
    </logger>

    <appender name="OSS_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>2048</queueSize>
        <appender-ref ref="OSS_Trace"/>
    </appender>

    <logger name="com.github.trace" level="INFO" additivity="false">
        <appender-ref ref="OSS_ASYNC"/>
    </logger>

    <logger name="com.facishare.paas.task.calculate" level="INFO" additivity="false">
        <appender-ref ref="CALCULATE_ASYNC"/>
        <appender-ref ref="Error"/>
    </logger>

    <logger name="com.facishare.paas.appframework.common.util.StopWatch" level="WARN" additivity="false">
        <appender-ref ref="PERF_ASYNC"/>
    </logger>

    <logger name="com.facishare.paas.expression.util.StopWatch" level="WARN" additivity="false">
        <appender-ref ref="PERF_ASYNC"/>
    </logger>

    <logger name="com.facishare.paas.metadata.util.MetadataStopWatch" level="WARN" additivity="false">
        <appender-ref ref="PERF_ASYNC"/>
    </logger>

    <logger name="com.fxiaoke.common.StopWatch" level="WARN" additivity="false">
        <appender-ref ref="PERF_ASYNC"/>
    </logger>

    <logger name="com.fxiaoke.dispatcher" level="INFO" additivity="false">
        <appender-ref ref="CALCULATE_ASYNC"/>
        <appender-ref ref="Error"/>
    </logger>

    <logger name="com.fxiaoke.bizconf.cache" level="WARN" additivity="false">
        <appender-ref ref="CALCULATE_ASYNC"/>
        <appender-ref ref="Error"/>
    </logger>

    <logger name="com.fxiaoke.notifier" level="ERROR" additivity="false">
        <appender-ref ref="CALCULATE_ASYNC"/>
        <appender-ref ref="Error"/>
    </logger>

    <logger name="com.fxiaoke.paas.auth" level="WARN" additivity="false">
        <appender-ref ref="CALCULATE_ASYNC"/>
        <appender-ref ref="Error"/>
    </logger>

    <logger name="com.fxiaoke.locator" level="WARN" additivity="false">
        <appender-ref ref="CALCULATE_ASYNC"/>
        <appender-ref ref="Error"/>
    </logger>

    <logger name="com.github.filter.reporter" level="WARN" additivity="false">
        <appender-ref ref="CALCULATE_ASYNC"/>
        <appender-ref ref="Error"/>
    </logger>

    <logger name="com.facishare.paas.task.calculate.monitor" level="info" additivity="false">
        <appender-ref ref="MONITOR_ASYNC"/>
        <appender-ref ref="Error"/>
    </logger>

    <logger name="com.facishare.paas.metadata.cache.DescribeCache" level="INFO" additivity="false">
        <appender-ref ref="CALCULATE_ASYNC"/>
        <appender-ref ref="Error"/>
    </logger>

    <root level="WARN">
        <appender-ref ref="CALCULATE_ASYNC"/>
    </root>

</configuration>
