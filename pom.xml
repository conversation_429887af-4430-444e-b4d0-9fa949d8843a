<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.fxiaoke.common</groupId>
        <artifactId>fxiaoke-parent-pom</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.facishare</groupId>
    <artifactId>fs-paas-calculate-task</artifactId>
    <packaging>pom</packaging>
    <version>9.6.5-SNAPSHOT</version>

    <modules>
        <module>fs-paas-calculate-task-web</module>
        <module>fs-paas-calculate-task-biz</module>
    </modules>

    <properties>
        <paas.app.metadata.version>9.6.5-SNAPSHOT</paas.app.metadata.version>
        <fs-paas-app-task.version>9.4.0-SNAPSHOT</fs-paas-app-task.version>
        <mongo-driver.version>3.10.2</mongo-driver.version>
        <dispatcher-support.version>7.0.2-SNAPSHOT</dispatcher-support.version>
        <paas-expression.version>8.8.5-SNAPSHOT</paas-expression.version>
        <mongo-spring-support.version>4.0.3-SNAPSHOT</mongo-spring-support.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>logging-interceptor</artifactId>
                <version>${okhttp3.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-calculate-task-biz</artifactId>
                <version>${project.version}</version>
<!--                <exclusions>-->
<!--                    <exclusion>-->
<!--                        <groupId>org.codehaus.groovy</groupId>-->
<!--                        <artifactId>groovy-all</artifactId>-->
<!--                    </exclusion>-->
<!--                </exclusions>-->
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-metadata</artifactId>
                <version>${paas.app.metadata.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-api</artifactId>
                <version>${paas.app.metadata.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-metadata-util</artifactId>
                <version>${paas.app.metadata.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-metadata-restdriver</artifactId>
                <version>${paas.app.metadata.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-task-async</artifactId>
                <version>${fs-paas-app-task.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-expression</artifactId>
                <version>${paas-expression.version}</version>
            </dependency>

            <dependency>
                <artifactId>i18n-util</artifactId>
                <groupId>com.facishare</groupId>
                <version>1.4-SNAPSHOT</version>
            </dependency>

            <!-- 支持错误日志抽样上报日志中心 -->
            <dependency>
                <groupId>com.fxiaoke.common</groupId>
                <artifactId>metrics-oss</artifactId>
                <version>${metrics-oss.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>biz-log-client</artifactId>
                <version>${biz-log-client.version}</version>
            </dependency>

            <dependency>
                <groupId>io.protostuff</groupId>
                <artifactId>protostuff-api</artifactId>
                <version>${protostuff.version}</version>
            </dependency>
            <dependency>
                <groupId>io.protostuff</groupId>
                <artifactId>protostuff-runtime</artifactId>
                <version>${protostuff.version}</version>
            </dependency>
            <dependency>
                <groupId>io.protostuff</groupId>
                <artifactId>protostuff-core</artifactId>
                <version>${protostuff.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-client</artifactId>
                <version>${curator.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>${curator.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>${curator.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongo-java-driver</artifactId>
                <version>${mongo-driver.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb.morphia</groupId>
                <artifactId>morphia</artifactId>
                <version>1.3.2</version>
            </dependency>

            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>2.0.0.Final</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
