<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.fxiaoke.common</groupId>
        <artifactId>fxiaoke-parent-pom</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <modules>
        <module>fs-webpage-customer-api</module>
        <module>fs-webpage-customer-provider</module>
        <module>fs-webpage-customer-core</module>
        <module>fs-webpage-customer-designer</module>
    </modules>

    <groupId>com.facishare</groupId>
    <artifactId>fs-webpage-customer</artifactId>
    <packaging>pom</packaging>
    <version>9.7.0-SNAPSHOT</version>

    <properties>
        <perf4j.version>0.9.16</perf4j.version>
        <fs-cep-spring-plugin.version>1.0.3-SNAPSHOT</fs-cep-spring-plugin.version>
        <fs-qixin-common-beans.version>1.0.2-SNAPSHOT</fs-qixin-common-beans.version>
        <fs-qixin-objgroup-manage-common.version>1.3.9-SNAPSHOT</fs-qixin-objgroup-manage-common.version>
        <fs-enterpriserelation-rest-api.version>2.1.1-SNAPSHOT</fs-enterpriserelation-rest-api.version>
        <powermock.version>2.0.9</powermock.version>
        <mockito.version>3.3.0</mockito.version>
        <fs-metadata-provider.version>9.5.0-SNAPSHOT</fs-metadata-provider.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.facishare.appserver</groupId>
                <artifactId>auditlog-util</artifactId>
                <version>1.8.2-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-webpage-customer-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-webpage-customer-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency> <!-- enables mocking of classes (in addition to interfaces) -->
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>1.10.15</version>
                <scope>test</scope>
            </dependency>
            <dependency> <!-- only required if Hamcrest matchers are used -->
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest-core</artifactId>
                <version>1.3</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy-agent</artifactId>
                <version>1.10.5</version>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-core</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4-rule</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-classloading-xstream</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <artifactId>xstream</artifactId>
                        <groupId>com.thoughtworks.xstream</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-metadata-api</artifactId>
                <version>${fs-metadata-provider.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- test begin -->
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>groovy-all</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-spring</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>groovy-all</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4-rule</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-stone-commons-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-classloading-xstream</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <artifactId>javassist-3.14.0-GA</artifactId>
            <groupId>org.ow2.util.bundles</groupId>
            <version>1.0.0</version>
            <scope>test</scope>
        </dependency>

        <dependency> <!-- enables mocking of classes (in addition to interfaces) -->
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency> <!-- only required if Hamcrest matchers are used -->
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-core</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- test end -->

    </dependencies>
</project>