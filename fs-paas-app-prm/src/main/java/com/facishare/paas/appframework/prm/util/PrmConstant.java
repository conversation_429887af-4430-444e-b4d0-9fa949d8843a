package com.facishare.paas.appframework.prm.util;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.fxiaoke.common.release.GrayRelease;
import com.fxiaoke.common.release.GrayReleaseBiz;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

public class PrmConstant {

    public static final String PRM_APP_ID = "prm";
    public static final String ENABLE_PARTNER_VIEW = "enable_partner_view";
    public static final String OWNER = "owner";
    public static final String ACCOUNT_ID = "account_id";
    public static final String FIELD_PARTNER_ID = "partner_id";
    public static final String OUT_TENANT_ID = "out_tenant_id";
    public static final String CONFIG_PARTNER_KEY = "config_partner_apiNames";
    public static final String SCHEDULE_OBJ = "ScheduleObj";


    public static List<String> menuApiNames;
    public static List<String> newMenuApiNames;
    public static List<String> unSupportLookupApiNames;
    public static List<String> unSupportLookupApiNamesForDetailPage;
    public static List<String> unSupportDetailApiNames;
    public static List<String> unSupportRelatedApiNames;
    public static List<String> predefineCustomObjects;
    public static Map<String, List<String>> supportBtns;
    public static List<String> unSupportIndexFields;
    public static List<String> supportPartnerRuleApiNames;
    public static List<String> supportMasterDataAppApiNames;
    public static List<String> supportEmployeeManyApiNames;
    public static List<String> supportButtonWhite;

    public static final Set<String> PARTNER_BUTTON_ACTION_LIST = Sets.newHashSet(ObjectAction.DELETE_PARTNER.getActionCode(),
            ObjectAction.CHANGE_PARTNER_OWNER.getActionCode(), ObjectAction.CHANGE_PARTNER.getActionCode());

    public static final List<String> PARTNER_BUTTON_NAME_SUFFIX_LIST = Lists.newArrayList("_ChangePartner_button_default"
            , "_DeletePartner_button_default", "_ChangePartnerOwner_button_default");

    public static List<String> unSupportOldObject = Lists.newArrayList();
    private static final GrayReleaseBiz scheduleBiz = GrayRelease.getInstance("new-schedule-obj");

    //开启支持互联日程
    private static final String SUPPORT_CROSS_SCHEDULE = "SUPPORT_CROSS_SCHEDULE";

    static {
        unSupportOldObject.add("ContactObj");
        unSupportOldObject.add("LeadsObj");
        unSupportOldObject.add("AccountObj");
        unSupportOldObject.add("SalesOrderObj");
        unSupportOldObject.add("OpportunityObj");
    }

    static {
        ConfigFactory.getInstance().getConfig("fs-crm-prm-config", config -> {
            menuApiNames = JSON.parseArray(config.get("menu_items"), String.class);
            supportEmployeeManyApiNames = JSON.parseArray(config.get("employee_many_objects"), String.class);
            newMenuApiNames = JSON.parseArray(config.get("new_menu_items"), String.class);
            unSupportLookupApiNames = JSON.parseArray(config.get("un_support_lookup_items"), String.class);
            unSupportLookupApiNamesForDetailPage = JSON.parseArray(config.get("un_support_lookup_items_for_detail_page"), String.class);
            unSupportDetailApiNames = JSON.parseArray(config.get("un_support_detail_items"), String.class);
            unSupportRelatedApiNames = JSON.parseArray(config.get("un_support_related_items"), String.class);
            predefineCustomObjects = JSON.parseArray(config.get("predefine_custom_objects"), String.class);
            supportBtns = JSON.parseObject(config.get("support_btns"), Map.class);
            unSupportIndexFields = JSON.parseArray(config.get("un_support_index_fields"), String.class);
            supportPartnerRuleApiNames = JSON.parseArray(config.get("support_partner_rule_obj"), String.class);
            supportMasterDataAppApiNames = JSON.parseArray(config.get("support_main_data_app_obj"), String.class);
            supportButtonWhite = JSON.parseArray(config.get("support_btns_white"), String.class);
        });
    }

    public static boolean isSupportEmployeeMany(String objectApiName, String tenantId) {
        if (CollectionUtils.empty(supportEmployeeManyApiNames) || StringUtils.isAnyBlank(objectApiName, tenantId)) {
            return false;
        }
        if (SCHEDULE_OBJ.equals(objectApiName) && !isAllowByBusiness(SUPPORT_CROSS_SCHEDULE, Integer.parseInt(tenantId))) {
            return false;
        }
        return supportEmployeeManyApiNames.contains(objectApiName);
    }

    public static boolean isAllowByBusiness(String business, int tenantId) {
        if (!scheduleBiz.isAllow("switch", "")) {
            return true;
        } else {
            return scheduleBiz.isAllow(business + "-enterprise", tenantId);
        }
    }
}
